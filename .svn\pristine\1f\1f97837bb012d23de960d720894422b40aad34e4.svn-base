<template>
  <div class="main_area">
    <div class="menu_area">
      <div v-for="(item, idx) in menuList" :key="'menu' + idx" class="main_menu" @click="goto(item)">
        <img :src="getHomeImg(item)" alt="">
      </div>
      <div class="main_menu" @click="goAgent">
        <img :src="home13" alt="">
      </div>
      <!-- <div
        style="width: 300px; height: 180px;margin-top: -20px;margin-left: 40px;margin-right: 20px;border-radius: 8px; cursor: pointer;position: relative;display: flex;flex-direction: column;align-items: center;justify-content: center;"
        @click="goAgent">
        <img :src="aiAgent" style="width: 100%; height: 100%;border-radius: 8px; position: absolute; top: 0; left: 0;">

        <Icon type="ios-chatbubbles" :size="60" color="white" style="z-index: 1;" />
        <div style="color: white; font-size: 30px;z-index: 1;margin-top: 20px;">AI-兴仔</div>
      </div> -->
    </div>
    <!-- <ChatBot /> -->
    <!-- Dify Chat 组件 -->
    <!-- <DifyChat ref="difyChat" :api-url="chatConfig.apiUrl" :api-key="chatConfig.apiKey" :user="currentUser" :postName="postName" /> -->
  </div>
</template>

<script>
import home1 from '@/assets/images/cgsjfx.png'
import home2 from '@/assets/images/cydsj.png'
import home3 from '@/assets/images/cbglpt.png'
import home4 from '@/assets/images/hcdt.png'
import home5 from '@/assets/images/qglctj.png'
import home6 from '@/assets/images/exam.png'
import home7 from '@/assets/images/jxsp.png'
import home8 from '@/assets/images/cbgl.png'
import home9 from '@/assets/images/jxtj.png'
import home10 from '@/assets/images/bi.png'
import home11 from '@/assets/images/cqzs.png'
import home12 from '@/assets/images/zncb.png'
import home13 from '@/assets/images/aixz.png'
import aiAgent from '@/assets/images/ai_agent.png'
import { loginGetToken } from '@/api/erpSys/login'
import DifyChat from '@/libs/dify-chat.js'

export default {
  name: 'home',
  components: {
    DifyChat
  },
  data() {
    return {
      home1,
      home2,
      home3,
      home4,
      home5,
      home6,
      home7,
      home8,
      home9,
      home10,
      home11,
      home12,
      home13,
      aiAgent,
      roleType: '',
      isLeader: 0,
      macUrl: process.env.VUE_APP_MAC,
      // 当前用户信息
      currentUser: 'user-1751250726641',

      // 聊天配置
      chatConfig: {
        apiUrl: 'http://************/v1',
        apiKey: 'app-ilgQfXl1nsyXd8AsoQL6H6cd' // 请替换为实际的API密钥
      },

      postName: JSON.parse(localStorage.getItem('userData')).post_name



    }
  },
  computed: {
    isLocalIp() {
      let usrIp = sessionStorage.getItem('ip')
      if (usrIp === '**************') return true
      return false
    }
    // menuList () {
    //   if (!localStorage.getItem('mainMenu')) return []
    //   let curUserAuth = JSON.parse(localStorage.getItem('mainMenu'))
    //   let list = curUserAuth.map(item => {
    //     return item.name
    //   })
    //   return list
    // }
  },
  methods: {
    goAgent() {
      this.$router.push({
        name: 'agent',
      })
    },
    // 路由跳转
    goto(item) {
      if (item.name === '船舶管理平台') { // 特殊处理瀛海登录
        this.erpLogin()
      } else if (item.name === '航次动态') { // 特殊处理航次动态登录
        this.hcLogin()
      } else if (item.name === '智能船舶') { // 特殊处理智能船舶跳转
        this.aiLogin()
      } else {
        if (item.href !== '') {
          window.open(item.href, '_blank')
        } else {
          let allMenuList = JSON.parse(localStorage.getItem('allMenuList'))
          allMenuList.forEach(list => {
            if (parseFloat(list.id) === parseFloat(item.menu_id)) {
              let homeRouter = {
                component: '',
                enabled: 1,
                href: '',
                icon: 'md-apps',
                menuDisplay: 1,
                mobileDisplay: 0,
                name: '首页',
                children: [{
                  href: 'home',
                  name: '首页',
                  enabled: 1,
                  menuDisplay: 1,
                  mobileDisplay: 0,
                  icon: 'md-apps',
                  component: () => import('@/view/single-page/home')
                }]
              }
              list.children.unshift(homeRouter)
              this.$store.commit('setMenuList', JSON.stringify(list.children))
              this.$nextTick(() => {
                if (list.children[1].component === 'externalLink' || (list.children[1].children.length > 0 && list.children[1].children[0].component) === 'externalLink') {
                  this.$router.push({
                    name: 'externalLink',
                    params: { url: list.children[1].href || list.children[1].children[0].href },
                    query: { url: list.children[1].href || list.children[1].children[0].href }
                  })
                } else {
                  this.$router.push({
                    name: list.children[1].href || list.children[1].children[0].href
                  })
                }
              })
            }
          })
        }
      }
    },
    getHomeImg(item) { // 在未引入动态背景图时临时用
      switch (item.name) {
        case '采购数据分析':
          return home1
        case '船员大数据':
          return home2
        case '船舶管理平台':
          return home3
        case '航次动态':
          return home4
        case '全国炼厂统计':
          return home5
        case '企业知识库':
          return home6
        case '绩效审批':
          return home7
        case '船舶管理':
          return home8
        case 'BI数据看板':
          return home10
        case '船期助手':
          return home11
        case '智能船舶':
          return home12
        default:
          return home1
      }
    },
    // erp系统
    erpLogin() {
      let _uuid = localStorage.getItem('uuid')
      window.open('http://erp.xtshipping.net/#/login?' + _uuid + '&resourceflag=1', '_blank')
    },
    // 航次动态
    hcLogin() {
      let _toUrl = 'http://hc.xtshipping.com/login?user=' + localStorage.getItem('hzxUser') + '&pwd=' + localStorage.getItem('hzxPwd')
      window.open(_toUrl, '_blank')
      // 测试
      // window.open('http://************:8078/login?user=15260223700&pwd=e535cf39cb2ce4d1307a4e767dcdda58ec7f053e8258ffff5db45097d155c339b3935ba333a780d2', '_blank')
    },
    aiLogin() { // 智能船舶跳转
      window.open('https://marine.apsatcom.com/#/login', '_blank')
    },
    // 全国炼厂
    qglcLogin() {
      window.open('http://************:8080', '_blank')
    }
  },
  created() {
    loginGetToken().then(res => {
      if (res.data.Code === 10000) {
        localStorage.setItem('access_token', res.data.access_token)
        var expireDate = res.data.expire_date.substring(0, 10).split('-')
        var accessTime = expireDate[1] + '-' + expireDate[2] + '-' + expireDate[0] + ' ' + res.data.expire_date.substring(10, 19)
        localStorage.setItem('access_token_time', accessTime)
      } else {
        // this.$Message.error(res.data.message)
      }
    })
    this.menuList = JSON.parse(localStorage.getItem('mainMenu'))
    if (!localStorage.getItem('userFlow')) return
    this.roleType = JSON.parse(localStorage.getItem('userFlow')).role_type
    if (!localStorage.getItem('leaderType')) return
    this.isLeader = JSON.parse(localStorage.getItem('leaderType'))
  }
}
</script>

<style lang="less">
.main_area {
  display: flex;
  width: 100%;
  height: 100%;
  background-image: url('../../../assets/images/login-bg.png');
  background-size: cover;
  background-position: bottom;
  position: relative;
  align-items: center;
  justify-content: center;
}

.main_menu {
  position: relative;
  text-align: center;
  cursor: pointer;
}

.menu_area {
  display: flex;
  // padding: 16% 0;
  flex-flow: wrap;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
}
</style>

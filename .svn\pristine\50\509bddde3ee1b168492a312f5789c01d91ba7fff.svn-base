import ierp_axios from '@/libs/ierp.api.request'
import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 苍穹接口中转 post
export function transferStationOut (data) {
  let curData = {
    paramMap: data.data,
    token: data.token,
    url: 'http://erp.xtshipping.com' + data.url // 正式线
    // url: 'http://ierptest.xtshipping.com:8023' + data.url // 测试线
  }
  return axios.request({
    url: '/kingDee/kd/requestTransferStation',
    method: 'post',
    headers: config.ajaxHeader,
    data: Qs.stringify(curData)
  })
}

export function requestTransferStationWithoutToken (data) {
  let curData = {
    paramMap: data.data,
    token: data.token,
    url: 'http://erp.xtshipping.com' + data.url // 正式线
    // url: 'http://ierptest.xtshipping.com:8023' + data.url // 测试线
  }
  return axios.request({
    url: '/kingDee/kd/requestTransferStationWithoutToken',
    method: 'post',
    headers: config.ajaxHeader,
    data: Qs.stringify(curData)
  })
}

// 苍穹接口中转 post
export function transferStation (data) {
  let curData = {
    paramMap: data.data,
    url: 'http://erp.xtshipping.com' + data.url // 正式线
    // url: 'http://ierptest.xtshipping.com:8023' + data.url // 测试线
  }
  return axios.request({
    url: '/kingDee/kd/requestTransferStation',
    method: 'post',
    headers: config.ajaxHeader,
    data: Qs.stringify(curData)
  })
}

// 获取计量单位 get
export function transferStationGet (data) {
  return ierp_axios.request({
    url: 'http://erp.xtshipping.com' + data.url, // 正式线
    // url: 'http://ierptest.xtshipping.com:8023' + data.url, // 测试线
    method: 'get',
    params: data
  })
}

// 物料、海图、油品、设备分类查询接口
export function getEquipList (data) {
  var statusVal = ''
  switch (data.status.length) {
    case 1:
      statusVal = 'status=' + data.status
      break;
    case 2:
      statusVal = 'status=' + data.status[0] + '&status=' + data.status[1]
      break;
    case 3:
      statusVal = 'status=' + data.status[0] + '&status=' + data.status[1] + '&status=' + data.status[2]
      break;
    default:
      break;
  }
  // delete data.status
  return ierp_axios.request({
    url: '/ierp/kapi/v2/xtgf/basedata/bd_materialgroup/query?' + statusVal,
    method: 'get',
    params: data
  })
}

// 获取计量单位
export function getUnitList (data) {
  return ierp_axios.request({
    url: '/ierp/kapi/v2/xtgf/base/bd_measureunits/meaSureSeach',
    method: 'get',
    params: data
  })
}

// 物料分类、备件分类新增
export function addEditMaterial (data) {
  let qsData = Qs.stringify(data)
  return ierp_axios.request({
    url: '/ierp/kapi/v2/xtgf/basedata/bd_materialgroup/saveownWL',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 物料分类、备件分类删除
export function delMaterialClassi (data) {
  let qsData = Qs.stringify(data)
  return ierp_axios.request({
    url: '/ierp/kapi/v2/xtgf/basedata/bd_materialgroup/deleteownWL',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  transferStation,
  transferStationOut,
  requestTransferStationWithoutToken,
  transferStationGet,
  getEquipList,
  getUnitList,
  addEditMaterial,
  delMaterialClassi
}

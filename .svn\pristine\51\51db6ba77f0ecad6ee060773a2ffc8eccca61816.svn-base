<template>
  <Modal v-model="modalData.modal" :title="modalData.title" width="850" @on-visible-change="modalShowHide" :mask-closable="false">
    <Form ref="formRef" :model="formData" :rules="ruleForm" :label-width="115" style="margin-bottom: 20px;">
      <Divider orientation="left">个人信息</Divider>
      <Row>
        <Col span="8">
          <FormItem label="姓名" prop="seafarer_name" :rules="{required: true, message: '此处不能为空', trigger: 'change'}">
            <Input v-if="modalData.type !== 'detail'" v-model="formData.seafarer_name">
              <Select filterable v-model="formData.seafarer_id" label-in-value slot="append" style="width: 80px" @on-select="shipperChange">
                <Option v-for="(item, idx) in shipperList" :key="'shipper' + idx" :value="item.seafarer_id">{{ item.seafarer_name }}</Option>
              </Select>
            </Input>
            <Input v-else type='text' v-model='formData.seafarer_name' readonly></Input>
          </FormItem>
        </Col>
        <Col span="8">
          <FormItem label="合同职务" prop="crt_duty_name" :rules="{required: true, message: '此处不能为空', trigger: 'change'}">
            <Select v-if="modalData.type !== 'detail'" v-model="formData.crt_duty_id" label-in-value filterable clearable @on-change="duty_select">
              <Option v-for="(item, idx) in modalData.dutyList" :key="'shipper' + idx" :value="item.value">{{ item.label }}</Option>
            </Select>
            <Input v-else type='text' v-model='formData.crt_duty_name' readonly></Input>
          </FormItem>
        </Col>
        <Col span="8">
          <FormItem label="身份证" prop="seafarer_id_no"> <!-- :rules="{required: true, message: '此处不能为空', trigger: 'change'}"-->
            <Input type='text' v-model='formData.seafarer_id_no' :readonly="modalData.type === 'detail'"></Input>
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span="8">
          <FormItem label="常用电话" prop="other_tel"> <!--:rules="{required: true, message: '此处不能为空', trigger: 'change'}"-->
            <Input type='text' v-model='formData.other_tel' :readonly="modalData.type === 'detail'"></Input>
          </FormItem>
        </Col>
        <Col span="8">
          <FormItem label="紧急联系人" prop="emergency_contact" :rules="{required: true, message: '此处不能为空', trigger: 'change'}">
            <Input type='text' v-model='formData.emergency_contact' :readonly="modalData.type === 'detail'"></Input>
          </FormItem>
        </Col>
        <Col span="8">
          <FormItem label="联系电话" prop="emergency_contact_tel"> <!-- :rules="{required: true, message: '此处不能为空', trigger: 'change'}"-->
            <Input type='text' v-model='formData.emergency_contact_tel' :readonly="modalData.type === 'detail'"></Input>
          </FormItem>
        </Col>
      </Row>
      <div>
        <FormItem label="居住地址" prop="current_address" :rules="{required: true, message: '此处不能为空', trigger: 'change'}">
          <Input type='text' v-model='formData.current_address' :readonly="modalData.type === 'detail'"></Input>
        </FormItem>
      </div>
      <Divider orientation="left">合同信息</Divider>
      <Row>
        <Col span="8">
          <FormItem label="合同编号" prop="contract_no" :rules="{required: true, message: '此处不能为空', trigger: 'change'}">
            <Input type='text' v-model='formData.contract_no' :readonly="modalData.type === 'detail'"></Input>
          </FormItem>
        </Col>
        <Col span="8">
          <FormItem label="合同类型" prop="contract_type" :rules="{required: true, message: '此处不能为空', trigger: 'change'}">
            <Select v-if="modalData.type !== 'detail'" v-model="formData.contract_type" filterable clearable>
              <Option v-for="item in contractTypeList" :key="'contract_type' + item.id" :value="item.id">{{ item.name }}</Option>
            </Select>
            <Input v-else type='text' v-model='contract_type_name' readonly></Input>
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span="8">
          <FormItem label="合同期限" prop="contract_period" :rules="{required: true, message: '此处不能为空', trigger: 'change'}">
            <Input type='text' v-model='formData.contract_period' :readonly="modalData.type === 'detail'">
              <span slot="append">年</span>
            </Input>
          </FormItem>
        </Col>
        <Col span="8">
          <FormItem label="合同开始" prop="contract_period_from"> <!-- :rules="{required: true, message: '此处不能为空', trigger: 'change'}"-->
            <DatePicker v-if="modalData.type !== 'detail'" type="date" format="yyyy-MM-dd" @on-change="data=>formData.contract_period_from=data" :value="formData.contract_period_from"></DatePicker>
            <Input v-else type='text' v-model='formData.contract_period_from' readonly></Input>
          </FormItem>
        </Col>
        <Col span="8">
          <FormItem label="合同结束" prop="contract_period_to"> <!-- :rules="{required: true, message: '此处不能为空', trigger: 'change'}"-->
            <DatePicker v-if="modalData.type !== 'detail'" type="date" format="yyyy-MM-dd" @on-change="data=>formData.contract_period_to=data" :value="formData.contract_period_to" :options="dateLimit"></DatePicker>
            <Input v-else type='text' v-model='formData.contract_period_to' readonly></Input>
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span="8">
          <FormItem label="试用期期限">
            <Input type='text' v-model='formData.probation_period' :readonly="modalData.type === 'detail'">
              <span slot="append">月</span>
            </Input>
          </FormItem>
        </Col>
        <Col span="8">
          <FormItem label="试用期开始">
            <!-- <DatePicker v-if="modalData.type !== 'detail'" type="date" :value="formData.probation_period_from"></DatePicker> -->
            <DatePicker v-if="modalData.type !== 'detail'" type="date" format="yyyy-MM-dd" @on-change="data=>formData.probation_period_from=data" :value="formData.probation_period_from"></DatePicker>
            <Input v-else type='text' v-model='formData.probation_period_from' readonly></Input>
          </FormItem>
        </Col>
        <Col span="8">
          <FormItem label="试用期结束">
            <DatePicker v-if="modalData.type !== 'detail'" type="date" format="yyyy-MM-dd" @on-change="data=>formData.probation_period_to=data" :value="formData.probation_period_to"></DatePicker>
            <Input v-else type='text' v-model='formData.probation_period_to' readonly></Input>
          </FormItem>
        </Col>
      </Row>
      <Divider orientation="left">待遇信息</Divider>
      <Row>
        <Col span="12">
          <FormItem label="税后基本工资" :label-width="180" prop="contract_basic_salary"> <!-- :rules="{required: true, message: '此处不能为空', trigger: 'change'}"-->
            <Input type='text' v-model='formData.contract_basic_salary' :readonly="modalData.type === 'detail'">
              <span slot="append">{{parseInt(formData.international_flag) === 0 ? '元' : '美元' }}</span>
            </Input>
          </FormItem>
        </Col>
        <Col span="12">
          <FormItem label="职务津贴、超时津贴、年休假金等" :label-width="300" prop="contract_merit_pay"> <!-- :rules="{required: true, message: '此处不能为空', trigger: 'change'}"-->
            <Input type='text' v-model='formData.contract_merit_pay' :readonly="modalData.type === 'detail'">
              <span slot="append">{{parseInt(formData.international_flag) === 0 ? '元' : '美元' }}</span>
            </Input>
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span="12">
          <FormItem label="试用期税后基本工资" :label-width="180" prop="probation_period_basic_salary">
            <Input type='text' v-model='formData.probation_period_basic_salary' :readonly="modalData.type === 'detail'">
              <span slot="append">{{parseInt(formData.international_flag) === 0 ? '元' : '美元' }}</span>
            </Input>
          </FormItem>
        </Col>
        <Col span="12">
          <FormItem label="试用期职务津贴、超时津贴、年休假金等" :label-width="300" prop="probation_period_merit_pay">
            <Input type='text' v-model='formData.probation_period_merit_pay' :readonly="modalData.type === 'detail'">
              <span slot="append">{{parseInt(formData.international_flag) === 0 ? '元' : '美元' }}</span>
            </Input>
          </FormItem>
        </Col>
      </Row>
    </Form>
    <div slot="footer"  class="demo-drawer-footer">
      <Button style="margin-right: 8px" @click="handleCancel">取消</Button>
      <Button v-if="modalData.type !== 'detail'" type="primary"  @click="handleSave">保存</Button>
    </div>
  </Modal>
</template>

<script>
import API from '@/api/shipperOwner/contract'
import { validateMobilePhone, validateIdCard, validatePhoneAndMobile, validateIntDecimal } from '@/libs/iViewValidate'

export default ({
  props: {
    modalData: Object
  },
  data () {
    return {
      shipperList: [], // 船员列表
      curShipper: {}, // 当前船员信息
      contractTypeList: [ // 合同类型数组 自定义
        { id: '0', name: '首次合同'},
        { id: '1', name: '续签合同'}
      ],
      dateLimit: {
        disabledDate (date) {
          return date && date.valueOf() < Date.now() - 86400000
        }
      },
      ruleForm: {
        seafarer_id_no: [
          { required: true, message: '此处不能为空', trigger: 'change' },
          { validator: validateIdCard, trigger: 'blur', message: '身份证格式出错' }
        ],
        other_tel: [
          { required: true, message: '此处不能为空', trigger: 'change' },
          { validator: validateMobilePhone, trigger: 'blur', message: '手机格式出错' }
        ],
        emergency_contact_tel: [
          { required: true, message: '此处不能为空', trigger: 'change' },
          { validator: validatePhoneAndMobile, trigger: 'blur', message: '联系电话格式出错' }
        ],
        contract_basic_salary: [
          { required: true, message: '此处不能为空', trigger: 'change' },
          { validator: validateIntDecimal, trigger: 'blur' }
        ],
        contract_merit_pay: [
          { required: true, message: '此处不能为空', trigger: 'change' },
          { validator: validateIntDecimal, trigger: 'blur' }
        ],
        probation_period_basic_salary: [
          // { validator: validateIntDecimal, trigger: 'blur' }
        ],
        probation_period_merit_pay: [
          // { validator: validateIntDecimal, trigger: 'blur' }
        ],
        contract_period_from: [
          { required: true, message: '此处不能为空', trigger: 'change' }
          // { validator: this.validateDateDifference, trigger: 'change' }
        ],
        contract_period_to: [
          { required: true, message: '此处不能为空', trigger: 'change' },
          { validator: this.validateDateDifference, trigger: 'change' }
        ]
      },
      contract_type_name: '', // 合同类型名称
      formData: {
        seafarer_id: '',
        seafarer_name: '',
        crt_duty_id: '',
        crt_duty_name: '',
        seafarer_id_no: '',
        other_tel: '',
        emergency_contact: '',
        emergency_contact_tel: '',
        current_address: '',
        contract_no: '',
        contract_type: '',
        contract_period: '',
        contract_period_from: '',
        contract_period_to: '',
        probation_period: '',
        probation_period_from: '',
        probation_period_to: '',
        after_pay: '',
        contract_basic_salary: '',
        contract_merit_pay: '',
        probation_after_pay: '',
        probation_period_basic_salary: '',
        probation_period_merit_pay: ''
      }
    }
  },
  methods: {
    handleSave () { // 保存
      console.log(this.formData)
      this.$refs['formRef'].validate((valid) => {
        if (valid) {
          let isOtherCheck = this.contractCheck() // 校验电话与合同期限问题
          if (!isOtherCheck) return
          if (this.modalData.type === 'add') { // 无引用新增
            API.addSeafarerContract(this.formData).then(res => {
              if (res.data.Code === 10000) {
                this.$Message.success(res.data.Message)
                this.$emit('addBack')
                this.handleCancel()
              } else {
                this.$Message.error(res.data.Message)
              }
            })
          }
          if (this.modalData.type === 'add_in') { // 当前船员引用新增
            delete this.formData.seafareseafarer_contract_idr_id
            API.addSeafarerContract(this.formData).then(res => {
              if (res.data.Code === 10000) {
                this.$Message.success(res.data.Message)
                this.$emit('addBack')
                this.handleCancel()
              } else {
                this.$Message.error(res.data.Message)
              }
            })
          }
          if (this.modalData.type === 'modify') {
            API.updateSeafarerContract(this.formData).then(res => {
              if (res.data.Code === 10000) {
                this.$Message.success(res.data.Message)
                this.$emit('addBack')
                this.handleCancel()
              } else {
                this.$Message.error(res.data.Message)
              }
            })
          }
        }
      })
    },
    validateDateDifference (rule, value, callback) { // 验证两个日期的年份差额
      const { contract_period, contract_period_from, contract_period_to } = this.formData
      if (contract_period === '/' || contract_period_from === '' || contract_period_to === '') {
        callback() // 如果标识字段是'/'，跳过验证
        return
      }
      const numericValue = parseInt(contract_period, 10)
      if (isNaN(numericValue)) {
        callback(new Error('标识字段必须是数字或“/”'))
        return
      }

      if (!contract_period_from || !contract_period_to) {
        callback(new Error('请先选择开始日期和结束日期'))
        return
      }

      const start = new Date(contract_period_from)
      const end = new Date(contract_period_to)
      // 计算年份差距
      const yearDifference = end.getFullYear() - start.getFullYear()
      // 检查结束日期是否比开始日期少一天
      const isExactEndDate = 
        end.getMonth() === start.getMonth() &&
        end.getDate() === start.getDate() - 1

      // 检查是否整年差距
      const expectedEndDate = new Date(start);
      expectedEndDate.setFullYear(expectedEndDate.getFullYear() + numericValue); // 加整数年
      expectedEndDate.setDate(expectedEndDate.getDate() - 1); // 减一天
      if(expectedEndDate.getTime() !== end.getTime()) {
        callback(new Error(`合同年份差距必须为${numericValue}年`))
        return
      }
      callback() // 验证通过
    },
    contractCheck () { // 校验电话是否一致 || 合同期限与起始结束日期是否一致
      let isCheck = true
      if (this.formData.other_tel === this.formData.emergency_contact_tel) {
        isCheck = false
        this.$Message.warning('常用电话与联系电话不能一致！')
      }
      return isCheck
    },
    handleCancel () { // 取消
      this.clearData()
      this.modalData.modal = false
      this.$refs['formRef'].resetFields()
    },
    duty_select (obj) {
      if (!this.modalData.modal) return
      this.formData.crt_duty_id = obj.value
      this.formData.crt_duty_name = obj.label
    },
    async getShiperList () { // 获取船员信息列表
      await API.querySeafarerInfoList().then(res => {
        if (res.data.Code === 10000) {
          this.shipperList = res.data.Result
        }
      })
    },
    shipperChange (obj) { // 船员选择触发
      const curShipperList = this.shipperList.filter(item => item.seafarer_id === obj.value)
      this.curShipper = curShipperList.length > 0 ? curShipperList[0] : {}
      this.formData.seafarer_name = this.curShipper.seafarer_name.replace(/\d+/g, '') // 移除名字中的身份证尾号
      this.formData.crt_duty_id = this.curShipper.crt_duty_id
      this.formData.crt_duty_name = this.curShipper.crt_duty_name
      this.formData.seafarer_id_no = this.curShipper.seafarer_id_no
      this.formData.other_tel = this.curShipper.mobile_phone
      this.formData.current_address = this.curShipper.census_register
    },
    clearData () {
      this.formData = {
        seafarer_id: '',
        seafarer_name: '',
        crt_duty_id: '',
        crt_duty_name: '',
        seafarer_id_no: '',
        other_tel: '',
        emergency_contact: '',
        emergency_contact_tel: '',
        current_address: '',
        contract_no: '',
        contract_type: '',
        contract_period: '',
        contract_period_from: '',
        contract_period_to: '',
        probation_period: '',
        probation_period_from: '',
        probation_period_to: '',
        after_pay: '',
        contract_basic_salary: '',
        contract_merit_pay: '',
        probation_after_pay: '',
        probation_period_basic_salary: '',
        probation_period_merit_pay: ''
      }
    },
    modalShowHide (val) {
      if (val) {
        this.getShiperList() // 获取船员信息列表
        if (this.modalData.type === 'detail' || this.modalData.type === 'add_in' || this.modalData.type === 'modify') {
          this.formData = {...{}, ...this.modalData.data}
          if (this.formData.contract_type !== '') {
            this.contract_type_name = this.formData.contract_type === '0' ? '首次合同' : this.formData.contract_type === '1' ? '续签合同' : ''
          }
        } else {
          this.formData.international_flag = this.modalData.flag
        }
      } else {
        this.clearData()
        this.$refs['formRef'].resetFields()
      }
    }
  }
})
</script>

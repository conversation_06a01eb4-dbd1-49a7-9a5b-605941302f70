const path = require('path')
const os = require('os')

const resolve = dir => {
  return path.join(__dirname, dir)
}

function getNetworkIp () {
  // 打开的 host
  let needHost = '' // 打开的host
  try {
    // 获得网络接口列表
    let network = os.networkInterfaces()
    for (let dev in network) {
      let iface = network[dev]
      for (let i = 0; i < iface.length; i++) {
        let alias = iface[i]
        if (alias.family === 'IPv4' && alias.address !== '127.0.0.1' && !alias.internal) {
          needHost = JSON.stringify(alias.mac)
        }
      }
    }
  } catch (e) {
    needHost = ''
  }
  return needHost
}

// 项目部署基础
// 默认情况下，我们假设你的应用将被部署在域的根目录下,
// 例如：https://www.my-app.com/
// 默认：'/'
// 如果您的应用程序部署在子路径中，则需要在这指定子路径
// 例如：https://www.foobar.com/my-app/
// 需要将它改为'/my-app/'
// iview-admin线上演示打包路径： https://file.iviewui.com/admin-dist/
const BASE_URL = process.env.NODE_ENV === 'production' ? '/' : '/'
const timeUpdate = new Date().getTime()

module.exports = {
  publicPath: BASE_URL,
  lintOnSave: false,
  configureWebpack: {
    output: {
      filename: `js/[name].js?v=${timeUpdate}`,
      chunkFilename: `js/[name].js?v=${timeUpdate}`
    }
  },
  chainWebpack: config => {
    // config.entry('main').add('babel-polyfill')
    config.plugin('define').tap((args) => {
      let ip = getNetworkIp()
      args[0]['process.env']['VUE_APP_MAC'] = ip
      return args
    })
    // 移除 prefetch 插件
    config.plugins.delete('prefetch')
    config.resolve.alias
      .set('@', resolve('src')) // key,value自行定义，比如.set('@@', resolve('src/components'))
      .set('_c', resolve('src/components'))
    config.module
      .rule('js')
      .test(/\.m?js$/)
      .include.add(resolve('src'), resolve('static'), resolve('node_modules/tree-table-vue/lib'))
      .end()
      .use('babel')
      .loader('babel-loader')
      .options({
        presets: ['@babel/preset-env']
      })
  },
  // 设为false打包时不生成.map文件
  productionSourceMap: false,
  // 这里写你调用接口的基础路径，来解决跨域，如果设置了代理，那你本地开发环境的axios的baseUrl要写为 '' ，即空字符串
  devServer: {
    proxy: {
      '/api': {
        target: 'http://erp.xtshipping.net',
        changeOrigin: true
      },
      '/ierp': {
        target: 'http://erp.xtshipping.com/', // 正式线
        // target: 'http://ierptest.xtshipping.com:8022', // 测试线
        changeOrigin: true
      },
      '/v1': {
        target: 'http://************', // 正式线
        // target: 'http://ierptest.xtshipping.com:8022', // 测试线
        changeOrigin: true
      }
    }
  }
}

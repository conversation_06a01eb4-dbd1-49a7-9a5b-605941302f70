import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 获取部门流程信息
export function queryPerfAllDeptFlowList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/perf/dept/flow/queryPerfAllDeptFlowList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取成员列表
export function queryUnifiedNodeAccountList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/org/unified/account/queryUnifiedNodeAccountList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 添加流程
export function addPerfDeptFlowAndDetails (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/perf/dept/flow/addPerfDeptFlowAndDetails',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 修改流程
export function updatePerfDeptFlowAndDetails (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/perf/dept/flow/updatePerfDeptFlowAndDetails',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 删除流程
export function delPerfDeptFlowAndDetails (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/perf/dept/flow/delPerfDeptFlowAndDetails',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 修改加减分说明
export function updatePerfDeptFlowRemark (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/perf/dept/flow/updatePerfDeptFlowRemark',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  queryPerfAllDeptFlowList,
  queryUnifiedNodeAccountList,
  addPerfDeptFlowAndDetails,
  updatePerfDeptFlowAndDetails,
  delPerfDeptFlowAndDetails,
  updatePerfDeptFlowRemark
}

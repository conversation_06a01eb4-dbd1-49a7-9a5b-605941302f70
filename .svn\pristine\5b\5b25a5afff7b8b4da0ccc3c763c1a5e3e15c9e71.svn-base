import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 船舶列表、职务列表、证书等级
export function querySeafarerBaseInfo (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/seafarer/schedule/querySeafarerBaseInfo',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询船员证书到期列表 240天
export function querySeafarerCertificateInfo (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/seafarer/schedule/querySeafarerCertificateInfo',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 船员排期信息
export function querySeafarerInfoList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/seafarer/schedule/querySeafarerInfoList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 所有船员排期信息
export function querySeafarerInfoMatchAllList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/seafarer/schedule/querySeafarerInfoMatchAllList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  querySeafarerBaseInfo,
  querySeafarerCertificateInfo,
  querySeafarerInfoList,
  querySeafarerInfoMatchAllList
}
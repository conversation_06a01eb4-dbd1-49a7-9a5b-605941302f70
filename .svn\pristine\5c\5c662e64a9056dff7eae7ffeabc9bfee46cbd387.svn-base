<template>
  <Submenu :name="`${parentName}`">
    <template slot="title">
      <common-icon :class="(parentItem.meta && parentItem.meta.isCustom) ? 'iconfont' : ''" :type="parentItem.icon || ''"/>
      <span>{{ showTitle(parentItem) }}</span>
    </template>
    <template v-for="item in children">
      <template v-if="item.children && item.children.length === 1">
        <side-menu-item  v-if="showChildren(item)" :key="`menu-${item.name}`" :parent-item="item"></side-menu-item>
        <menu-item v-else :name="getNameOrHref(item, true)" :key="`menu-${item.children[0].name}`"><common-icon :class="(item.meta && item.meta.isCustom) ? 'iconfont' : ''" :type="item.children[0].icon || ''"/><span>{{ showTitle(item.children[0]) }}</span></menu-item>
      </template>
      <template v-else>
        <span v-if="item.menuDisplay === 1">
          <side-menu-item v-if="showChildren(item)" :key="`menu-${item.name}`" :parent-item="item"></side-menu-item>
          <menu-item :style="$store.state.user.access === 'erpSys' ? 'background: #001529' : ''" v-else :name="getNameOrHref(item)" :key="`menu-else-${item.name}`"><common-icon :class="(item.meta && item.meta.isCustom) ? 'iconfont' : ''" :type="item.icon || ''"/><span>{{ showTitle(item) }}</span></menu-item>
        </span>
      </template>
    </template>
  </Submenu>
</template>
<script>
import mixin from './mixin'
import itemMixin from './item-mixin'
export default {
  name: 'SideMenuItem',
  mixins: [ mixin, itemMixin ]
}
</script>
<style lang="less" scoped>
.iconfont {
  font-size: 26px;
  margin: 0 4px 0 -5px;
}
.ivu-menu-submenu > .ivu-menu > .ivu-menu-item > i.iconfont {
  margin: 0 4px 0 -5px !important;
}
</style>

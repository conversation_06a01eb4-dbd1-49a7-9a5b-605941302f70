import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 月度计划列表
export function queryGroupVmpPlanList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/vmp/plan/queryGroupVmpPlanList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 月度计划详情
export function queryVmpPlanDetail (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/vmp/plan/queryVmpPlanDetail',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 月度计划添加
export function addVmpPlan (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/vmp/plan/addVmpPlan',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 月度计划修改
export function updateVmpPlan (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/vmp/plan/updateVmpPlan',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 月度计划删除
export function delVmpPlan (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/vmp/plan/delVmpPlan',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 月度计划删除
export function delBatchVmpPlan (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/vmp/plan/delBatchVmpPlan',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 月度计划已读配置
export function addVmpUserRead (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/vmp/user/read/addVmpUserRead',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 推送到云之家
export function sendTipsToYunZhiJia (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/vmp/send/config/sendTipsToYunZhiJia',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  queryGroupVmpPlanList,
  queryVmpPlanDetail,
  addVmpPlan,
  updateVmpPlan,
  delVmpPlan,
  delBatchVmpPlan,
  addVmpUserRead,
  sendTipsToYunZhiJia
}
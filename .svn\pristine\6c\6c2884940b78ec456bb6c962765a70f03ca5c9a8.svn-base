.main{
  .logo-con{
    display: flex;
    height: 40px;
    padding: 18px;
    z-index: 999;
    top: 0;
    left: 0;
    margin-bottom: 32px;
    position: relative;
    // -webkit-box-shadow: 0 0 11px 3px rgba(0,0,0,0.11);
    // -moz-box-shadow: 0 0 11px 3px rgba(0,0,0,0.11);
    // box-shadow: 0 0 11px 3px rgba(0,0,0,0.11);
    // float: left;
    &::after {
      content: '';
      width: 0;
      opacity: 1;
      height: 40px;
      left: 188px;
      top: 0;
      position: fixed;
      -webkit-box-shadow: 0 0 11px 2px rgba(0, 0, 0, 0.3);
      -moz-box-shadow: 0 0 11px 2px rgba(0, 0, 0, 0.3);
      box-shadow: 0 0 11px 2px rgba(0, 0, 0, 0.3);
      -webkit-transition: all 0.2s ease-in-out;
      transition: all 0.2s ease-in-out;
    }
    span {
      font-size: 20px;
      font-weight: bold;
      color: #fff;
      margin-left: 16px;
    }
    img{
      height: 30px;
      width: auto;
      display: block;
    }
  }
  .ivu-layout-sider-collapsed {
    .logo-con::after {
      opacity: 0
    }
  }
  .ivu-layout {
    .ivu-layout-header {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 999;
      height: 40px;
      line-height: 40px;
    }
  }
  .header-logo {
    position: absolute;
    left: 32px;
    display: flex;
    align-items: center;
    img {
      width: 30px;
      height: 30px;
    }
    span {
      font-size: 18px;
      font-weight: bold;
      color: #002070;
      margin-left: 16px;
    }
  }
  .header-con{
    background: #fff;
    padding: 0 6px;
    width: 100%;
    .header-search {
      width: auto;
      line-height: 40px !important;
      margin-right: 30px;
      .ivu-input {
        border-radius: 15px;
      }
      .ivu-input-search-icon {
        margin-top: 4px;
      }
    }
  }
  .main-layout-con{
    height: 100%;
    overflow: hidden;
    background: #F5F7FA;
  }
  .main-content-con{
    // height: ~"calc(100% - 60px)";
    overflow: hidden;
    width: 100%;
    margin-top: 40px;
  }
  .main-content-con-full {
    height: 100%;
    position: relative;
    overflow: hidden;
    width: 100%;
    margin-top: 0;
  }
  .tag-nav-wrapper{
    padding: 0;
    height:40px;
    background:#F0F0F0;
  }
  .content-wrapper{
    padding: 18px;
    height: ~"calc(100% - 80px)";
    overflow: auto;
    // background: linear-gradient(to bottom, #145ae8 0%, #b4d2ff 20%, #b9e1f5 30%, #f7f7f7 100%);
  }
  .content-home {
    padding: 0;
    height: ~"calc(100% - 80px)";
    overflow: auto;
  }
  .left-sider{
    // background: #343848;
    background-image: url('../../assets/images/sider-bg.png') !important;
    background-repeat: no-repeat;
    background-size: cover;
    z-index: 1090;
    .ivu-layout-sider-children{
      overflow-y: auto;
      // margin-right: -18px;
    }
  }
  .erpSys-left-sider {
    background-color: rgb(0, 12, 23);
    z-index: 9999;
  }
}
.change_info_and_login {
  padding: 0 !important;
  div {
    padding: 7px 16px;
  }
}
.ivu-menu-item > i{
  margin-right: 12px !important;
}
.ivu-menu-submenu > .ivu-menu > .ivu-menu-item > i {
  margin-right: 8px !important;
}
.collased-menu-dropdown{
  width: 100%;
  margin: 0;
  line-height: normal;
  padding: 7px 0 6px 16px;
  clear: both;
  font-size: 12px !important;
  white-space: nowrap;
  list-style: none;
  cursor: pointer;
  transition: background 0.2s ease-in-out;
  &:hover{
    background: rgba(100, 100, 100, 0.1);
  }
  & * {
    color: #515a6e;
  }
  .ivu-menu-item > i{
    margin-right: 12px !important;
  }
  .ivu-menu-submenu > .ivu-menu > .ivu-menu-item > i {
    margin-right: 8px !important;
  }
}

.ivu-select-dropdown.ivu-dropdown-transfer{
  max-height: 400px;
}
// .cmenu {
//   background: #21242d !important;
//   color: #fff !important;
// }
// .cmenu .ivu-dropdown-item {
//   color: #fff !important;
// }
// .cmenu .ivu-dropdown-item:hover {
//   background: none !important;
//   color: #5582F3 !important;
// }

.topdate {
  top: 3px;
  z-index: 999;
  margin-left: 35px;
  position: absolute;
}
.org_title {
  margin-top: -12px;
  margin-left: 5px;
  font-size: 10px;
  transform: scale(0.8333);
  transform-origin: 0 0;
}
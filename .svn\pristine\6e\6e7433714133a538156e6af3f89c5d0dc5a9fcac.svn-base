<template>
  <Modal v-model="modalData.modal" :title="modalData.title" :mask-closable="false" width="500" @on-visible-change="modalHideShow">
    <Form ref="formInline" :model="formData" inline :label-width="70">
      <Form-item prop="open_id" label="人员" :rules="{required: true, message: '此处不能为空', trigger: 'change', type: 'array'}">
        <Select filterable v-model="formData.open_id" multiple style="width: 360px;">
          <Option v-for="(item, idx) in userList" :key="'user' + idx" :value="item.open_id">{{ item.user_name }}</Option>
        </Select>
      </Form-item>
      <Form-item prop="ship_id" label="船舶" :rules="{required: true, message: '此处不能为空', trigger: 'change', type: 'array'}">
        <Select filterable v-model="formData.ship_id" multiple style="width: 360px;">
          <Option v-for="(item, idx) in shipList" :key="'ship' + idx" :value="item.ship_id">{{ item.ship_name }}</Option>
        </Select>
      </Form-item>
      <Form-item prop="send_content" label="推送内容">
        <Input type="textarea" v-model="formData.send_content" :rows="2" placeholder="请输入推送内容" style="width: 360px;"></Input>
      </Form-item>
    </Form>
    <div slot="footer">
      <Button @click="handleCancel">取消</Button>
      <Button @click="handleSave" type="primary">保存</Button>
    </div>
  </Modal>
</template>
<script>
import basicAPI from '@/api/basicData'
import API from '@/api/voyagePlanSet'

export default {
  props: {
    modalData: Object
  },
  data () {
    return {
      formData: {
        open_id: '',
        ship_id: '',
        send_content: ''
      },
      userList: [], // 人员列表
      shipList: [] // 船舶列表
    }
  },
  methods: {
    getBasicList () { // 获取基础数据
      basicAPI.queryBasicShipList({ business_model_in: '1,2,3' }).then(res => {
        if (res.data.Code === 10000) {
          this.shipList = res.data.Result
        }
      })
      basicAPI.queryUnifiedAccountList().then(res => {
        if (res.data.Code === 10000) {
          this.userList = res.data.Result
        }
      })
    },
    handleSave () { // 保存
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          let _param = {
            open_ids: this.formData.open_id.join(),
            ship_ids: this.formData.ship_id.join(),
            send_content: this.formData.send_content
          }
          if (this.modalData.modalType === 'modify') { // 修改接口
            Object.assign(_param, {
              vmp_send_config_id: this.modalData.data.vmp_send_config_id
            })
            API.updateVmpSendConfig(_param).then(res => {
              if (res.data.Code === 10000) {
                this.$Message.success(res.data.Message)
                this.$emit('dataBack')
                this.modalData.modal = false
                this.clearData()
              } else {
                this.$Message.error(res.data.Message)
              }
            })
          } else { // 新增接口
            API.addVmpSendConfig(_param).then(res => {
              if (res.data.Code === 10000) {
                this.$Message.success(res.data.Message)
                this.$emit("dataBack")
                this.modalData.modal = false
                this.clearData()
              } else {
                this.$Message.error(res.data.Message)
              }
            })
          }
        }
      })
    },
    handleCancel () { // 取消
      this.modalData.modal = false
      this.clearData()
    },
    clearData () {
      this.formData = {
        open_id: '',
        ship_id: '',
        send_content: ''
      }
      this.$refs['formInline'].resetFields()
    },
    modalHideShow (val) {
      if (val) {
        this.getBasicList()
        if (this.modalData.modalType === 'modify') {
          this.formData.open_id = this.modalData.data.open_ids.split(',')
          this.formData.ship_id = this.modalData.data.ship_ids.split(',')
          this.formData.send_content = this.modalData.data.send_content
        }
      }
    }
  }
}
</script>
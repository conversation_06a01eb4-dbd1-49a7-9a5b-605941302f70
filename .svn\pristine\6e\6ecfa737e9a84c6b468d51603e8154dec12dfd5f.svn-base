import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 区域配置列表 无分页
export function queryAreaList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/unified/area/queryUnifiedAreaList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 新增
export function addArea (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/unified/area/addUnifiedArea',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 编辑
export function updateArea (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/unified/area/updateUnifiedArea',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

<template>
  <Modal v-model="modalData.modal" :title="modalData.title" width="350" :mask-closable="false" @on-visible-change="modalShowHide">
    <Form ref="formValidate" :model="formValidate" :label-width="105">
      <FormItem label="文件名称" prop="name"  :rules="{required: true, message: '此处不能为空', trigger: 'change'}">
        <Input v-model="formValidate.name"></Input>
      </FormItem>
      <FormItem v-if="fileType !== 'directory'" label="到期时间" prop="end_time" :rules="{required: true, message: '此处不能为空', trigger: 'change'}">
        <DatePicker type="date" format="yyyy-MM-dd" @on-change="data=>formValidate.end_time=data" :value="formValidate.end_time" :editable="false"></DatePicker>
      </FormItem>
      <!-- <FormItem v-if="fileType !== 'directory'" label="附件" prop="file_url" >
        <fileUpload ref="fileUploadComponent" :fileDataList="fileDataList" @getFileId="getFileId"></fileUpload>
      </FormItem> -->
    </Form>
    <div slot="footer"  class="demo-drawer-footer">
      <Button style="margin-right: 8px" @click="modalData.modal = false">取消</Button>
      <Button type="primary"  @click="handleSave">保存</Button>
    </div>
  </Modal>
</template>
<script>
import API from '@/api/erpSys/certificate'
// import fileUpload from './fileUpload'

export default ({
  props: {
    modalData: Object
  },
  // components: {
  //   fileUpload
  // },
  data () {
    return {
      fileType: '',
      fileDataList: [],
      formValidate: {
        name: '',
        end_time: '',
        file_url: ''
      }
    }
  },
  methods: {
    handleSave () {
      if (this.fileType === 'directory') {
        let _param = {
          file_directory_id: this.modalData.data.file_directory_id,
          name: this.formValidate.name
        }
        API.updateFileDirectory(_param).then(res => {
          if (res.data.Code === 10000) {
            this.$emit('updateBack')
            this.$Message.success(res.data.Message)
            this.clearData()
          } else {
            this.$Message.error(res.data.Message)
          }
        })
      }
    },
    clearData () {
      this.modalData.modal = false
      this.formValidate = {
        name: '',
        end_time: '',
        file_url: ''
      }
    },
    getFileId () {
      // this.wps_ids = this.fileDataList.map(item => {
      //   return item.id
      // }).join()
    },
    modalShowHide (val) {
      if (val) {
        this.formValidate = this.modalData.data
        this.fileType = this.modalData.data.type
      }
    }
  }
})
</script>

<template>
  <div>
    <Card class="compare_analysis">
      <search @searchResults='searchResults' :setSearch='setSearchData' @resetResults='resetResults'></search>
      <div style="margin-top: 10px;">
        <label>时间：</label>
        <div class="date_selec">
          <DatePicker v-model="dateValue" type="year" confirm :editable="false" @on-change="changeDate" @on-clear="clearRightDate('dateValue')" :options="options1" style="width: 200px;z-index:100;"></DatePicker>
          <span>{{ timeValue }}</span>
        </div>
        <label> &nbsp;&nbsp; 对比时间：</label>
        <div class="date_selec">
          <DatePicker v-model="compareValue" type="year" multiple :options="options2" confirm @on-change="changeCompareDate" @on-clear="clearRightDate('compareValue')" style="width: 440px"></DatePicker>
          <span>{{ selectedDates }}</span>
        </div>
      </div>
    </Card>
    <Card style="margin-top: 10px">
      <ChartBarVertical v-if="isBarShow" :style="{height: `${barHeight}px`}" :value="barData" :compareAnalysis="true" text="采购总量" unit="元"></ChartBarVertical>
      <Button type="primary" @click="exportData" style="float: right;margin-bottom: 15px">数据导出 <Icon type="ios-download-outline" size="18"></Icon></Button>
      <Table border :loading="loading" :columns="columns" :data="list" class="data_list"></Table>
    </Card>
  </div>
</template>
<script>
import search from '_c/search' // 查询组件
import { ChartBarVertical } from '_c/charts'
import API from '@/api/basicData'
import { getMaterialCompare, materialCompareExport } from '@/api/materialPurchaseSystem/compareAnalysis'

export default {
  components: {
    search,
    ChartBarVertical
  },
  data () {
    return {
      isBarShow: false,
      barHeight: 300,
      clickType: 'left',
      timeValue: '',
      selectedDates: '',
      dateValue: '',
      compareValue: [],
      barData: {
        xAxis: [],
        data: []
      },
      queryParam: {
        usefor: '',
        shipname_in: '',
        vendorname: '',
        compareTimeArray: [{}]
      },
      newCompareTimeArray: [], // 存储原compareTimeArray数组
      loading: false,
      list: [],
      options1: {},
      options2: {},
      columns: [],
      // columns: [{
      //     title: '用途',
      //     key: 'usefor',
      //     align: 'center',
      //     fixed: 'left',
      //     minWidth: 120
      // }],
      setSearchData: {
        usefor: {
          type: 'selectMultiple',
          label: '种类',
          selectData: [],
          selected: [],
          placeholder: '请选择',
          selectName: '',
          value: '',
          filterable: true
        },
        shipname_in: {
          type: 'selectMultiple',
          label: '船舶',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          value: '',
          filterable: true
        },
        vendorname: {
          type: 'select',
          label: '供应商',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 180,
          value: '',
          filterable: true
        }
      },
      leftListVal: '',
      rightListVal: [],
      curYearValue: '', // 存储当前默认年份
      curYearCompareValue: '' // 存储当前默认对比年份
    }
  },
  methods: {
    getList () {
      this.loading = true
      this.barData.xAxis = []
      this.barData.data = []
      this.list = []
      this.columns = [] // [this.columns[0]]
      this.isBarShow = false
      this.newCompareTimeArray = this.queryParam.compareTimeArray
      this.queryParam.compareTimeArray = typeof this.queryParam.compareTimeArray === 'string' ? this.queryParam.compareTimeArray : JSON.stringify(this.queryParam.compareTimeArray)
      getMaterialCompare(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          this.loading = false
          let arrVal = JSON.parse(this.queryParam.compareTimeArray)
          // 时间、对比时间点击事件
          let curTitle = ''
          this.columns.push({
            title: '用途',
            key: 'title',
            width: 145,
            align: 'center',
            fixed: 'left'
          })
          arrVal.map((item, idx) => {
            if (idx === 0) {
              this.leftListVal = this.timeValue ? this.timeValue : item.date_month_st
              curTitle = this.leftListVal
              this.list[idx] = {
                title: curTitle
              }
            } else {
              this.rightListVal = []
              if (this.selectedDates) { // 点击左侧快捷按钮
                let leftArr = this.selectedDates.split(',')
                leftArr.map(item => {
                  this.rightListVal.push(item)
                })
                curTitle = leftArr[idx - 1]
              } else { // 点击右侧月份赋值
                if (this.compareValue.length > 0) {
                  this.compareValue.map(item => {
                    this.rightListVal.push(item)
                  })
                }
                curTitle = item.date_month_st
              }
              this.list.push({
                title: curTitle
              }, {
                title: '增长值'
              }, {
                title: '增长率'
              })
            }
            // this.columns = [...this.columns, this.columns[idx + 1] = {
            //   title: curTitle,
            //   key: 'cost_sum' + idx,
            //   align: 'center',
            //   minWidth: 150,
            //   render: (h, params) => {
            //     let curIcon = params.row['growth_amount' + idx] < 0 ? '↓' : '↑'
            //     let val = '(' + curIcon + params.row['growth_amount' + idx] + '元 ' + params.row['growth_rate' + idx] + '%)'
            //     return h('div',[
            //       h('div', {}, params.row['cost_sum' + idx]),
            //       h('div', {
            //         style: {
            //           display: idx === 0 ? 'none' : '',
            //           color: params.row['growth_amount' + idx] < 0 ? 'green' : 'red'
            //         }
            //       }, val)
            //     ])
            //   }
            // }]
          })
          let curgrow = []
          if (this.selectedDates && typeof this.selectedDates === 'string') {
            var newArr = []
            this.selectedDates.split(',').forEach(item => newArr.push({ name: item }))
          }
          if(!res.data.Result) return
          res.data.Result.map((e, index) => {
            this.barData.data[index] = { name: '',
              type: 'bar',
              data: [],
              label: { show: true,
                position: 'right',
                textBorderColor: 'none',
                formatter: item => {
                  let seriesLength = this.barData.data[0].data.length
                  let curgrowIdx = curgrow.length / seriesLength
                  let labVal = []
                  let _color = ''
                  for (let i = 0; i < curgrowIdx; i++) {
                    labVal[i] = []
                    for (let j = 0; j < seriesLength; j++) {
                      labVal[i].push(curgrow[i * seriesLength + j])
                    }
                  }
                  if (parseInt(e[item.dataIndex].growth_amount) >= 0) {
                    _color = '{up|' + labVal[item.seriesIndex - 1][item.dataIndex] + '}'
                  } else {
                    _color = '{down|' + labVal[item.seriesIndex - 1][item.dataIndex] + '}'
                  }
                  return _color
                },
                rich: {
                  up: {
                    color: 'red'
                  },
                  down: {
                    color: 'green'
                  }
                }
              } }
            let classObj = {}
            e.map((item, idx) => {
              this.barData.data[index].data.push(item.cost_sum)
              if (index === 0) {
                this.barData.xAxis.push(item.usefor)
                this.barData.data[0].name = this.timeValue ? this.timeValue : this.newCompareTimeArray[0].date_month_st
                this.barData.data[0].label.formatter = ''
                // this.list.push({ usefor: item.usefor })
                this.columns.push({
                  title: item.usefor,
                  key: 'value' + idx,
                  align: 'center',
                  minWidth: 135
                })
                Object.assign(this.list[index], {
                  ['value' + idx]: item.cost_sum,
                  sumary: res.data.Period[0].period_cost_sum
                })
              } else {
                if (item.growth_amount > 0) {
                  curgrow.push('↑' + item.growth_amount + '元 ' + item.growth_rate + '%')
                } else if (item.growth_amount < 0) {
                  curgrow.push('↓' + item.growth_amount + '元 ' + item.growth_rate + '%')
                } else {
                  curgrow.push(item.growth_amount + '元 ' + item.growth_rate + '%')
                }
                let _index = (index - 1) * 3
                Object.assign(classObj, {
                  ['value' + idx]: item.growth_amount < 0 ? 'downClass' : 'upClass',
                  sumary: res.data.Period[index].period_growth_amount < 0 ? 'downClass' : 'upClass'
                })
                Object.assign(this.list[_index + 1], {
                  ['value' + idx]: item.cost_sum,
                  sumary: res.data.Period[index].period_cost_sum
                })
                Object.assign(this.list[_index + 2], {
                  ['value' + idx]: item.growth_amount < 0 ? '↓' + item.growth_amount : '↑' + item.growth_amount,
                  sumary: res.data.Period[index].period_growth_amount < 0 ? '↓' + res.data.Period[index].period_growth_amount : '↑' + res.data.Period[index].period_growth_amount,
                  cellClassName: classObj
                })
                Object.assign(this.list[_index + 3], {
                  ['value' + idx]: item.growth_rate < 0 ? '↓' + item.growth_rate : '↑' + item.growth_rate,
                  sumary: res.data.Period[index].period_rate < 0 ? '↓' + res.data.Period[index].period_rate : '↑' + res.data.Period[index].period_rate,
                  cellClassName: classObj
                })
                this.barData.data[index].name = this.selectedDates ? newArr[index - 1].name : this.newCompareTimeArray[index].date_month_st
              }
              // Object.assign(this.list[idx], {
              //   ['cost_sum' + index]: item.cost_sum,
              //   ['growth_amount' + index]: item.growth_amount,
              //   ['growth_rate' + index]: item.growth_rate
              // })
            })
          })
          // this.list[this.list.length] = {
          //   usefor: '总计'
          // }
          // res.data.Period.map((item, idx) => {
          //   Object.assign(this.list[this.list.length - 1], {
          //     ['cost_sum' + idx]: item.period_cost_sum,
          //     ['growth_amount' + idx]: item.period_growth_amount,
          //     ['growth_rate' + idx]: item.period_rate
          //   })
          // })
          this.barHeight = this.barData.data.length > 1 && this.barData.data[0].data.length > 2 ? this.barData.data.length * 35 * this.barData.data[0].data.length : this.barData.data.length * 100
          this.isBarShow = true
          this.columns.push({
            align: 'center',
            key: 'sumary',
            title: '总计',
            width: 135,
            fixed: 'right'
          })
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 查询
    searchResults (e) {
      this.queryParam.shipname_in = e.shipname_in.toString()
      this.queryParam.vendorname = e.vendorname
      this.queryParam.usefor = e.usefor.toString()
      if (this.queryParam.compareTimeArray[0].date_month_et === '' || this.curYearValue === '') {
        return this.$Message.error('时间不能为空！')
      } else if (this.queryParam.compareTimeArray.length < 2 || this.curYearCompareValue === '') {
        return this.$Message.error('对比时间不能为空！')
      }
      delete e.target
      this.getList()
    },
    // 选择时间右侧月份触发
    changeDate (date) {
      if (date) {
        this.dateValue = ''
        this.curYearValue = date
        // this.timeValue = ''
        // this.queryParam.compareTimeArray = typeof this.queryParam.compareTimeArray === 'string' ? JSON.parse(this.queryParam.compareTimeArray) : this.queryParam.compareTimeArray
        // this.queryParam.compareTimeArray[0] = {
        //   date_month_st: date,
        //   date_month_et: date,
        //   date_name: date
        // }
      }
    },
    // 选择对比时间右侧月份触发
    changeCompareDate (date) {
      if (date !== '') {
        this.compareValue = ''
        this.curYearCompareValue = date
      }
      // this.queryParam.compareTimeArray = typeof this.queryParam.compareTimeArray === 'string' ? JSON.parse(this.queryParam.compareTimeArray) : this.queryParam.compareTimeArray
      // if (date !== '') {
      //   this.selectedDates = ''
      //   date = date.split(',')
      //   this.compareValue = date
      //   this.clickType = 'right'
      //   this.queryParam.compareTimeArray.splice(1)
      //   date.forEach((e, idx) => {
      //     this.queryParam.compareTimeArray[idx + 1] = {
      //       date_month_st: e,
      //       date_month_et: e,
      //       date_name: e
      //     }
      //   })
      // } else {
      //   this.queryParam.compareTimeArray.length === 1 ? '' : this.queryParam.compareTimeArray.splice(1)
      // }
    },
    checkClickType (str) { // str 自定义左右侧
      if (this.clickType !== str) {
        this.queryParam.compareTimeArray.splice(1)
      }
    },
    getOptions () {
      let _that = this
      _that.options1 = {
        shortcuts: [
          {
            text: '第一季度',
            value () {
              // _that.dateValue = ''
              let curYear = _that.curYearValue
              _that.queryParam.compareTimeArray = typeof _that.queryParam.compareTimeArray === 'string' ? JSON.parse(_that.queryParam.compareTimeArray) : _that.queryParam.compareTimeArray
              _that.queryParam.compareTimeArray[0] = {
                date_month_st: curYear + '-01',
                date_month_et: curYear + '-03',
                date_name: curYear + '第一季度'
              }
              _that.timeValue = curYear + '第一季度'
            }
          },
          {
            text: '第二季度',
            value () {
              // _that.dateValue = ''
              let curYear = _that.curYearValue
              _that.queryParam.compareTimeArray = typeof _that.queryParam.compareTimeArray === 'string' ? JSON.parse(_that.queryParam.compareTimeArray) : _that.queryParam.compareTimeArray
              _that.queryParam.compareTimeArray[0] = {
                date_month_st: curYear + '-04',
                date_month_et: curYear + '-06',
                date_name: curYear + '第二季度'
              }
              _that.timeValue = curYear + '第二季度'
            }
          },
          {
            text: '第三季度',
            value () {
              // _that.dateValue = ''
              let curYear = _that.curYearValue
              _that.queryParam.compareTimeArray = typeof _that.queryParam.compareTimeArray === 'string' ? JSON.parse(_that.queryParam.compareTimeArray) : _that.queryParam.compareTimeArray
              _that.queryParam.compareTimeArray[0] = {
                date_month_st: curYear + '-07',
                date_month_et: curYear + '-09',
                date_name: curYear + '第三季度'
              }
              _that.timeValue = curYear + '第三季度'
            }
          },
          {
            text: '第四季度',
            value () {
              // _that.dateValue = ''
              let curYear = _that.curYearValue
              _that.queryParam.compareTimeArray = typeof _that.queryParam.compareTimeArray === 'string' ? JSON.parse(_that.queryParam.compareTimeArray) : _that.queryParam.compareTimeArray
              _that.queryParam.compareTimeArray[0] = {
                date_month_st: curYear + '-10',
                date_month_et: curYear + '-12',
                date_name: curYear + '第四季度'
              }
              _that.timeValue = curYear + '第四季度'
            }
          },
          {
            text: '上半年',
            value () {
              // _that.dateValue = ''
              let curYear = _that.curYearValue
              _that.queryParam.compareTimeArray = typeof _that.queryParam.compareTimeArray === 'string' ? JSON.parse(_that.queryParam.compareTimeArray) : _that.queryParam.compareTimeArray
              _that.queryParam.compareTimeArray[0] = {
                date_month_st: curYear + '-01',
                date_month_et: curYear + '-06',
                date_name: curYear + '上半年'
              }
              _that.timeValue = curYear + '上半年'
            }
          },
          {
            text: '下半年',
            value () {
              // _that.dateValue = ''
              let curYear = _that.curYearValue
              _that.queryParam.compareTimeArray = typeof _that.queryParam.compareTimeArray === 'string' ? JSON.parse(_that.queryParam.compareTimeArray) : _that.queryParam.compareTimeArray
              _that.queryParam.compareTimeArray[0] = {
                date_month_st: curYear + '-07',
                date_month_et: curYear + '-12',
                date_name: curYear + '下半年'
              }
              _that.timeValue = curYear + '下半年'
            }
          },
          {
            text: '整年度',
            value () {
              // _that.dateValue = ''
              let curYear = _that.curYearValue
              _that.queryParam.compareTimeArray = typeof _that.queryParam.compareTimeArray === 'string' ? JSON.parse(_that.queryParam.compareTimeArray) : _that.queryParam.compareTimeArray
              _that.queryParam.compareTimeArray[0] = {
                date_month_st: curYear + '-01',
                date_month_et: curYear + '-12',
                date_name: curYear + '整年度'
              }
              _that.timeValue = curYear + '整年度'
            }
          }
        ]
      }
      _that.options2 = {
        shortcuts: [
          {
            text: '第一季度',
            onClick: (picker) => {
              let curYear = _that.curYearCompareValue
              if (curYear === '') return this.$Message.error('请选择对比年份！')
              _that.queryParam.compareTimeArray = typeof _that.queryParam.compareTimeArray === 'string' ? JSON.parse(_that.queryParam.compareTimeArray) : _that.queryParam.compareTimeArray
              _that.compareValue = []
              _that.checkClickType('left')
              _that.clickType = 'left'
              let newArr = [...[], ..._that.queryParam.compareTimeArray]
              let curIdx = newArr.findIndex((e, idx) => { return idx > 0 && e.date_month_st === curYear + '-01' && e.date_month_et === curYear + '-03' })
              if (curIdx === -1) {
                if (this.queryParam.compareTimeArray.length > 1 && JSON.stringify(this.queryParam.compareTimeArray[1]) === '{}') {
                  Object.assign(_that.queryParam.compareTimeArray[1], {
                    date_month_st: curYear + '-01',
                    date_month_et: curYear + '-03',
                    date_name: curYear + '第一季度'
                  })
                } else {
                  _that.queryParam.compareTimeArray.push({
                    date_month_st: curYear + '-01',
                    date_month_et: curYear + '-03',
                    date_name: curYear + '第一季度'
                  })
                }
              } else {
                _that.queryParam.compareTimeArray.splice(curIdx, 1)
              }
              let curSelectedTime = curYear + '第一季度'
              if (_that.selectedDates === '') {
                _that.selectedDates = curSelectedTime
              } else {
                if (_that.selectedDates.indexOf(curSelectedTime) === -1) {
                  _that.selectedDates += ',' + curSelectedTime
                } else if (_that.selectedDates.indexOf(curSelectedTime) === 0) {
                  _that.selectedDates = _that.selectedDates.length === 8 ? _that.selectedDates.replace(curSelectedTime, '') : _that.selectedDates.replace(curSelectedTime + ',', '')
                } else {
                  _that.selectedDates = _that.selectedDates.replace(',' + curSelectedTime, '')
                }
              }
            }
          },
          {
            text: '第二季度',
            onClick: () => {
              let curYear = _that.curYearCompareValue
              if (curYear === '') return this.$Message.error('请选择对比年份！')
              _that.queryParam.compareTimeArray = typeof _that.queryParam.compareTimeArray === 'string' ? JSON.parse(_that.queryParam.compareTimeArray) : _that.queryParam.compareTimeArray
              _that.compareValue = []
              _that.checkClickType('left')
              _that.clickType = 'left'
              let newArr = [...[], ..._that.queryParam.compareTimeArray]
              let curIdx = newArr.findIndex((e, idx) => { return idx > 0 && e.date_month_st === curYear + '-04' && e.date_month_et === curYear + '-06' })
              if (curIdx === -1) {
                if (this.queryParam.compareTimeArray.length > 1 && JSON.stringify(this.queryParam.compareTimeArray[1]) === '{}') {
                  Object.assign(_that.queryParam.compareTimeArray[1], {
                    date_month_st: curYear + '-04',
                    date_month_et: curYear + '-06',
                    date_name: curYear + '第二季度'
                  })
                } else {
                  _that.queryParam.compareTimeArray.push({
                    date_month_st: curYear + '-04',
                    date_month_et: curYear + '-06',
                    date_name: curYear + '第二季度'
                  })
                }
              } else {
                _that.queryParam.compareTimeArray.splice(curIdx, 1)
              }
              let curSelectedTime = curYear + '第二季度'
              if (_that.selectedDates === '') {
                _that.selectedDates = curSelectedTime
              } else {
                if (_that.selectedDates.indexOf(curSelectedTime) === -1) {
                  _that.selectedDates += ',' + curSelectedTime
                } else if (_that.selectedDates.indexOf(curSelectedTime) === 0) {
                  _that.selectedDates = _that.selectedDates.length === 8 ? _that.selectedDates.replace(curSelectedTime, '') : _that.selectedDates.replace(curSelectedTime + ',', '')
                } else {
                  _that.selectedDates = _that.selectedDates.replace(',' + curSelectedTime, '')
                }
              }
            }
          },
          {
            text: '第三季度',
            onClick: () => {
              let curYear = _that.curYearCompareValue
              if (curYear === '') return this.$Message.error('请选择对比年份！')
              _that.queryParam.compareTimeArray = typeof _that.queryParam.compareTimeArray === 'string' ? JSON.parse(_that.queryParam.compareTimeArray) : _that.queryParam.compareTimeArray
              _that.compareValue = []
              _that.checkClickType('left')
              _that.clickType = 'left'
              let newArr = [...[], ..._that.queryParam.compareTimeArray]
              let curIdx = newArr.findIndex((e, idx) => { return idx > 0 && e.date_month_st === curYear + '-07' && e.date_month_et === curYear + '-09' })
              if (curIdx === -1) {
                if (this.queryParam.compareTimeArray.length > 1 && JSON.stringify(this.queryParam.compareTimeArray[1]) === '{}') {
                  Object.assign(_that.queryParam.compareTimeArray[1], {
                    date_month_st: curYear + '-07',
                    date_month_et: curYear + '-09',
                    date_name: curYear + '第三季度'
                  })
                } else {
                  _that.queryParam.compareTimeArray.push({
                    date_month_st: curYear + '-07',
                    date_month_et: curYear + '-09',
                    date_name: curYear + '第三季度'
                  })
                }
              } else {
                _that.queryParam.compareTimeArray.splice(curIdx + 1, 1)
              }
              let curSelectedTime = curYear + '第三季度'
              if (_that.selectedDates === '') {
                _that.selectedDates = curSelectedTime
              } else {
                if (_that.selectedDates.indexOf(curSelectedTime) === -1) {
                  _that.selectedDates += ',' + curSelectedTime
                } else if (_that.selectedDates.indexOf(curSelectedTime) === 0) {
                  _that.selectedDates = _that.selectedDates.length === 8 ? _that.selectedDates.replace(curSelectedTime, '') : _that.selectedDates.replace(curSelectedTime + ',', '')
                } else {
                  _that.selectedDates = _that.selectedDates.replace(',' + curSelectedTime, '')
                }
              }
            }
          },
          {
            text: '第四季度',
            onClick: () => {
              let curYear = _that.curYearCompareValue
              if (curYear === '') return this.$Message.error('请选择对比年份！')
              _that.queryParam.compareTimeArray = typeof _that.queryParam.compareTimeArray === 'string' ? JSON.parse(_that.queryParam.compareTimeArray) : _that.queryParam.compareTimeArray
              _that.compareValue = []
              _that.checkClickType('left')
              _that.clickType = 'left'
              let newArr = [...[], ..._that.queryParam.compareTimeArray]
              let curIdx = newArr.findIndex((e, idx) => { return idx > 0 && e.date_month_st === curYear + '-10' && e.date_month_et === curYear + '-12' })
              if (curIdx === -1) {
                if (this.queryParam.compareTimeArray.length > 1 && JSON.stringify(this.queryParam.compareTimeArray[1]) === '{}') {
                  Object.assign(_that.queryParam.compareTimeArray[1], {
                    date_month_st: curYear + '-10',
                    date_month_et: curYear + '-12',
                    date_name: curYear + '第四季度'
                  })
                } else {
                  _that.queryParam.compareTimeArray.push({
                    date_month_st: curYear + '-10',
                    date_month_et: curYear + '-12',
                    date_name: curYear + '第四季度'
                  })
                }
              } else {
                _that.queryParam.compareTimeArray.splice(curIdx + 1, 1)
              }
              let curSelectedTime = curYear + '第四季度'
              if (_that.selectedDates === '') {
                _that.selectedDates = curSelectedTime
              } else {
                if (_that.selectedDates.indexOf(curSelectedTime) === -1) {
                  _that.selectedDates += ',' + curSelectedTime
                } else if (_that.selectedDates.indexOf(curSelectedTime) === 0) {
                  _that.selectedDates = _that.selectedDates.length === 8 ? _that.selectedDates.replace(curSelectedTime, '') : _that.selectedDates.replace(curSelectedTime + ',', '')
                } else {
                  _that.selectedDates = _that.selectedDates.replace(',' + curSelectedTime, '')
                }
              }
            }
          },
          {
            text: '上半年',
            onClick: () => {
              let curYear = _that.curYearCompareValue
              if (curYear === '') return this.$Message.error('请选择对比年份！')
              _that.queryParam.compareTimeArray = typeof _that.queryParam.compareTimeArray === 'string' ? JSON.parse(_that.queryParam.compareTimeArray) : _that.queryParam.compareTimeArray
              _that.compareValue = []
              _that.checkClickType('left')
              _that.clickType = 'left'
              let newArr = [...[], ..._that.queryParam.compareTimeArray]
              let curIdx = newArr.findIndex((e, idx) => { return idx > 0 && e.date_month_st === curYear + '-01' && e.date_month_et === curYear + '-06' })
              if (curIdx === -1) {
                if (this.queryParam.compareTimeArray.length > 1 && JSON.stringify(this.queryParam.compareTimeArray[1]) === '{}') {
                  Object.assign(_that.queryParam.compareTimeArray[1], {
                    date_month_st: curYear + '-01',
                    date_month_et: curYear + '-06',
                    date_name: curYear + '上半年'
                  })
                } else {
                  _that.queryParam.compareTimeArray.push({
                    date_month_st: curYear + '-01',
                    date_month_et: curYear + '-06',
                    date_name: curYear + '上半年'
                  })
                }
              } else {
                _that.queryParam.compareTimeArray.splice(curIdx + 1, 1)
              }
              let curSelectedTime = curYear + '上半年'
              if (_that.selectedDates === '') {
                _that.selectedDates = curSelectedTime
              } else {
                if (_that.selectedDates.indexOf(curSelectedTime) === -1) {
                  _that.selectedDates += ',' + curSelectedTime
                } else if (_that.selectedDates.indexOf(curSelectedTime) === 0) {
                  _that.selectedDates = _that.selectedDates.length === 7 ? _that.selectedDates.replace(curSelectedTime, '') : _that.selectedDates.replace(curSelectedTime + ',', '')
                } else {
                  _that.selectedDates = _that.selectedDates.replace(',' + curSelectedTime, '')
                }
              }
            }
          },
          {
            text: '下半年',
            onClick: () => {
              let curYear = _that.curYearCompareValue
              if (curYear === '') return this.$Message.error('请选择对比年份！')
              _that.queryParam.compareTimeArray = typeof _that.queryParam.compareTimeArray === 'string' ? JSON.parse(_that.queryParam.compareTimeArray) : _that.queryParam.compareTimeArray
              _that.compareValue = []
              _that.checkClickType('left')
              _that.clickType = 'left'
              let newArr = [...[], ..._that.queryParam.compareTimeArray]
              let curIdx = newArr.findIndex((e, idx) => { return idx > 0 && e.date_month_st === curYear + '-07' && e.date_month_et === curYear + '-12' })
              if (curIdx === -1) {
                if (this.queryParam.compareTimeArray.length > 1 && JSON.stringify(this.queryParam.compareTimeArray[1]) === '{}') {
                  Object.assign(_that.queryParam.compareTimeArray[1], {
                    date_month_st: curYear + '-07',
                    date_month_et: curYear + '-12',
                    date_name: curYear + '下半年'
                  })
                } else {
                  _that.queryParam.compareTimeArray.push({
                    date_month_st: curYear + '-07',
                    date_month_et: curYear + '-12',
                    date_name: curYear + '下半年'
                  })
                }
              } else {
                _that.queryParam.compareTimeArray.splice(curIdx + 1, 1)
              }
              let curSelectedTime = curYear + '下半年'
              if (_that.selectedDates === '') {
                _that.selectedDates = curSelectedTime
              } else {
                if (_that.selectedDates.indexOf(curSelectedTime) === -1) {
                  _that.selectedDates += ',' + curSelectedTime
                } else if (_that.selectedDates.indexOf(curSelectedTime) === 0) {
                  _that.selectedDates = _that.selectedDates.length === 7 ? _that.selectedDates.replace(curSelectedTime, '') : _that.selectedDates.replace(curSelectedTime + ',', '')
                } else {
                  _that.selectedDates = _that.selectedDates.replace(',' + curSelectedTime, '')
                }
              }
            }
          },
          {
            text: '整年度',
            onClick: () => {
              let curYear = _that.curYearCompareValue
              if (curYear === '') return this.$Message.error('请选择对比年份！')
              _that.queryParam.compareTimeArray = typeof _that.queryParam.compareTimeArray === 'string' ? JSON.parse(_that.queryParam.compareTimeArray) : _that.queryParam.compareTimeArray
              _that.compareValue = []
              _that.checkClickType('left')
              _that.clickType = 'left'
              let newArr = [...[], ..._that.queryParam.compareTimeArray]
              let curIdx = newArr.findIndex((e, idx) => { return idx > 0 && e.date_month_st === curYear + '-01' && e.date_month_et === curYear + '-12' })
              if (curIdx === -1) {
                if (this.queryParam.compareTimeArray.length > 1 && JSON.stringify(this.queryParam.compareTimeArray[1]) === '{}') {
                  Object.assign(_that.queryParam.compareTimeArray[1], {
                    date_month_st: curYear + '-01',
                    date_month_et: curYear + '-12',
                    date_name: curYear + '整年度'
                  })
                } else {
                  _that.queryParam.compareTimeArray.push({
                    date_month_st: curYear + '-01',
                    date_month_et: curYear + '-12',
                    date_name: curYear + '整年度'
                  })
                }
              } else {
                _that.queryParam.compareTimeArray.splice(curIdx + 1, 1)
              }
              let curSelectedTime = curYear + '整年度'
              if (_that.selectedDates === '') {
                _that.selectedDates = curSelectedTime
              } else {
                if (_that.selectedDates.indexOf(curSelectedTime) === -1) {
                  _that.selectedDates += ',' + curSelectedTime
                } else if (_that.selectedDates.indexOf(curSelectedTime) === 0) {
                  _that.selectedDates = _that.selectedDates.length === 7 ? _that.selectedDates.replace(curSelectedTime, '') : _that.selectedDates.replace(curSelectedTime + ',', '')
                } else {
                  _that.selectedDates = _that.selectedDates.replace(',' + curSelectedTime, '')
                }
              }
            }
          }
        ]
      }
    },
    // 清除已选月份
    clearRightDate (type) {
      this.queryParam.compareTimeArray = typeof this.queryParam.compareTimeArray === 'string' ? JSON.parse(this.queryParam.compareTimeArray) : this.queryParam.compareTimeArray
      if (type === 'dateValue') {
        this.timeValue = ''
        this.curYearValue = ''
        this.queryParam.compareTimeArray[0] = {
          date_month_st: '',
          date_month_et: '',
          date_name: ''
        }
      } else {
        this.selectedDates = ''
        this.curYearCompareValue = ''
        this.queryParam.compareTimeArray = [this.queryParam.compareTimeArray[0], {}]
      }
    },
    // 数据导出
    exportData () {
      let data = Object.assign(this.queryParam, { template_name: '' })
      materialCompareExport(data).then(res => {
        if (res.data.Code === 10000) {
          window.open(res.data.fileUrl, '_blank')
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 重置
    resetResults () {
      this.queryParam = {
        usefor: '',
        shipname_in: '',
        vendorname: '',
        compareTimeArray: [{}]
      }
      this.dateValue = ''
      this.compareValue = []
      this.setSearchData.shipname_in.selected = ''
      this.setSearchData.usefor.selected = ''
      this.setSearchData.vendorname.selected = ''
      this.$store.state.setState.compareAnalyseParam = {}
      this.getDate()
    },
    // 获取默认时间、对比时间
    getDate () {
      let that = this
      if (typeof that.$store.state.setState.compareAnalyseParam.compareTimeArray === 'object') {
        that.getCurDate()
      } else {
        if (Object.keys(that.$store.state.setState.compareAnalyseParam).length > 0) { // 获取存储的入参
          this.curYearValue = new Date().getFullYear().toString()
          this.curYearCompareValue = new Date().getFullYear().toString()
          let storeQuery = that.$store.state.setState.compareAnalyseParam
          storeQuery.compareTimeArray = JSON.parse(storeQuery.compareTimeArray)
          storeQuery.compareTimeArray.map((item, index) => {
            if (item.date_month_st === item.date_month_et) {
              if (index === 0) {
                that.dateValue = item.date_month_st
              } else {
                that.compareValue.push(item.date_month_st)
              }
            } else {
              if (storeQuery.timeValue !== '') {
                that.timeValue = storeQuery.timeValue
              }
              if (storeQuery.selectedDates !== '') {
                that.selectedDates = storeQuery.selectedDates
              }
            }
          })
          that.queryParam.compareTimeArray = storeQuery.compareTimeArray
        } else {
          that.getCurDate()
        }
      }
      that.getList()
    },
    getCurDate () {
      // let curYear = new Date().getFullYear()
      this.curYearValue = new Date().getFullYear().toString()
      this.curYearCompareValue = new Date().getFullYear().toString()
      this.timeValue = this.curYearValue + '第一季度'
      this.queryParam.compareTimeArray[0] = {
        date_month_st: this.curYearValue + '-01',
        date_month_et: this.curYearValue + '-03',
        date_name: this.curYearValue + '第一季度'
      }
      this.selectedDates = this.curYearValue + '第二季度'
      this.queryParam.compareTimeArray[1] = {
        date_month_st: this.curYearValue + '-04',
        date_month_et: this.curYearValue + '-06',
        date_name: this.curYearValue + '第二季度'
      }
    }
  },
  beforeDestroy () {
    let data = {
      compareTimeArray: this.queryParam.compareTimeArray,
      timeValue: this.timeValue,
      selectedDates: this.selectedDates
    }
    this.$store.commit('setCompareAnalyse', data)
  },
  async created () {
    this.getDate()
    await this.getOptions()
    API.materialUsefor().then(res => { // 获取用途
      if (res.data.Code === 10000) {
        res.data.Result.map(item => {
          this.setSearchData.usefor.selectData.push({
            value: item.usefor,
            label: item.usefor
          })
        })
      }
    })
    API.materialWarehousename().then(res => { // 获取船舶
      if (res.data.Code === 10000) {
        res.data.Result.map(item => {
          this.setSearchData.shipname_in.selectData.push({
            value: item.shipname,
            label: item.shipname
          })
        })
      }
    })
    API.materialVendorname().then(res => { // 获取供应商
      if (res.data.Code === 10000) {
        res.data.Result.map(item => {
          this.setSearchData.vendorname.selectData.push({
            value: item.vendorname,
            label: item.vendorname
          })
        })
      }
    })
  }
}
</script>
<style lang="less" scoped>
.compare_analysis {
  .text_con {
    margin: 10px 0;
    button {
      float: right;
      color: #007DFF;
      font-size: 14px;
      font-weight: bold;
      margin-top: -5px;
      .ivu-icon {
        font-weight: bold;
      }
    }
  }
  .date_selec {
    position: relative;
    display: inline;
    span {
      position: absolute;
      top: 0;
      left: 10px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 86%;
    }
  }
}
.data_list {
  clear: both;
}
</style>
<style lang="less">
.compare_analysis {
  .searchBtn {
    top: 63px;
    left: 790px;
    position: absolute;
  }
}
.data_list {
  .upClass {
    color: red;
  }
  .downClass {
    color: green;
  }
}
</style>

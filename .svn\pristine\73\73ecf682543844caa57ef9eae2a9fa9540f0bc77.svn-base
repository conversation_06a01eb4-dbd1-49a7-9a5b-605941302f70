<template>
  <div>
    <Card>
      <search @searchResults='searchResults' @selectOnChanged='selectOnChanged' :setSearch='setSearchData' @resetResults='resetResults'></search>
      <div class="train_info">
        <h3>培训信息</h3>
        <p>
          <span><img src="../../../../assets/images/theme.png" alt="" style="width: 12px;">完结课题数量 &nbsp;&nbsp; </span>
          <span style="font-size: 32px">{{ trainInfoData.theme_num }}</span>个
          <span style="margin-left: 150px"><img src="../../../../assets/images/train.png" alt="" style="width: 14px;">培训时长 &nbsp;&nbsp; </span>
          <span style="font-size: 32px">{{ trainInfoData ? trainInfoData.total_time_hour.split('.')[0] : '' }}</span>小时
          <span style="font-size: 32px">{{ trainInfoData ? trainInfoData.total_time_minute.split('.')[0] : '' }}</span>分钟
        </p>
      </div>
    </Card>
    <Card style="margin-top: 10px" class="train_info">
      <h3>排行榜前十</h3>
      <Row style="line-height:30px">
        <Col span="6">
          <h4 class="table_lbody">培训总时长/签到次数<Button type="text" @click="moreList(trainList, 'train')">更多</Button></h4>
          <Row style="color: #1F2633;line-height: 20px;">
            <Col span="2">&nbsp;</Col>
            <Col span="5">姓名</Col>
            <Col span="11" style="text-align: center">时长<Icon type="md-arrow-dropup" color="gray" size="22" @click="changeOrder('0', 'train')" /></Col>
            <Col span="6" style="text-align: center">次数<Icon type="md-arrow-dropup" color="gray" size="22" @click="changeOrder('1', 'train')" /></Col>
          </Row>
          <Row v-for="(item, idx) in trainList" :key="idx">
            <Col span="2" class="list-code" :class="{'first_class': idx === 0, 'second_class': idx === 1, 'third_class': idx === 2}">{{ idx+1 }}</Col>
            <Col span="5">{{ item.member_name }}</Col>
            <Col span="11" style="text-align: center">{{ item.member_train_hour.split('.')[0] }}小时{{ item.member_train_minute.split('.')[0] }}分钟</Col>
            <Col span="6" style="text-align: center">{{ item.member_sign_times }}次</Col>
          </Row>
        </Col>
        <Col span="6" offset="1">
          <h4 class="table_lbody">考试成绩/次数<Button type="text" @click="moreList(examList, 'exam')">更多</Button></h4>
          <Row style="color: #1F2633;line-height: 20px;">
            <Col span="2">&nbsp;</Col>
            <Col span="8">姓名</Col>
            <Col span="9">成绩<Icon type="md-arrow-dropup" color="gray" size="22" @click="changeOrder('1', 'exam')" /></Col>
            <Col span="5" style="text-align: center">次数<Icon type="md-arrow-dropup" color="gray" size="22" @click="changeOrder('0', 'exam')" /></Col>
          </Row>
          <Row v-for="(item, idx) in examList" :key="idx">
            <Col span="2" class="list-code" :class="{'first_class': idx === 0, 'second_class': idx === 1, 'third_class': idx === 2}">{{ idx+1 }}</Col>
            <Col span="8">{{ item.member_name }}</Col>
            <Col span="9">{{ item.member_avg_score }}分</Col>
            <Col span="5" style="text-align: center">{{ item.member_exam_times }}次</Col>
          </Row>
        </Col>
        <Col span="10" offset="1">
          <h4 class="table_lbody">讲师评分/参与人数<Button type="text" @click="moreList(evaluateList, 'evaluate')">更多</Button></h4>
          <Row style="color: #1F2633;line-height: 20px;">
            <Col span="2">&nbsp;</Col>
            <Col span="4">姓名</Col>
            <Col span="6">课题</Col>
            <Col span="4" style="text-align: center">评分<Icon type="md-arrow-dropup" color="gray" size="22" @click="changeOrder('0', 'evaluate')" /></Col>
            <Col span="4" style="text-align: center">参会人数<Icon type="md-arrow-dropup" color="gray" size="22" @click="changeOrder('1', 'evaluate')" /></Col>
            <Col span="4" style="text-align: center">参评人数<Icon type="md-arrow-dropup" color="gray" size="22" @click="changeOrder('2', 'evaluate')" /></Col>
          </Row>
          <Row v-for="(item, idx) in evaluateList" :key="idx">
            <Col span="2" class="list-code" :class="{'first_class': idx === 0, 'second_class': idx === 1, 'third_class': idx === 2}">{{ idx+1 }}</Col>
            <Col span="4">{{ item.train_lecturer }}</Col>
            <Col span="6">
              <span class="theme-title">{{ item.theme_title }}</span>
            </Col>
            <Col span="4" style="text-align: center">{{ item.theme_evaluate_score }}分</Col>
            <Col span="4" style="text-align: center">{{ item.train_member_num }}人</Col>
            <Col span="4" style="text-align: center">{{ item.member_evaluate_num }}人</Col>
          </Row>
        </Col>
      </Row>
      <detailModal :modalData="modalData"></detailModal>
    </Card>
  </div>
</template>
<script>
import search from '_c/search' // 查询组件
import detailModal from './detailModal'
import API from '@/api/examSystem/trainingModule/memberStatistics'
export default {
  components: {
    search,
    detailModal
  },
  data () {
    return {
      trainInfoData: '',
      trainList: [],
      examList: [],
      evaluateList: [],
      listQuery: {
        train_date_start: '',
        train_date_end: ''
      },
      listData: {
        order_type: '0', // 排序方式
        pageSize: 10,
        pageIndex: 1
      },
      setSearchData: {
        train_date: {
          type: 'date_range',
          label: '时间',
          selected: [],
          value: [],
          width: 190,
          isdisable: false
        }
      },
      modalData: {
        modal: false,
        type: '',
        title: '',
        time: {
          train_date_start: '',
          train_date_end: ''
        },
        data: undefined
      }
    }
  },
  created () {
    this.getAllListData()
  },
  methods: {
    // 获取课题数量&培训时长 总计
    getInfoList () {
      API.queryTrainThemeStatInfo(this.listQuery).then(res => {
        if (res.data.Code === 10000) {
          this.trainInfoData = res.data
        }
      })
    },
    // 获取培训总时长/签到次数
    getTrainList () {
      let data = { ...this.listQuery, ...this.listData }
      API.queryTrainMemberDurationPage(data).then(res => {
        if (res.data.Code === 10000) {
          this.trainList = res.data.Result
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 考试平均成绩/次数
    getExamList () {
      let data = {...this.listQuery, ...this.listData}
      API.queryTrainMemberExamAvgPage(data).then(res => {
        if (res.data.Code === 10000) {
          this.examList = res.data.Result
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 讲师评分/参与人数
    getLecturerList () {
      let data = {...this.listQuery, ...this.listData}
      API.queryTrainThemeLecturerPage(data).then(res => {
        if (res.data.Code === 10000) {
          this.evaluateList = res.data.Result
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 排序
    changeOrder (val, type) {
      this.listData.order_type = val
      if (type === 'train') {
        this.getTrainList()
      } if (type === 'exam') {
        this.getExamList()
      } else if (type === 'evaluate') {
        this.getLecturerList()
      }
    },
    // 时间格式
    selectOnChanged (e) {
      this.listQuery.train_date_start = e.key[0]
      this.listQuery.train_date_end = e.key[1]
    },
    // 查询
    searchResults (e) {
      this.getAllListData()
    },
    // 重置
    resetResults () {
      this.listQuery.train_date_start = ''
      this.listQuery.train_date_end = ''
      this.setSearchData.train_date.selected = ''
      this.getAllListData()
    },
    getAllListData () {
      this.getInfoList()
      this.getTrainList()
      this.getExamList()
      this.getLecturerList()
    },
    // 开启更多弹窗
    moreList (row, type) {
      this.modalData = {
        data: row,
        modal: true,
        time: {
          train_date_start: this.listQuery.train_date_start,
          train_date_end: this.listQuery.train_date_end
        },
        type: type
      }
      if (type === 'train') {
        this.modalData.title = '培训总时长/签到次数'
      } else if (type === 'exam') {
        this.modalData.title = '考试成绩/次数'
      } else {
        this.modalData.title = '讲师评分/参与人数'
      }
    }
  }
}
</script>
<style lang="less" scoped>
.train_info {
  margin: 20px 0 15px;
  .list-code {
    text-align: right;
    padding-right: 5px;
  }
  .theme-title {
    margin: 5px 0;
    line-height: 20px;
    -ms-word-break: break-all;
    word-break: break-all;
    width: 90%;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  h3 {
    margin-bottom: 20px;
    color: #000;
    font-size: 18px;
  }
  span {
    color: #333;
    font-size: 14px;
  }
  span > img {
    margin-right: 5px;
    vertical-align: text-top;
  }
}
.table_lbody {
  color: #1F2633;
  font-size: 16px;
  margin-bottom: 10px;
  button {
    color: #3D7FFF;
    font-size: 14px;
    font-weight: 600;
    float: right;
    padding: 0;
  }
}
.first_class {
  color: #FF3B3B;
}
.second_class {
  color: #F5A623;
}
.third_class {
  color: #3D7FFF;
}
</style>

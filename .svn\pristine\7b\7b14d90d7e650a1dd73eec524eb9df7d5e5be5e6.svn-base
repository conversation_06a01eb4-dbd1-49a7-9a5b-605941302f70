<template>
  <div ref="dom" class="charts chart-bar"></div>
</template>

<script>
import echarts from 'echarts'
import tdTheme from './theme.json'
import { on, off } from '@/libs/tools'
echarts.registerTheme('tdTheme', tdTheme)
export default {
  name: 'ChartBar',
  props: {
    value: Object,
    text: String,
    subtext: String,
    unit: String,
    clickable: {
      type: Boolean,
      default: false
    },
    rotate: {
      type: String,
      default: '0'
    },
    markLine: {
      type: Boolean,
      default: false
    },
    legendRight: {
      type: Number
    }
  },
  data () {
    return {
      dom: null
    }
  },
  methods: {
    chartInit () {
      let _that = this
      this.$nextTick(() => {
        let xAxisData = this.value.xAxis
        let seriesData = this.value.data
        let option = {
          title: {
            text: this.text,
            subtext: this.subtext,
            x: 'left'
          },
          grid: {
            left: '3%',
            right: 120,
            bottom: '3%',
            containLabel: true
          },
          legend: {
            right: this.legendRight,
            data: this.value.legend ? this.value.legend : []
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: { // 坐标轴指示器，坐标轴触发有效
              type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
            },
            formatter: (item => { // '{b}<br/>{c}' + this.unit
              let str = ''
              if (item.length > 1) {
                str = item[0].name + '<br/>'
                item.forEach(list => {
                  str += list.marker + list.seriesName + ':' + list.value + '<br/>'
                })
              } else {
                str = item[0].name + '<br/>' + item[0].marker + item[0].value
              }
              return str
            })
          },
          barMaxWidth: 20,
          xAxis: {
            type: 'category',
            data: xAxisData,
            axisLabel: {
              interval: 0,
              rotate: this.rotate
            },
            splitLine: {
              show: false // 取消网格线
            }
          },
          yAxis: {
            type: 'value',
            name: this.unit
          },
          series: []
          // series: [{
          //   data: seriesData,
          //   type: 'bar'
          // }]
        }
        if (seriesData.length > 0 && seriesData[0].constructor === Array) {
          seriesData.forEach((item, idx) => {
            option.series.push({
              data: item,
              type: 'bar',
              name: this.value.legend && this.value.legend.length > 1 ? this.value.legend[idx] : ''
            })
            if (this.markLine) {
              option.series[idx] = Object.assign(option.series[idx], {
                markLine: {
                  label: {
                    formatter: '{b}: {c}'
                  },
                  data: [{ type: 'average', name: '均值' }]
                }
              })
            }
          })
        } else {
          option.series = [{
            data: seriesData,
            type: 'bar',
            name: ''
          }]
          if (this.markLine) {
            option.series = Object.assign(option.series[0], {
              markLine: {
                label: {
                  formatter: '{b}: {c}'
                },
                data: [{ type: 'average', name: '均值' }]
              }
            })
          }
        }
        this.dom = echarts.init(this.$refs.dom, 'tdTheme')
        this.dom.setOption(option)
        this.dom.off('click')
        // this.dom.getZr().off('click')
        this.dom.getZr().on('click', item => {
          let pointInPixel = [item.offsetX, item.offsetY]
          if (this.dom.containPixel('grid', pointInPixel)) {
            let xIndex = this.dom.convertFromPixel({ seriesIndex: 0 }, [item.offsetX, item.offsetY])[0]
            if (this.clickable) {
              _that.$emit('clickBack', xIndex)
            }
          }
        })
        // this.dom.on('click', (params) => {
        //   if (this.clickable) {
        //     _that.$emit('clickBack', params)
        //   }
        // })
        on(window, 'resize', this.resize)
      })
    },
    resize () {
      this.dom.resize()
    }
  },
  mounted () {
    this.chartInit()
  },
  beforeDestroy () {
    off(window, 'resize', this.resize)
  },
  watch: {
    value: {
      handler (n, o) {
        this.chartInit()
      },
      deep: true,
      immediate: true
    }
  }
}
</script>

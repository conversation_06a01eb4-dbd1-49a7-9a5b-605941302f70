<template>
  <div class="con_area">
    <Tabs v-model="curId" @on-click="tabClick">
      <TabPane v-for="(item, idx) in shipList" :key="item.id" :label="item.name" :name="item.id">
        <Form ref="formInline" :model="formData" :label-width="130" inline>
          <!-- 合同约定项目 -->
          <div class="fee_list_top">
            <div class="fee_list_title">合同约定项目</div>
            <div class="fee_list_content">
              <FormItem prop="user" label="船员工资">
                <Input type="text" v-model="formData.crew_wages">
                </Input>
              </FormItem>
              <FormItem prop="user" label="船员支出">
                <Input type="text" v-model="formData.crew_expenses">
                </Input>
              </FormItem>
              <FormItem prop="user" label="船员伙食">
                <Input type="text" v-model="formData.crew_meals">
                </Input>
              </FormItem>
              <FormItem prop="user" label="物料备件">
                <Input type="text" v-model="formData.material_parts">
                </Input>
              </FormItem>
              <FormItem prop="user" label="滑油费用">
                <Input type="text" v-model="formData.oil_cost">
                </Input>
              </FormItem>
              <FormItem prop="user" label="船舶维修">
                <Input type="text" v-model="formData.ship_maintenance">
                </Input>
              </FormItem>
              <FormItem prop="user" label="船员家庭健康保险">
                <Input type="text" v-model="formData.crew_insurance">
                </Input>
              </FormItem>
              <FormItem prop="user" label="船舶管理费">
                <Input type="text" v-model="formData.ship_management_fee">
                </Input>
              </FormItem>
              <FormItem prop="user" label="其他">
                <Input type="text" v-model="formData.other_cost">
                </Input>
              </FormItem>
              <FormItem prop="user" label="合计">
                <span>{{ getShipTotal() }}</span>
              </FormItem>
            </div>
          </div>

          <!-- 接船费用 -->
          <div class="fee_list_top">
            <div class="fee_list_title">接船费用</div>
            <div class="fee_list_content">
              <FormItem prop="user" label="船员工资">
                <Input type="text" v-model="formData.cost_crew_wages">
                </Input>
              </FormItem>
              <FormItem prop="user" label="船员支出">
                <Input type="text" v-model="formData.cost_crew_expenses">
                </Input>
              </FormItem>
              <FormItem prop="user" label="船员伙食">
                <Input type="text" v-model="formData.cost_crew_meals">
                </Input>
              </FormItem>
              <FormItem prop="user" label="物料备件">
                <Input type="text" v-model="formData.cost_material_parts">
                </Input>
              </FormItem>
              <FormItem prop="user" label="滑油费用">
                <Input type="text" v-model="formData.cost_oil_cost">
                </Input>
              </FormItem>
              <FormItem prop="user" label="船舶维修">
                <Input type="text" v-model="formData.cost_ship_maintenance">
                </Input>
              </FormItem>
              <FormItem prop="user" label="船员家庭健康保险">
                <Input type="text" v-model="formData.cost_crew_insurance">
                </Input>
              </FormItem>
              <FormItem prop="user" label="船舶管理费">
                <Input type="text" v-model="formData.cost_ship_management_fee">
                </Input>
              </FormItem>
              <FormItem prop="user" label="其他">
                <Input type="text" v-model="formData.cost_other_cost">
                </Input>
              </FormItem>
              <FormItem prop="user" label="合计">
                <span>{{ getFeeTotal() }}</span>
              </FormItem>
            </div>
          </div>
        </Form>
      </TabPane>
    </Tabs>
    <Button class="save_btn" type="primary" @click="handleSave">保存</Button>
  </div>
</template>
<script>
import BAPI from '@/api/erpSys/common'
import API from '@/api/shipApplication/index'

export default ({
  data () {
    return {
      curId: '', // 当前船舶id
      shipList: [],
      formData: {}
    }
  },
  methods: {
    getList () { // 获取船舶列表
      let _url = '/ierp/kapi/v2/xtgf/basedata/xtgf_bos_org_copy/IntShipList?pageSize=100&pageNo=1'
      let dataParam = {
        url: _url
      }
      BAPI.transferStationGet(dataParam).then(res => {
        if (res.data.errorCode === '0') {
          if (res.data.data.totalCount && res.data.data.totalCount > 0) {
            this.shipList = res.data.data.rows
            this.curId = res.data.data.rows[0].id
            this.getShipDetail()
          } else {
            this.$Message.warning('暂无数据！')
          }
        } else {
          
        }
      })
    },
    getShipDetail () { // 获取船舶详细配置数据
      this.formData = {}
      API.queryImosShipCostList({ ship_id: this.curId }).then(res => {
        if (res.data.Code === 10000 && res.data.Result.length > 0) {
          this.formData = {...this.formData, ...res.data.Result[0]}
        }
      })
    },
    getShipTotal () { // 获取合同约定项目合计
      const keys = [
        'crew_wages',
        'crew_expenses',
        'crew_meals',
        'material_parts',
        'oil_cost',
        'ship_maintenance',
        'crew_insurance',
        'ship_management_fee',
        'other_cost',
      ]
      const total = keys.reduce((sum, key) => {
        const value = parseFloat(this.formData[key]) // 转换为数字
        return sum + (isNaN(value) || value <= 0 ? 0 : value) // 容错处理，忽略 NaN 或 <= 0 的值
      }, 0)
      return total.toFixed(2)
    },
    getFeeTotal () { // 获取接船费用合计
      const keys = [
        'cost_crew_wages',
        'cost_crew_expenses',
        'cost_crew_meals',
        'cost_material_parts',
        'cost_oil_cost',
        'cost_ship_maintenance',
        'cost_crew_insurance',
        'cost_ship_management_fee',
        'cost_other_cost',
      ]
      const total = keys.reduce((sum, key) => {
        const value = parseFloat(this.formData[key]) // 转换为数字
        return sum + (isNaN(value) || value <= 0 ? 0 : value) // 容错处理，忽略 NaN 或 <= 0 的值
      }, 0)
      return total.toFixed(2)
    },
    tabClick (val) {
      this.curId = val
      this.getShipDetail()
    },
    handleSave () { // 保存
      Object.assign(this.formData, {
        ship_id: this.curId
      })
      API.addOrUpdateImosShipCost(this.formData).then(res => {
        if (res.data.Code === 10000) {
          this.$Message.success(res.data.Message)
          this.getList()
        } else {
          this.$Message.warning(res.data.Message)
        }
      })
    }
  },
  created () {
    this.getList()
  }
})
</script>
<style scoped>
  .save_btn {
    position: absolute;
    right: 50px;
  }
  .fee_list_top {
    display: flex;
    padding: 10px;
    margin-bottom: 15px;
  }
  .fee_list_title {
    background: rgb(84, 152, 247);
    color: rgb(255, 255, 255);
    writing-mode: vertical-lr;
    display: flex;
    padding: 15px 0;
    align-items: center;
    justify-content: center;
    width: 150px;
    font-size: 16px;
    letter-spacing: 5px;
    font-weight: bold;
  }
  .fee_list_content {
    background: #f9fcfd;
    padding-top: 15px;
  }
</style>
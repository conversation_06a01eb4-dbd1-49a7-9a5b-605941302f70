<template>
  <Card>
    <Tabs type="card" v-model="tabId" :animated="false" @on-click="tabClick">
      <TabPane label="审批流程" name="0">
        <ApproveProcess></ApproveProcess>
      </TabPane>
      <TabPane label="时间设置" name="1">
        <TimeSet ref="timeSet"></TimeSet>
      </TabPane>
    </Tabs>
  </Card>
</template>
<script>
import ApproveProcess from './approveProcess.vue'
import TimeSet from './timeSet.vue'

export default ({
  components: {
    ApproveProcess,
    TimeSet
  },
  data () {
    return {
      tabId: 0
    }
  },
  methods: {
    tabClick (val) {
      if (val === '1') {
        this.$refs['timeSet'].getRemindTime()
      }
    }
  }
})
</script>

<template>
  <Layout style="height: 100%" class="main">
    <!-- 左侧菜单导航 -->
    <Sider v-show="this.$route.name !== 'home' && menuShow" hide-trigger collapsible :width="180" :collapsed-width="64" v-model="collapsed" :class="access === 'erpSys' ? 'erpSys-left-sider' : 'left-sider'" :style="{overflow: 'hidden'}">
      <side-menu ref="sideMenu" :active-name="activeName" :collapsed="collapsed" @on-select="turnToPage" :menu-list="menuList">
        <div class="logo-con">
          <img v-show="!collapsed" :src="logo" key="max-logo" />
          <span v-show="!collapsed">兴通海运</span>
          <img v-show="collapsed" :src="logo" key="min-logo" />
        </div>
      </side-menu>
    </Sider>
    <Layout>
      <Header v-show="menuShow" class="header-con">
        <header-bar :collapsed="collapsed" @on-coll-change="handleCollapsedChange">
          <div v-if="this.$route.name === 'home'" class="header-logo">
            <img class="head-logo" :src="logo" alt="">
            <span>兴通海运</span>
          </div>
          <div>
            <!-- <Tooltip placement="bottom">
              <Button icon="ios-log-out" size="large" type="text" @click="quitLogin()"></Button>
              <div slot="content">
                <p>退出登录</p>
              </div>
            </Tooltip> -->
            <Dropdown>
              <Icon type="md-arrow-dropdown" size="24"/>
              <DropdownMenu slot="list">
                <DropdownItem class="change_info_and_login">
                  <div @click="changePwd()">修改密码</div>
                </DropdownItem>
                <DropdownItem class="change_info_and_login">
                  <div @click="quitLogin()">退出登录</div>
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </div>
          <user :message-unread-count="unreadCount" :user-avatar="userAvatar"/>
          <Button v-show="isManageLogin && this.$route.name === 'home'" type="primary" @click="manageLogin" style="margin: 4px 10px 0 0;">后台管理</Button>
        </header-bar>
      </Header>
      <Content :class="menuShow ? 'main-content-con' : 'main-content-con-full'" :style="this.$route.name === 'home' ? 'display: inline;' : ''">
        <div class="topdate" v-show="userDataSyncDate">
          <DatePicker type="daterange" split-panels placeholder="选择日期" @on-change="changeSyncDate" style="width: 200px;"></DatePicker>
          <Button type="primary" @click="dataToSyncData" style="margin-left: 10px;">数据同步</Button>
        </div>
        <!-- linear-gradient(to bottom, #2980b9 0%, #6dd5fa 20%, #f2fcfe 60%, #f0fcff 100%); -->
        <Layout class="main-layout-con" :style="this.$route.name === 'voyagePlanDetail' ? `background: url(${mobileBg});background-size: cover; background-color: #7ea6f4;` : ''">
          <Content class="content-wrapper" :class="(this.$route.name === 'home' || this.$route.name === 'contractMobile') ? 'content-home' : ''">
            <div v-if="access.includes('materialPurchase')" class="org_title">{{ orgName }}</div>
            <keep-alive :include="cacheList">
              <router-view/>
            </keep-alive>
            <ABackTop :height="100" :bottom="80" :right="50" container=".content-wrapper"></ABackTop>
          </Content>
        </Layout>
      </Content>
    </Layout>
    <ChangePwd :modalData="pwdModal" @changeBack="quitLogin"></ChangePwd>
  </Layout>
</template>
<script>
import Cookies from 'js-cookie'
import SideMenu from './components/side-menu'
import HeaderBar from './components/header-bar'
// import TagsNav from './components/tags-nav'
import User from './components/user'
import ChangePwd from '../modal/changPwd.vue'
import ABackTop from './components/a-back-top'
import { mapMutations, mapActions, mapGetters } from 'vuex'
import { getNewTagList, routeEqual, getMenuShow, getMenuIndex } from '@/libs/util'

import routers from '@/router/routers'
import appCode from '@/assets/images/app.png'
import logo from '@/assets/images/logo-boat.png'
import minLogo from '@/assets/images/logo-min.png'
import maxLogo from '@/assets/images/logo.png'
import mobileBg from '@/assets/images/mobile_bg.png'
import { dataToSync } from '@/api/basicData'
import { queryOrgConfigName } from '@/api/materialPurchaseSystem/dataConfigure'
import './main.less'
export default {
  name: 'Main',
  components: {
    SideMenu,
    HeaderBar,
    // TagsNav,
    ChangePwd,
    User,
    ABackTop
  },
  data () {
    return {
      userDataSyncDate: false,
      // collapsed: true,
      searchText: '',
      appCode,
      logo,
      minLogo,
      maxLogo,
      mobileBg,
      isManageLogin: false,
      pwdModal: {
        title: '修改密码',
        modal: false
      },
      dataToDataParam: {
        date_begin: '', // 审核日期开始
        date_end: '' // 审核日期结束
      },
      dynamicRouter: [ // 管理后台菜单路由
        {
          component: '',
          enabled: 1,
          href: '',
          icon: 'md-apps',
          menuDisplay: 1,
          mobileDisplay: 0,
          name: '首页',
          children: [{
            href: 'home',
            name: '首页',
            enabled: 1,
            menuDisplay: 1,
            mobileDisplay: 0,
            icon: 'md-apps',
            component: () => import ('@/view/single-page/home')
          }]
        },
        {
          href: 'jurisdictionManage',
          icon: 'ios-albums-outline',
          name: '组织架构',
          menuDisplay: 1,
          mobileDisplay: 0,
          children: [
            {
              href: 'userManagement',
              menuDisplay: 1,
              mobileDisplay: 0,
              icon: 'md-person',
              name: '用户管理',
              component: () => import('@/view/jurisdictionManage/userManagement')
            },
            {
              href: 'departmentManagement',
              menuDisplay: 1,
              mobileDisplay: 0,
              icon: 'md-people',
              name: '部门管理',
              component: () => import('@/view/jurisdictionManage/departmentManagement')
            },
            {
              href: 'moduleManagement',
              menuDisplay: 1,
              mobileDisplay: 0,
              icon: 'md-cog',
              name: '模块管理',
              component: () => import('@/view/jurisdictionManage/moduleManagement')
            }
          ]
        },
        {
          href: '/',
          menuDisplay: 1,
          mobileDisplay: 0,
          icon: 'ios-color-filter',
          name: '绩效管理',
          children: [
            {
              href: 'positionSystem',
              menuDisplay: 1,
              mobileDisplay: 0,
              icon: 'md-swap',
              name: '绩效设置',
              component: () => import('@/view/jurisdictionManage/positionSystem')
            },
            {
              href: 'performanceProfileManage',
              menuDisplay: 1,
              mobileDisplay: 0,
              icon: 'ios-folder-open',
              name: '绩效档案',
              component: () => import('@/view/jurisdictionManage/performanceProfile')
            },
            {
              href: 'performanceStatistic',
              menuDisplay: 1,
              mobileDisplay: 0,
              icon: 'ios-analytics',
              name: '绩效统计',
              component: () => import('@/view/jurisdictionManage/performanceStatistic')
            }
          ]
        },
        {
          href: 'pushSet',
          name: 'pushSet',
          icon: 'ios-color-filter',
          name: '推送管理',
          menuDisplay: 1,
          mobileDisplay: 0,
          children: [
            {
              href: 'voyagePlanSet',
              name: '计划推送配置',
              icon: 'md-swap',
              menuDisplay: 1,
              mobileDisplay: 0,
              component: () => import('@/view/pushSet/voyagePlanSet')
            }
          ]
        }
      ]
    }
  },
  computed: {
    ...mapGetters([
      'errorCount'
    ]),
    collapsed: {
      get () {
        return this.$store.state.app.collapsed
      },
      set (val) {
        return val
      }
    },
    activeName () {
      if (this.$route.name === 'externalLink') { // 第三方引入页面特殊处理返回当前选中页签
        return 'isTurnByHref_' + this.$route.query.url
      }
      return this.$route.name
    },
    orgName () {
      return this.$store.state.user.orgName
    },
    access: {
      get () {
        return this.$store.state.user.access || Cookies.get('access') || []
      },
      set (val) {
        this.$emit('setAccess', val)
        this.$store.state.user.access = val
      }
    },
    tagNavList () {
      return this.$store.state.app.tagNavList
    },
    tagRouter () {
      return this.$store.state.app.tagRouter
    },
    menuShow () { // 移动端隐藏菜单项，但是绩效列表、绩效详情及绩效审批除外，出差人员需要在移动端审批
      let isMobile = (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini|Mobi/i.test(navigator.userAgent) || window.innerWidth < 500) && (this.$route.name !== 'performanceList' && this.$route.name !== 'performanceApprove' && this.$route.name !== 'performanceDetail')
      if (isMobile) return false
      return (getMenuShow() || this.$store.state.user.menuShow)
    },
    userAvatar () {
      return this.$store.state.user.avatarImgPath
    },
    cacheList () {
      const list = ['ParentView', ...this.tagNavList.length ? this.tagNavList.filter(item => !(item.meta && item.meta.notCache)).map(item => item.name) : []]
      return list
    },
    menuList () {
      if (this.$store.state.user.menuList.length > 0) {
        return JSON.parse(this.$store.state.user.menuList)
      }
      if (this.$route.name !== 'home' && !this.UNLOGIN_PAGE_LIST.includes(this.$route.name)) {
        this.$router.push({
          name: 'error_401'
        })
      } else {
        
      }
      return []
    },
    hasReadErrorPage () {
      return this.$store.state.app.hasReadErrorPage
    },
    unreadCount () {
      return this.$store.state.user.unreadCount
    }
  },
  methods: {
    ...mapMutations([
      'setBreadCrumb',
      'setTagNavList',
      'addTag',
      // 'setLocal',
      'setHomeRoute',
      'closeTag'
    ]),
    ...mapActions([
      'handleLogin',
      'handleLogOut',
      'getUnreadMessageCount'
    ]),
    turnToPage (route) {
      let { name, params, query } = {}
      if (typeof route === 'string') name = route
      else {
        name = route.name
        params = route.params
        query = route.query
      }
      if (name.indexOf('isTurnByHref_') > -1) {
        // window.open(name.split('_')[1])
        this.$router.push({
          name: 'externalLink',
          params: { url: name.split('_')[1] },
          query: { url: name.split('_')[1] }
        })
        return
      }
      this.$nextTick(() => {
        this.$router.push({
          name,
          params,
          query
        })
      })
    },
    // 开始查询
    searchVoyage (val) {
      if (val === '') return
      API.queryWaybillNumber({ number_no: val }).then(res => {
        if (res.data.Code === 10000) {
          if (res.data.Result.length === 0) {
            this.$Notice.warning({
              title: '无此单号',
              desc: '查无此单号信息,请确认单号信息是否正确!'
            })
          } else {
            localStorage.setItem('voyageObj', JSON.stringify(res.data.Result[0]))
            this.searchText = ''
            this.$router.push({
              name: 'searchInDetail',
              params: {
                id: val
              }
            })
          }
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    handleCollapsedChange (state) {
      this.collapsed = state
      this.$store.commit('setCollapsed', state)
      // localStorage.setItem('collapsed', state)
    },
    handleCloseTag (res, type, route) {
      if (type !== 'others') {
        if (type === 'all') {
          // this.turnToPage('home')
          this.turnToPage(this.$config.homeName)
        } else {
          if (routeEqual(this.$route, route)) {
            this.closeTag(route)
          }
        }
      }
      this.setTagNavList(res)
    },
    handleClick (item) {
      this.turnToPage(item)
    },
    // 修改密码
    changePwd () {
      this.pwdModal.modal = true
    },
    // 退出登录
    quitLogin () {
      this.handleLogOut().then(() => {
        this.$router.push({
          name: 'login'
        })
      })
    },
    // 选择数据同步日期
    changeSyncDate (val) {
      this.dataToDataParam = {
        date_begin: val[0],
        date_end: val[1]
      }
    },
    // 获取组织名称
    getOrgName () {
      queryOrgConfigName().then(res => {
        if (res.data.Code === 10000) {
          let _orgNameList = res.data.Result.map(item => { return item.departmentname })
          let _orgName = _orgNameList.join()
          localStorage.setItem('orgName', _orgName)
          this.$store.commit('setOrgName', _orgName)
        }
      })
    },
    // 数据同步
    dataToSyncData () {
      const msg = this.$Message.loading({
        content: '数据同步中...',
        duration: 0
      })
      let data = this.dataToDataParam
      dataToSync(data).then(res => {
        setTimeout(msg)
        if (res.data.Code === 10000) {
          this.$Message.success('同步成功')
        } else {
          this.$Message.error('同步失败')
        }
      })
    },
    // 跳转后台管理
    manageLogin () {
      // this.setManageRoute()
      this.$nextTick(() => {
        this.$router.push({
          name: 'userManagement'
        })
      })
    },
    // 设置管理员权限菜单路由
    setManageRoute (router_name) {
      this.$store.commit('setAccess', 'organizational')
      this.$store.commit('setMenuList', JSON.stringify(this.dynamicRouter))
    }
  },
  watch: {
    '$route' (newRoute) {
      const { name, query, params, meta } = newRoute
      this.addTag({
        route: { name, query, params, meta },
        type: 'push'
      })
      this.setBreadCrumb(newRoute)
      this.setTagNavList(getNewTagList(this.tagNavList, newRoute))
      this.access = Cookies.get('access')
      this.$refs.sideMenu.updateOpenName(newRoute.name)
      this.userDataSyncDate = (localStorage.getItem('userDataId') === '9195D8D08B35449CA4AB1E4F545BDDD1' && this.$route.path !== '/home' && Cookies.get('access') === 'materialPurchase')
      this.isManageLogin = ((localStorage.getItem('userDataId') === '9195D8D08B35449CA4AB1E4F545BDDD1' || (JSON.parse(localStorage.getItem('userData')).admin_rights && JSON.parse(localStorage.getItem('userData')).admin_rights === '1'))) //  && this.$route.path === '/home'
    }
  },
  mounted () {
    // this.collapsed = Boolean(localStorage.collapsed)
    /**
     * @description 初始化设置面包屑导航和标签导航
     */
    this.setTagNavList()
    this.setHomeRoute(routers)
    const { name, params, query, meta } = this.$route
    this.addTag({
      route: { name, params, query, meta }
    })
    this.setBreadCrumb(this.$route)
    // 设置初始语言
    // this.setLocal(this.$i18n.locale)
    // 如果当前打开页面不在标签栏中，跳到homeName页
    if (!this.tagNavList.find(item => item.name === this.$route.name)) {
      this.$router.push({
        name: this.$config.homeNamek
        // name: 'home'
      })
    }
    this.userDataSyncDate = localStorage.getItem('userDataId') === 'a5f4cc75010e44f4b8ec175b1db284ff' && this.$route.path !== '/home' && Cookies.get('access') === 'materialPurchase'
    this.isManageLogin = ((localStorage.getItem('userDataId') === '9195D8D08B35449CA4AB1E4F545BDDD1' || (JSON.parse(localStorage.getItem('userData')) && JSON.parse(localStorage.getItem('userData')).admin_rights === '1'))) //  && this.$route.path === '/home'
    // if (this.isManageLogin) {
    //   let isInRouter = getMenuIndex(this.dynamicRouter, name)
  //   if (isInRouter) {
    //     this.setManageRoute()
    //     this.$nextTick(() => {
    //       this.$router.push({
    //         name: name
    //       })
    //     })
    //   }
    // }
    // 获取未读消息条数
    // this.getUnreadMessageCount()
    // this.getOrgName() // 获取组织名称
  }
}
</script>

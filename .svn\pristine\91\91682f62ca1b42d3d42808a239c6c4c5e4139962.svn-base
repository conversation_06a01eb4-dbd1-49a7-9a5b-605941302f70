<template>
  <div ref="dom" class="charts chart-bar"></div>
</template>

<script>
import echarts from 'echarts'
import tdTheme from './theme.json'
import { on, off } from '@/libs/tools'
echarts.registerTheme('tdTheme', tdTheme)
export default {
  name: 'ChartBarVertical',
  props: {
    value: Object,
    text: String,
    subtext: String,
    unit: String,
    clickable: {
      type: Boolean,
      default: false
    },
    rotate: {
      type: String,
      default: '0'
    }
  },
  data () {
    return {
      dom: null
    }
  },
  methods: {
    chartInit () {
      let _that = this
      this.$nextTick(() => {
        let xAxisData = this.value.xAxis
        let seriesData = this.value.data
        let option = {
          title: {
            text: this.text,
            subtext: this.subtext,
            x: 'left'
          },
          legend: {
            type: 'scroll'
            // orient: 'vertical',
            // right: '0'
          },
          grid: {
            left: '0',
            right: '18%',
            bottom: '3%',
            containLabel: true
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: { // 坐标轴指示器，坐标轴触发有效
              type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
            },
            formatter: (item => {
              let itemIdx = item[0].name + '<br/>'
              item.map(val => {
                itemIdx += val.marker + val.seriesName + ': ' + val.value + '<br/>'
              })
              return itemIdx
            })
          },
          barMaxWidth: 20,
          xAxis: {
            type: 'value',
            name: this.unit,
            position: 'top'
          },
          yAxis: {
            type: 'category',
            data: xAxisData,
            inverse: true,
            axisLabel: {
              interval: 0,
              rotate: this.rotate
            },
            splitLine: {
              show: false // 取消网格线
            }
          },
          series: seriesData
          // series: [{
          //   data: seriesData,
          //   type: 'bar'
          // }]
        }
        this.dom = echarts.init(this.$refs.dom, 'tdTheme')
        this.dom.setOption(option)
        this.dom.off('click')
        this.dom.getZr().on('click', item => {
          let pointInPixel = [item.offsetX, item.offsetY]
          if (this.dom.containPixel('grid', pointInPixel)) {
            let xIndex = this.dom.convertFromPixel({ seriesIndex: 0 }, [item.offsetX, item.offsetY])[0]
            if (this.clickable) {
              _that.$emit('clickBack', xIndex)
            }
          }
        })
        on(window, 'resize', this.resize)
      })
    },
    resize () {
      this.dom.resize()
    }
  },
  mounted () {
    this.chartInit()
  },
  beforeDestroy () {
    off(window, 'resize', this.resize)
  },
  watch: {
    value: {
      handler (n, o) {
        this.chartInit()
      },
      deep: true,
      immediate: true
    }
  }
}
</script>

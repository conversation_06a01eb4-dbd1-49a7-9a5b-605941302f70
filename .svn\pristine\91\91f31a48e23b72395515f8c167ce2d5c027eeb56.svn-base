import ierp_axios from '@/libs/ierp.api.request'
import Qs from 'qs'
import config from '@/config'

// 获取船舶列表
export function shipQuery (data) {
  return ierp_axios.request({
    url: '/ierp/kapi/v2/xtgf/basedata/xtgf_bos_org_copy/ShipSearch',
    method: 'get',
    params: data
  })
}

// 船舶设备一级列表-通过船舶名称进行查询
export function equipListLevelThree (data) {
  return ierp_axios.request({
    url: '/ierp/kapi/v2/xtgf/basedata/bd_materialgroup/Equipmentsearch',
    method: 'get',
    params: data
  })
}

// 船舶设备二级/三级列表-通过船舶编码进行二级查询，设备二级编码查询三级
export function equipListLevelMore (data) {
  var statusVal = ''
  switch (data.status && data.status.length) {
    case 1:
      statusVal = 'status=' + data.status
      break
    case 2:
      statusVal = 'status=' + data.status[0] + '&status=' + data.status[1]
      break
    case 3:
      statusVal = 'status=' + data.status[0] + '&status=' + data.status[1] + '&status=' + data.status[2]
      break
    default:
      break
  }
  return ierp_axios.request({
    url: '/ierp/kapi/v2/xtgf/basedata/bd_materialgroup/Equipmentsearch2?' + statusVal,
    method: 'get',
    params: data
  })
}

// 船舶设备二级/三级列表-通过查询搜索
export function equipListLevelSearch (data) {
  var statusVal = ''
  switch (data.status && data.status.length) {
    case 1:
      statusVal = 'status=' + data.status
      break
    case 2:
      statusVal = 'status=' + data.status[0] + '&status=' + data.status[1]
      break
    case 3:
      statusVal = 'status=' + data.status[0] + '&status=' + data.status[1] + '&status=' + data.status[2]
      break
    default:
      break
  }
  return ierp_axios.request({
    url: '/ierp/kapi/v2/xtgf/basedata/bd_materialgroup/EquipmentLook?' + statusVal,
    method: 'get',
    params: data
  })
}

// 船舶设备备件详情查询
export function equipDetailGoodSelect (data) {
  var statusVal = ''
  switch (data.status && data.status.length) {
    case 1:
      statusVal = 'status=' + data.status
      break
    case 2:
      statusVal = 'status=' + data.status[0] + '&status=' + data.status[1]
      break
    case 3:
      statusVal = 'status=' + data.status[0] + '&status=' + data.status[1] + '&status=' + data.status[2]
      break
    default:
      break
  }
  return ierp_axios.request({
    url: '/ierp/kapi/v2/xtgf/basedata/bd_material/goodSearch?' + statusVal,
    method: 'get',
    params: data
  })
}

// 船舶设备备件详情搜索
export function equipDetailGoodSearch (data) {
  var statusVal = ''
  switch (data.status && data.status.length) {
    case 1:
      statusVal = 'status=' + data.status
      break
    case 2:
      statusVal = 'status=' + data.status[0] + '&status=' + data.status[1]
      break
    case 3:
      statusVal = 'status=' + data.status[0] + '&status=' + data.status[1] + '&status=' + data.status[2]
      break
    default:
      break
  }
  return ierp_axios.request({
    url: '/ierp/kapi/v2/xtgf/basedata/bd_material/materialLook2?' + statusVal,
    method: 'get',
    params: data
  })
}

<template>
  <div>
    <div class="picker-area">
      <div class="picker-list">
        <span>绩效月份：</span>
        <DatePicker type="month" v-if="type !== 'detail' && type !=='approve'" placeholder="请选择月份" style="width: 100px" format="yyyy-MM" :value="belong_month" @on-change="monthChange"></DatePicker>
        <span v-else style="font-weight: 100;">{{ belong_month }}</span>
      </div>
      <div class="picker-list">
        <span>模板：</span>
        {{ formTempName }}
      </div>
      <div class="back_btn" v-if="type === 'detail'">
        <Button @click="handleCancel">返回</Button>
      </div>
      <div class="back_btn" v-if="type === 'add' || type === 'modify'">
        <Button type="primary" @click="handleInport">导入</Button>
      </div>
    </div>
    <div class="approve-top">
      <div v-for="(item, idx) in flowObj.flowList" :key="item.unified_account_id">
        <span v-if="flowObj.flowList.length > 1" class="approve-top-pre" :class="flowColor(idx)">{{ idx > 0 ? '审' : '自'}}</span>
        <span class="approve-top-name">{{ item.user_name }}</span>
        <Icon v-if="idx < (flowObj.flowList.length - 1)" class="approve-top-arrow" type="md-arrow-forward" color="#797979" size="20" />
      </div>
    </div>
    <div class="approve-steps">
      <Tooltip max-width="200" v-for="(item, idx) in flowHisList" :key="item.flow_history_id" placement="left" theme="light">
        <div class="step-list" :class="idx === (flowHisList.length - 1) ? 'step-cur' : ''">{{ item.user_name.substring(0, 1) }}</div>
        <div slot="content">
          <span v-if="item.execute_code !== '1'">
            <span style="color: #57a3f3;">{{ item.user_name }}：</span>
            <span>{{ item.insert_time }}</span>
            <span style="margin-left: 5px; color: #57a3f3;">{{ item.execute_name }}</span>
            <span style="margin-left: 5px; color: red;">{{ item.execute_bak }}</span>
          </span>
          <span v-else>
            <span style="color: #57a3f3;">{{ item.user_name }}：</span>
            <span>{{ item.insert_time }}</span>
            <span style="margin-left: 5px; color: #57a3f3;">{{ item.execute_name }}</span>
          </span>
        </div>
      </Tooltip>
    </div>
    <!-- 工作效率工作开始 -->
    <Card style="width: calc(100% - 30px);">
      <h3>
        一、工作效率（50%）
        <Tooltip max-width="500">
          <Icon type="ios-alert" color="#57a3f3" size="16"/>
          <div slot="content" v-html="explain1"></div>
        </Tooltip>
        <span style="margin-left: 15px; color: red; font-size: 16px;">(注：ctrl + s可保存草稿)</span>
      </h3>
      <div class="table_scroll">
        <table class="table table-bordered" :style="(type === 'detail' || type === 'approve') ? 'width: 100%' : 'width: calc(100% - 55px)'">
          <!-- 表头开始 -->
          <tr>
            <th style="width: 50px;">序号</th>
            <th style="min-width: 200px;">具体细项</th>
            <th style="min-width: 200px;">具体要求</th>
            <th style="width: 80px;">考核周期</th>
            <th style="width: 125px;">加分/扣分规则</th>
            <th style="width: 50px;">自评得分</th>
            <th style="width: 50px;">复评得分</th>
            <th style="min-width: 150px;">备注</th>
          </tr>
          <!-- 表头结束 -->
          <tr v-for="(item, idx) in mainList1" :key="'key' + idx" :style="item.type === 'title' ? 'background: #f6f6f6' : ''">
            <td class="center" :style="item.type === 'title' ? 'font-weight: bold; background: #e7e7e7;' : ''">{{ idx + 1 }}</td>
            <td :colspan="item.type === 'title' ? 2 : 1" :class="item.type === 'title' ? 'title_font' : ''">
              <Input type="textarea" :autosize="{minRows: 1,maxRows: 5}" v-if="type === 'add' || type === 'modify'" v-model="item.content" @on-focus="getBlurIndex(1, 'content', idx)" />
              <span v-else>{{ item.content }}</span>
            </td>
            <td :colspan="item.type === 'title' ? 5 : 1" style="min-width: 200px;">
              <div v-if="item.type === 'title' && (type === 'add' || type === 'modify')">
                <Button type="primary" size="small" @click="addMainList(idx,1)">新增</Button>
                <Button type="error" size="small" style="margin-left: 10px;" @click="delMainList(idx, 1)" v-if="isMainLen1()">删除</Button>
              </div>
              <div v-else>
                <Input type="textarea" :autosize="{minRows: 1,maxRows: 5}" v-if="type === 'add' || type === 'modify' && item.type !== 'title'" v-model="item.target" @on-focus="getBlurIndex(1, 'target', idx)" />
                <span v-else>{{ item.target }}</span>
              </div>
            </td>
            <td v-if="item.type === 'detail'" style="width: 50px;" class="center">
              <Input type="textarea" :autosize="{minRows: 1,maxRows: 5}" v-if="type === 'add' || type === 'modify'" v-model="item.time" @on-focus="getBlurIndex(1, 'time', idx)" />
              <span v-else>{{ item.time }}</span>
            </td>
            <td v-if="item.type === 'detail'" style="width: 110px;" class="center">
              <Input type="textarea" :autosize="{minRows: 1,maxRows: 5}" v-if="type === 'add' || type === 'modify'" v-model="item.rule" @on-focus="getBlurIndex(1, 'rule', idx)" />
              <span v-else>{{ item.rule }}</span>
            </td>
            <td v-if="item.type === 'detail'" style="width: 50px;" class="center">
              <InputNumber style="width: 50px; height: 26px;" v-if="type === 'add' || type === 'modify'" :max="0" :min="-100" size="small" v-model="item.selfScore"></InputNumber>
              <span v-else>{{ item.selfScore }}</span>
            </td>
            <td v-if="item.type === 'detail'" style="width: 50px;" class="center">
              <InputNumber style="width: 50px; height: 26px;" v-if="type === 'approve'" :max="0" :min="-100" size="small" v-model="item.supScore"></InputNumber>
              <span v-else>{{ item.supScore }}</span>
            </td>
            <td v-if="item.type === 'detail'" style="min-width: 150px;" class="center">
              <Input type="textarea" :autosize="{minRows: 1,maxRows: 5}" v-if="type === 'add' || type === 'modify' || type === 'approve'" v-model="item.remark" />
              <span v-else>{{ item.remark }}</span>
            </td>
            <Button class="temp1_add_btn" size="small" type="primary" v-if="item.type !== 'title' && (type === 'add' || type === 'modify')" @click="addMainLine(idx, 1)">+</Button>
            <Button v-if="item.type !== 'title' && isMainLineLen1(idx) && (type === 'add' || type === 'modify')" class="remove_btn" size="small" type="error" @click="delMainLine(idx, 1)">-</Button>
          </tr>
          <!-- 第一项得分汇总 -->
          <tr style="background: #f6f6f6; height: 35px;font-weight: bold;">
            <td class="center">{{ mainList1.length + 1 }}</td>
            <td colspan="2" class="center">第一项总得分（50分）</td>
            <td class="center">月</td>
            <td></td>
            <td class="center">{{ getSelfScore(1) }}</td>
            <td class="center">{{ getSupScore(1) }}</td>
            <td></td>
          </tr>
        </table>
      </div>
    </Card>
    <!-- 工作效率结束 -->

    <!-- 工作质量开始 -->
    <Card style="margin-top: 20px;width: calc(100% - 30px);">
      <h3>
        二、工作质量（40%）
        <Tooltip max-width="500">
          <Icon type="ios-alert" color="#57a3f3" size="16"/>
          <div slot="content" v-html="explain2"></div>
        </Tooltip>
      </h3>
      <div class="table_scroll">
        <table class="table table-bordered" :style="(type === 'detail' || type === 'approve') ? 'width: 100%' : 'width: calc(100% - 55px)'">
          <!-- 表头开始 -->
          <tr>
            <th style="width: 50px;">序号</th>
            <th style="min-width: 200px;">具体细项</th>
            <th style="min-width: 200px;">具体要求</th>
            <th style="width: 80px;">考核周期</th>
            <th style="width: 125px;">加分/扣分规则</th>
            <th style="width: 50px;">自评得分</th>
            <th style="width: 50px;">复评得分</th>
            <th style="min-width: 150px;">备注</th>
          </tr>
          <!-- 表头结束 -->
          <tr v-for="(item, idx) in mainList2" :key="'key' + idx" :style="item.type === 'title' ? 'background: #f6f6f6' : ''">
            <td class="center" :style="item.type === 'title' ? 'font-weight: bold; background: #e7e7e7;' : ''">{{ idx + 1 }}</td>
            <td :colspan="item.type === 'title' ? 2 : 1" :class="item.type === 'title' ? 'title_font' : ''">
              <Input type="textarea" :autosize="{minRows: 1,maxRows: 5}" v-if="type === 'add' || type === 'modify'" v-model="item.content" @on-focus="getBlurIndex(2, 'content', idx)" />
              <span v-else>{{ item.content }}</span>
            </td>
            <td :colspan="item.type === 'title' ? 5 : 1" style="min-width: 200px;">
              <div v-if="item.type === 'title' && (type === 'add' || type === 'modify')">
                <Button type="primary" size="small" @click="addMainList(idx, 2)">新增</Button>
                <Button type="error" size="small" style="margin-left: 10px;" @click="delMainList(idx, 2)" v-if="isMainLen2()">删除</Button>
              </div>
              <div v-else>
                <Input type="textarea" :autosize="{minRows: 1,maxRows: 5}" v-if="type === 'add' || type === 'modify' && item.type !== 'title'" v-model="item.target" @on-focus="getBlurIndex(2, 'target', idx)" />
                <span v-else>{{ item.target }}</span>
              </div>
            </td>
            <td v-if="item.type === 'detail'" style="width: 50px;" class="center">
              <Input type="textarea" :autosize="{minRows: 1,maxRows: 5}" v-if="type === 'add' || type === 'modify'" v-model="item.time" @on-focus="getBlurIndex(2, 'time', idx)" />
              <span v-else>{{ item.time }}</span>
            </td>
            <td v-if="item.type === 'detail'" style="width: 110px;" class="center">
              <Input type="textarea" :autosize="{minRows: 1,maxRows: 5}" v-if="type === 'add' || type === 'modify'" v-model="item.rule" @on-focus="getBlurIndex(2, 'rule', idx)" />
              <span v-else>{{ item.rule }}</span>
            </td>
            <td v-if="item.type === 'detail'" style="width: 50px;" class="center">
              <InputNumber style="width: 50px; height: 26px;" v-if="type === 'add' || type === 'modify'" :max="0" :min="-100" size="small" v-model="item.selfScore"></InputNumber>
              <span v-else>{{ item.selfScore }}</span>
            </td>
            <td v-if="item.type === 'detail'" style="width: 50px;" class="center">
              <InputNumber style="width: 50px; height: 26px;" v-if="type === 'approve'" :max="0" :min="-100" size="small" v-model="item.supScore"></InputNumber>
              <span v-else>{{ item.supScore }}</span>
            </td>
            <td v-if="item.type === 'detail'" style="min-width: 150px;" class="center">
              <Input type="textarea" :autosize="{minRows: 1,maxRows: 5}" v-if="type === 'add' || type === 'modify' || type === 'approve'" v-model="item.remark" />
              <span v-else>{{ item.remark }}</span>
            </td>
            <Button class="temp1_add_btn" size="small" type="primary" v-if="item.type !== 'title' && (type === 'add' || type === 'modify')" @click="addMainLine(idx, 2)">+</Button>
            <Button v-if="item.type !== 'title' && isMainLineLen2(idx) && (type === 'add' || type === 'modify')" class="remove_btn" size="small" type="error" @click="delMainLine(idx, 2)">-</Button>
          </tr>
          <!-- 第二项得分汇总 -->
          <tr style="background: #f6f6f6; height: 35px;font-weight: bold;">
            <td class="center">{{ mainList2.length + 1 }}</td>
            <td colspan="2" class="center">第二项总得分（40分）</td>
            <td class="center">月</td>
            <td></td>
            <td class="center">{{ getSelfScore(2) }}</td>
            <td class="center">{{ getSupScore(2) }}</td>
            <td></td>
          </tr>
        </table>
      </div>
    </Card>
    <!-- 工作质量结束 -->

    <!-- 日常事务开始 -->
    <Card style="margin-top: 20px;width: calc(100% - 30px);">
      <h3>
        三、日常事务（10%）
        <Tooltip max-width="500">
          <Icon type="ios-alert" color="#57a3f3" size="16"/>
          <div slot="content" v-html="explain3"></div>
        </Tooltip>
      </h3>
      <div class="table_scroll">
        <table class="table table-bordered" :style="(type === 'detail' || type === 'approve') ? 'width: 100%' : 'width: calc(100% - 55px)'">
          <!-- 表头开始 -->
          <tr>
            <th style="width: 50px;">序号</th>
            <th style="min-width: 200px;">具体细项</th>
            <th style="min-width: 200px;">具体要求</th>
            <th style="width: 80px;">考核周期</th>
            <th style="width: 125px;">加分/扣分规则</th>
            <th style="width: 50px;">自评得分</th>
            <th style="width: 50px;">复评得分</th>
            <th style="min-width: 150px;">备注</th>
          </tr>
          <!-- 表头结束 -->
          <tr v-for="(item, idx) in mainList3" :key="'key' + idx" :style="item.type === 'title' ? 'background: #f6f6f6' : ''">
            <td class="center" :style="item.type === 'title' ? 'font-weight: bold; background: #e7e7e7;' : ''">{{ idx + 1 }}</td>
            <td :colspan="item.type === 'title' ? 2 : 1" :class="item.type === 'title' ? 'title_font' : ''">
              <Input type="textarea" :autosize="{minRows: 1,maxRows: 5}" v-if="type === 'add' || type === 'modify'" v-model="item.content" @on-focus="getBlurIndex(3, 'content', idx)" />
              <span v-else>{{ item.content }}</span>
            </td>
            <td :colspan="item.type === 'title' ? 5 : 1" style="min-width: 200px;">
              <div v-if="item.type === 'title' && (type === 'add' || type === 'modify')">
                <Button type="primary" size="small" @click="addMainList(idx, 3)">新增</Button>
                <Button type="error" size="small" style="margin-left: 10px;" @click="delMainList(idx, 3)" v-if="isMainLen3()">删除</Button>
              </div>
              <div v-else>
                <Input type="textarea" :autosize="{minRows: 1,maxRows: 5}" v-if="type === 'add' || type === 'modify' && item.type !== 'title'" v-model="item.target" @on-focus="getBlurIndex(3, 'target', idx)" />
                <span v-else>{{ item.target }}</span>
              </div>
            </td>
            <td v-if="item.type === 'detail'" style="width: 50px;" class="center">
              <Input type="textarea" :autosize="{minRows: 1,maxRows: 5}" v-if="type === 'add' || type === 'modify'" v-model="item.time" @on-focus="getBlurIndex(3, 'time', idx)" />
              <span v-else>{{ item.time }}</span>
            </td>
            <td v-if="item.type === 'detail'" style="width: 110px;" class="center">
              <Input type="textarea" :autosize="{minRows: 1,maxRows: 5}" v-if="type === 'add' || type === 'modify'" v-model="item.rule" @on-focus="getBlurIndex(3, 'rule', idx)" />
              <span v-else>{{ item.rule }}</span>
            </td>
            <td v-if="item.type === 'detail'" style="width: 50px;" class="center">
              <InputNumber style="width: 50px; height: 26px;" v-if="type === 'add' || type === 'modify'" :max="0" :min="-100" size="small" v-model="item.selfScore"></InputNumber>
              <span v-else>{{ item.selfScore }}</span>
            </td>
            <td v-if="item.type === 'detail'" style="width: 50px;" class="center">
              <InputNumber style="width: 50px; height: 26px;" v-if="type === 'approve'" :max="0" :min="-100" size="small" v-model="item.supScore"></InputNumber>
              <span v-else>{{ item.supScore }}</span>
            </td>
            <td v-if="item.type === 'detail'" style="min-width: 150px;" class="center">
              <Input type="textarea" :autosize="{minRows: 1,maxRows: 5}" v-if="type === 'add' || type === 'modify' || type === 'approve'" v-model="item.remark" />
              <span v-else>{{ item.remark }}</span>
            </td>
            <Button class="temp1_add_btn" size="small" type="primary" v-if="item.type !== 'title' && (type === 'add' || type === 'modify')" @click="addMainLine(idx, 3)">+</Button>
            <Button v-if="item.type !== 'title' && isMainLineLen3(idx) && (type === 'add' || type === 'modify')" class="remove_btn" size="small" type="error" @click="delMainLine(idx, 3)">-</Button>
          </tr>
          <!-- 第二项得分汇总 -->
          <tr style="background: #f6f6f6; height: 35px;font-weight: bold;">
            <td class="center">{{ mainList3.length + 1 }}</td>
            <td colspan="2" class="center">第三项总得分（10分）</td>
            <td class="center">月</td>
            <td></td>
            <td class="center">{{ getSelfScore(3) }}</td>
            <td class="center">{{ getSupScore(3) }}</td>
            <td></td>
          </tr>
        </table>
      </div>
    </Card>
    <!-- 日常事务结束 -->

    <!-- 学习与成长开始 -->
    <Card style="margin-top: 20px; margin-bottom: 60px;width: calc(100% - 30px);">
      <h3>
        四、学习与成长（10%）
        <Tooltip max-width="500">
          <Icon type="ios-alert" color="#57a3f3" size="16"/>
          <div slot="content" v-html="explain4"></div>
        </Tooltip>
      </h3>
      <div class="table_scroll">
        <table class="table table-bordered" :style="(type === 'detail' || type === 'approve') ? 'width: 100%' : 'width: calc(100% - 55px)'">
          <!-- 表头开始 -->
          <tr>
            <th style="width: 50px;">序号</th>
            <th style="min-width: 200px;">具体细项</th>
            <th style="min-width: 200px;">具体要求</th>
            <th style="width: 80px;">考核周期</th>
            <th style="width: 125px;">加分/扣分规则</th>
            <th style="width: 50px;">自评得分</th>
            <th style="width: 50px;">复评得分</th>
            <th style="min-width: 150px;">备注</th>
          </tr>
          <!-- 表头结束 -->
          <tr v-for="(item, idx) in mainList4" :key="'key' + idx" :style="item.type === 'title' ? 'background: #f6f6f6' : ''">
            <td class="center" :style="item.type === 'title' ? 'font-weight: bold; background: #e7e7e7;' : ''">{{ idx + 1 }}</td>
            <td :colspan="item.type === 'title' ? 2 : 1" :class="item.type === 'title' ? 'title_font' : ''">
              <Input type="textarea" :autosize="{minRows: 1,maxRows: 5}" v-if="type === 'add' || type === 'modify'" v-model="item.content" @on-focus="getBlurIndex(4, 'content', idx)" />
              <span v-else>{{ item.content }}</span>
            </td>
            <td :colspan="item.type === 'title' ? 5 : 1" style="min-width: 200px;">
              <div v-if="item.type === 'title' && (type === 'add' || type === 'modify')">
                <Button type="primary" size="small" @click="addMainList(idx, 4)">新增</Button>
                <Button type="error" size="small" style="margin-left: 10px;" @click="delMainList(idx, 4)" v-if="isMainLen4()">删除</Button>
              </div>
              <div v-else>
                <Input type="textarea" :autosize="{minRows: 1,maxRows: 5}" v-if="type === 'add' || type === 'modify' && item.type !== 'title'" v-model="item.target" @on-focus="getBlurIndex(4, 'target', idx)" />
                <span v-else>{{ item.target }}</span>
              </div>
            </td>
            <td v-if="item.type === 'detail'" style="width: 50px;" class="center">
              <Input type="textarea" :autosize="{minRows: 1,maxRows: 5}" v-if="type === 'add' || type === 'modify'" v-model="item.time" @on-focus="getBlurIndex(4, 'time', idx)" />
              <span v-else>{{ item.time }}</span>
            </td>
            <td v-if="item.type === 'detail'" style="width: 110px;" class="center">
              <Input type="textarea" :autosize="{minRows: 1,maxRows: 5}" v-if="type === 'add' || type === 'modify'" v-model="item.rule" @on-focus="getBlurIndex(4, 'rule', idx)" />
              <span v-else>{{ item.rule }}</span>
            </td>
            <td v-if="item.type === 'detail'" style="width: 50px;" class="center">
              <InputNumber style="width: 50px; height: 26px;" v-if="type === 'add' || type === 'modify'" :max="100" :min="0" size="small" v-model="item.selfScore"></InputNumber>
              <span v-else>{{ item.selfScore }}</span>
            </td>
            <td v-if="item.type === 'detail'" style="width: 50px;" class="center">
              <InputNumber style="width: 50px; height: 26px;" v-if="type === 'approve'" :max="100" :min="0" size="small" v-model="item.supScore"></InputNumber>
              <span v-else>{{ item.supScore }}</span>
            </td>
            <td v-if="item.type === 'detail'" style="min-width: 150px;" class="center">
              <Input type="textarea" :autosize="{minRows: 1,maxRows: 5}" v-if="type === 'add' || type === 'modify' || type === 'approve'" v-model="item.remark" />
              <span v-else>{{ item.remark }}</span>
            </td>
            <Button class="temp1_add_btn" size="small" type="primary" v-if="item.type !== 'title' && (type === 'add' || type === 'modify')" @click="addMainLine(idx, 4)">+</Button>
            <Button v-if="item.type !== 'title' && isMainLineLen4(idx) && (type === 'add' || type === 'modify')" class="remove_btn" size="small" type="error" @click="delMainLine(idx, 4)">-</Button>
          </tr>
          <!-- 第二项得分汇总 -->
          <tr style="background: #f6f6f6; height: 35px;font-weight: bold;">
            <td class="center">{{ mainList4.length + 1 }}</td>
            <td colspan="2" class="center">加分项（10分）</td>
            <td class="center">月</td>
            <td></td>
            <td class="center">{{ getSelfScore(4) }}</td>
            <td class="center">{{ getSupScore(4) }}</td>
            <td></td>
          </tr>
          <tr class="center" style="background: #f6f6f6; height: 40px;font-weight: bold;">
            <td >合计</td>
            <td colspan="3"></td>
            <td>总分100%+10%</td>
            <td class="center">{{ selfTotalScore() }}</td>
            <td class="center">{{ supTotalScore() }}</td>
            <td></td>
          </tr>
        </table>
      </div>
    </Card>
    <!-- 学习与成长结束 -->
    <div class="btn-area" v-if="type === 'add' || type === 'modify'">
      <Button @click="handleCancel">返回</Button>
      <Button type="primary" @click="handleJustSave">保存</Button>
      <Button type="primary" @click="handleSave">提交</Button>
    </div>
    <div class="btn-area" v-if="type === 'approve'">
      <Button @click="handleCancel">返回</Button>
      <Button @click="handleApprove(-1)">退回修改</Button>
      <Button type="primary" @click="handleApprove(1)">提交</Button>
    </div>
    <ImportModal :modalData="importData" @ImportBack="importBackData"></ImportModal>
  </div>
</template>
<script>
import API from '@/api/performance'
import ImportModal from './importModal'

export default ({
  components: {
    ImportModal
  },
  data () {
    return {
      formTempName: '财务部模板', // 模板名称
      explain1: '按时完成岗位职责，确保财务工作的及时性',
      explain2: '按照上市公司的标准和要求履行职责，维护公司良好形象',
      explain3: '按照公司规章制度，完成日常性工作事务',
      explain4: '积极参加财税业务培训，鼓励创新，不断提高专业化和职业化',
      importData: {
        modal: false,
        title: '导入绩效信息'
      },
      detailObj: {}, // 详情数据
      flowObj: [], // 流程人员列表
      flowHisList: [], // 流程流转记录
      pasteMainIdx: 1, // 复制粘贴用，判断是第几个数组
      pasteStr: '', // 复制粘贴用，判断是哪一项复制操作
      pasteLine: '', // 复制粘贴用，判断从哪一行开始
      mainList1: [ // 工作效率数据
        { content: '', type: 'title' },
        { content: '', target: '', time: '', rule: '', selfScore: 0, supScore: 0, remark: '', type: 'detail' },
        { content: '', target: '', time: '', rule: '', selfScore: 0, supScore: 0, remark: '', type: 'detail' }
      ],
      mainList2: [ // 工作质量数据
        { content: '', type: 'title' },
        { content: '', target: '', time: '', rule: '', selfScore: 0, supScore: 0, remark: '', type: 'detail' },
        { content: '', target: '', time: '', rule: '', selfScore: 0, supScore: 0, remark: '', type: 'detail' }
      ],
      mainList3: [ // 日常事务数据
        { content: '', type: 'title' },
        { content: '', target: '', time: '', rule: '', selfScore: 0, supScore: 0, remark: '', type: 'detail' },
        { content: '', target: '', time: '', rule: '', selfScore: 0, supScore: 0, remark: '', type: 'detail' }
      ],
      mainList4: [ // 学习与成长数据
        { content: '', type: 'title' },
        { content: '', target: '', time: '', rule: '', selfScore: 0, supScore: 0, remark: '', type: 'detail' },
        { content: '', target: '', time: '', rule: '', selfScore: 0, supScore: 0, remark: '', type: 'detail' }
      ],
      isLastApprove: false, // 本流程最后一个审批人
      isFirstMember: false, // 本流程第一个提交人
      type: '',
      form_id: '',
      sysUser: '',
      approveIdx: 0, // 審批流程位置
      sysIdx: null, // 當前人員基於流程位置
      belong_month: '', // 月份
      is_finish: '0', // 表单状态（0拟稿，1审核中，2完结）
      isComplete: false, // 流程是否審批完成
      scoreObj: { selfScore1: 0, supScore1: 0, selfScore2: 0, supScore2: 0, selfScore3: 0, supScore3: 0, selfScore4: 0, supScore4: 0 }, // 每项评分内容
      self_evaluate_score: 0, // 自评得分
      re_evaluate_score: 0, // 复评得分
      final_score: 0 // 最终得分
    }
  },
  methods: {
    getFormDetail () { // 获取详情
      // 获取表格详情
      API.getPerfFormInfo({ form_id: this.form_id }).then(res => {
        if (res.data.Code === 10000) {
          let _formObj = res.data.Result[0].form_json
          this.is_finish = res.data.Result[0].is_finish
          if (res.data.Result[0].is_finish === '2') {
            this.isComplete = true
          }
          this.belong_month = res.data.Result[0].belong_month
          if (JSON.stringify(_formObj) === '{}') return
          this.detailObj = res.data.Result[0]
          this.mainList1 = _formObj.mainList1
          this.mainList2 = _formObj.mainList2
          this.mainList3 = _formObj.mainList3
          this.mainList4 = _formObj.mainList4
          this.flowObj.flowList = _formObj.flowList
          // 获取流转内容
          API.queryPerfFlowHistoryList({
            form_id: this.form_id
          }).then(res => {
            if (res.data.Code === 10000) {
              this.flowHisList = res.data.Result
              if (this.flowHisList.length > 0) {
                this.approveIdx = this.flowObj.flowList.findIndex(item => item.unified_account_id === this.flowHisList[this.flowHisList.length - 1].unified_account_id)
              }
            }
          })
          if (this.flowObj.flowList && this.flowObj.flowList.length > 0) {
            // this.flowObj.flowList.reverse() // 数组倒序
            this.sysIdx = this.flowObj.flowList.findIndex(item => item.user_name === this.sysUser)
            if (this.sysIdx < (this.flowObj.flowList.length - 1)) {
              this.next_unified_account_id = this.flowObj.flowList[this.sysIdx + 1].unified_account_id
            } else {
              this.next_unified_account_id = ''
            }
            if (_formObj.flowList[_formObj.flowList.length - 1].unified_account_id === JSON.parse(localStorage.getItem('userData')).unified_account_id) {
              this.isLastApprove = true
            } else {
              this.isLastApprove = false
            }
            if (_formObj.flowList[0].unified_account_id === JSON.parse(localStorage.getItem('userData')).unified_account_id) {
              this.isFirstMember = true
              if (this.type === 'detail' && this.is_finish !== '2') {
                this.backResetSupData('self')
              }
            } else {
              this.isFirstMember = false
              if (this.type === 'approve' && this.is_finish === '1' && !this.isLastApprove) { // 当是审批情况下且最后得分为0  复制自评人分数给复评人
                this.resetJsonData()
              }
            }
          }
        }
      })
    },
    backResetSupData (str) {
      this.mainList1.map(item => {
        if (item.type === 'detail') {
          item.supScore = 0
          if (str === 'import') item.remark = ''
        }
      })
      this.mainList2.map(item => {
        if (item.type === 'detail') {
          item.supScore = 0
          if (str === 'import') item.remark = ''
        }
      })
      this.mainList3.map(item => {
        if (item.type === 'detail') {
          item.supScore = 0
          if (str === 'import') item.remark = ''
        }
      })
      this.mainList4.map(item => {
        if (item.type === 'detail') {
          item.supScore = 0
          if (str === 'import') item.remark = ''
        }
      })
    },
    // 审批时设置上级评分同步
    resetJsonData () {
      this.mainList1.map(item => {
        if (item.type === 'detail') {
          item.supScore = item.selfScore
        }
      })
      this.mainList2.map(item => {
        if (item.type === 'detail') {
          item.supScore = item.selfScore
        }
      })
      this.mainList3.map(item => {
        if (item.type === 'detail') {
          item.supScore = item.selfScore
        }
      })
      this.mainList4.map(item => {
        if (item.type === 'detail') {
          item.supScore = item.selfScore
        }
      })
    },
    // 仅保存草稿
    handleJustSave () {
      if (this.type === 'approve') return
      if (this.belong_month === '') {
        this.$Message.warning('请选择绩效考核时间再提交！')
        return
      }
      let _formJson = {
        formType: '3', // 财务部模板，绩效档案识别用
        mainList1: this.mainList1,
        mainList2: this.mainList2,
        mainList3: this.mainList3,
        mainList4: this.mainList4,
        flowList: this.flowObj.flowList
      }
      API.addOrUpdatePerfForm({
        form_id: this.form_id,
        unified_account_id: JSON.parse(localStorage.getItem('userData')).unified_account_id,
        dept_flow_id: JSON.parse(localStorage.getItem('userFlow')).dept_flow_id,
        belong_month: this.belong_month,
        self_evaluate_score: this.self_evaluate_score,
        re_evaluate_score: this.re_evaluate_score,
        form_json: JSON.stringify(_formJson)
      }).then(res => {
        if (res.data.Code === 10000) {
          this.form_id = res.data.Result
          this.$Message.success('表单保存成功！')
        } else {
          this.$Message.warning(res.data.Message)
        }
      })
    },
    handleSave () { // 保存
      let _formJson = {
        formType: '3', // 财务部模板，绩效档案识别用
        mainList1: this.mainList1,
        mainList2: this.mainList2,
        mainList3: this.mainList3,
        mainList4: this.mainList4,
        flowList: this.flowObj.flowList
      }
      this.$Modal.confirm({
        title: '提示',
        content: '<p>是否保存并提交绩效审核？</p>',
        loading: true,
        okText: '提交',
        cancelText: '不提交',
        onOk: () => {
          API.submitPerfForm({
            form_id: this.form_id,
            draft_unified_account_id: this.flowHisList.length > 0 ? this.flowHisList[0].unified_account_id : JSON.parse(localStorage.getItem('userData')).unified_account_id,
            current_unified_account_id: JSON.parse(localStorage.getItem('userData')).unified_account_id,
            next_unified_account_id: this.next_unified_account_id,
            dept_flow_id: JSON.parse(localStorage.getItem('userFlow')).dept_flow_id,
            belong_month: this.belong_month,
            self_evaluate_score: this.self_evaluate_score,
            re_evaluate_score: this.re_evaluate_score,
            form_json: JSON.stringify(_formJson),
            execute_code: 1
          }).then(res => {
            this.loading = false
            this.$Modal.remove()
            if (res.data.Code === 10000) {
              this.$Message.success(res.data.Message)
              this.$router.push({
                name: 'performanceList'
              })
            } else {
              this.$Message.warning(res.data.Message)
            }
          })
        },
        onCancel: () => {
          API.addOrUpdatePerfForm({
            form_id: this.form_id,
            unified_account_id: JSON.parse(localStorage.getItem('userData')).unified_account_id,
            dept_flow_id: JSON.parse(localStorage.getItem('userFlow')).dept_flow_id,
            belong_month: this.belong_month,
            self_evaluate_score: this.self_evaluate_score,
            re_evaluate_score: this.re_evaluate_score,
            form_json: JSON.stringify(_formJson)
          }).then(res => {
            if (res.data.Code === 10000) {
              this.form_id = res.data.Result
              this.loading = false
              this.$Modal.remove()
              this.$Message.success(res.data.Message)
              this.$router.push({
                name: 'performanceList'
              })
            } else {
              this.$Message.warning(res.data.Message)
            }
          })
        }
      })
    },
    // 绩效审批
    handleApprove (execute_code) {
      let backStr = execute_code === 1 ? '是否确认提交绩效审批？' : '是否确认退回修改'
      let _formJson = {
        formType: '3', // 财务部模板，绩效档案识别用
        mainList1: this.mainList1,
        mainList2: this.mainList2,
        mainList3: this.mainList3,
        mainList4: this.mainList4,
        flowList: this.flowObj.flowList
      }
      let execute_bak = ''
      this.$Modal.confirm({
        title: '提示',
        content: '<p>' + backStr + '</p>',
        loading: true,
        okText: execute_code === 1 ? '提交' : '确定',
        cancelText: '取消',
        render: (h) => {
          if(execute_code !== 1) {
            return h('Input', {
              props: {
                value: execute_bak,
                autofocus: true,
                placeholder: '请输入退回备注'
              },
              on: {
                input: (val) => {
                  execute_bak = val
                }
              }
            })
          } else {
            return h('div', {},  backStr)
          }
        },
        onOk: () => {
          API.submitPerfForm({
            form_id: this.form_id,
            draft_unified_account_id: this.flowHisList[0].unified_account_id,
            current_unified_account_id: JSON.parse(localStorage.getItem('userData')).unified_account_id,
            next_unified_account_id: execute_code === -1 ? this.flowHisList[0].unified_account_id : this.next_unified_account_id,
            dept_flow_id: this.detailObj.dept_flow_id,
            belong_month: this.belong_month,
            self_evaluate_score: this.self_evaluate_score,
            re_evaluate_score: this.re_evaluate_score,
            final_score: this.final_score,
            execute_code: execute_code,
            execute_bak: execute_bak,
            form_json: JSON.stringify(_formJson)
          }).then(res => {
            this.loading = false
            this.$Modal.remove()
            if (res.data.Code === 10000) {
              this.$Message.success(res.data.Message)
              this.$router.push({
                name: 'performanceApprove'
              })
            } else {
              this.$Message.warning(res.data.Message)
            }
          })
        }
      })
    },
    // 判断光标位置 存储位置
    getBlurIndex (mainIdx, str, lineIdx) {
      this.pasteMainIdx = mainIdx
      this.pasteStr = str
      this.pasteLine = lineIdx
    },
    async handleEvent (event) {
      if (event.ctrlKey && event.code === 'KeyS') {
        event.preventDefault()
        event.returnValue = false
        if (this.type === 'detail') return
        this.handleJustSave()
      }
    },
    // 计算当前项自评总扣分
    getSelfScore (tableIdx) {
      let backSelfScore = 0
      let _addScore = 0
      if (tableIdx === 1) {
        this.mainList1.forEach(item => {
          backSelfScore += item.selfScore ? item.selfScore : 0
        })
        this.scoreObj.selfScore1 = backSelfScore
        _addScore = 50
      }
      if (tableIdx === 2) {
        this.mainList2.forEach(item => {
          backSelfScore += item.selfScore ? item.selfScore : 0
        })
        this.scoreObj.selfScore2 = backSelfScore
        _addScore = 40
      }
      if (tableIdx === 3) {
        this.mainList3.forEach(item => {
          backSelfScore += item.selfScore ? item.selfScore : 0
        })
        this.scoreObj.selfScore3 = backSelfScore
        _addScore = 10
      }
      if (tableIdx === 4) {
        this.mainList4.forEach(item => {
          backSelfScore += item.selfScore ? item.selfScore : 0
        })
        this.scoreObj.selfScore4 = backSelfScore
      }
      return backSelfScore + _addScore
    },
    // 计算当前项复评总扣分
    getSupScore (tableIdx) {
      let backSupScore = 0
      let _addScore = 0
      if (tableIdx === 1) {
        this.mainList1.forEach(item => {
          backSupScore += item.supScore ? item.supScore : 0
        })
        this.scoreObj.supScore1 = backSupScore
        if (this.type === 'add' || this.type === 'modify' || (this.isFirstMember && this.is_finish !== '2')) {
          _addScore = 0
        } else {
          _addScore = 50
        }
      }
      if (tableIdx === 2) {
        this.mainList2.forEach(item => {
          backSupScore += item.supScore ? item.supScore : 0
        })
        this.scoreObj.supScore2 = backSupScore
        if (this.type === 'add' || this.type === 'modify' || (this.isFirstMember && this.is_finish !== '2')) {
          _addScore = 0
        } else {
          _addScore = 40
        }
      }
      if (tableIdx === 3) {
        this.mainList3.forEach(item => {
          backSupScore += item.supScore ? item.supScore : 0
        })
        this.scoreObj.supScore3 = backSupScore
        if (this.type === 'add' || this.type === 'modify' || (this.isFirstMember && this.is_finish !== '2')) {
          _addScore = 0
        } else {
          _addScore = 10
        }
      }
      if (tableIdx === 4) {
        this.mainList4.forEach(item => {
          backSupScore += item.supScore ? item.supScore : 0
        })
        this.scoreObj.supScore4 = backSupScore
      }
      return backSupScore + _addScore
    },
    // 计算自评总分
    selfTotalScore () {
      this.self_evaluate_score = this.scoreObj.selfScore1 + this.scoreObj.selfScore2 + this.scoreObj.selfScore3 + this.scoreObj.selfScore4 + 100
      return (this.scoreObj.selfScore1 + this.scoreObj.selfScore2 + this.scoreObj.selfScore3 + this.scoreObj.selfScore4 + 100)
    },
    // 计算复评部分
    supTotalScore () {
      let _addScore = 0
      if (this.type === 'add' || this.type === 'modify' || (this.isFirstMember && this.is_finish !== '2')) {
        _addScore = 0
      } else {
        _addScore = 100
      }
      this.re_evaluate_score = this.scoreObj.supScore1 + this.scoreObj.supScore2 + this.scoreObj.supScore3 + this.scoreObj.supScore4 + _addScore
      this.final_score = this.re_evaluate_score
      return (this.scoreObj.supScore1 + this.scoreObj.supScore2 + this.scoreObj.supScore3 + this.scoreObj.supScore4 + _addScore)
    },
    // 新增工作效率主项
    addMainList (idx, tableIdx) {
      if (tableIdx === 1) {
        this.mainList1.some((item, index) => {
          if (index > idx && item.type === 'title') {
            this.mainList1.splice(index, 0, 
              { content: '', type: 'title' },
              { content: '', target: '', time: '', rule: '', selfScore: 0, supScore: 0, remark: '', type: 'detail' },
              { content: '', target: '', time: '', rule: '', selfScore: 0, supScore: 0, remark: '', type: 'detail' }
            )
            return true
          } else if (index === this.mainList1.length - 1) {
            this.mainList1.splice(index + 1, 0, 
              { content: '', type: 'title' },
              { content: '', target: '', time: '', rule: '', selfScore: 0, supScore: 0, remark: '', type: 'detail' },
              { content: '', target: '', time: '', rule: '', selfScore: 0, supScore: 0, remark: '', type: 'detail' }
            )
            return true
          }
        })
        
        // this.mainList1.push(
        //   { content: '', type: 'title' },
        //   { content: '', target: '', time: '', rule: '', selfScore: 0, supScore: 0, remark: '', type: 'detail' },
        //   { content: '', target: '', time: '', rule: '', selfScore: 0, supScore: 0, remark: '', type: 'detail' }
        // )
      }
      if (tableIdx === 2) {
        this.mainList2.some((item, index) => {
          if (index > idx && item.type === 'title') {
            this.mainList2.splice(index, 0, 
              { content: '', type: 'title' },
              { content: '', target: '', time: '', rule: '', selfScore: 0, supScore: 0, remark: '', type: 'detail' },
              { content: '', target: '', time: '', rule: '', selfScore: 0, supScore: 0, remark: '', type: 'detail' }
            )
            return true
          } else if (index === this.mainList2.length - 1) {
            this.mainList2.splice(index + 1, 0, 
              { content: '', type: 'title' },
              { content: '', target: '', time: '', rule: '', selfScore: 0, supScore: 0, remark: '', type: 'detail' },
              { content: '', target: '', time: '', rule: '', selfScore: 0, supScore: 0, remark: '', type: 'detail' }
            )
            return true
          }
        })
        // this.mainList2.push(
        //   { content: '', type: 'title' },
        //   { content: '', target: '', time: '', rule: '', selfScore: 0, supScore: 0, remark: '', type: 'detail' },
        //   { content: '', target: '', time: '', rule: '', selfScore: 0, supScore: 0, remark: '', type: 'detail' }
        // )
      }
      if (tableIdx === 3) {
        this.mainList3.some((item, index) => {
          if (index > idx && item.type === 'title') {
            this.mainList3.splice(index, 0, 
              { content: '', type: 'title' },
              { content: '', target: '', time: '', rule: '', selfScore: 0, supScore: 0, remark: '', type: 'detail' },
              { content: '', target: '', time: '', rule: '', selfScore: 0, supScore: 0, remark: '', type: 'detail' }
            )
            return true
          } else if (index === this.mainList3.length - 1) {
            this.mainList3.splice(index + 1, 0, 
              { content: '', type: 'title' },
              { content: '', target: '', time: '', rule: '', selfScore: 0, supScore: 0, remark: '', type: 'detail' },
              { content: '', target: '', time: '', rule: '', selfScore: 0, supScore: 0, remark: '', type: 'detail' }
            )
            return true
          }
        })
        // this.mainList3.push(
        //   { content: '', type: 'title' },
        //   { content: '', target: '', time: '', rule: '', selfScore: 0, supScore: 0, remark: '', type: 'detail' },
        //   { content: '', target: '', time: '', rule: '', selfScore: 0, supScore: 0, remark: '', type: 'detail' }
        // )
      }
      if (tableIdx === 4) {
        this.mainList4.some((item, index) => {
          if (index > idx && item.type === 'title') {
            this.mainList4.splice(index, 0, 
              { content: '', type: 'title' },
              { content: '', target: '', time: '', rule: '', selfScore: 0, supScore: 0, remark: '', type: 'detail' },
              { content: '', target: '', time: '', rule: '', selfScore: 0, supScore: 0, remark: '', type: 'detail' }
            )
            return true
          } else if (index === this.mainList4.length - 1) {
            this.mainList4.splice(index + 1, 0, 
              { content: '', type: 'title' },
              { content: '', target: '', time: '', rule: '', selfScore: 0, supScore: 0, remark: '', type: 'detail' },
              { content: '', target: '', time: '', rule: '', selfScore: 0, supScore: 0, remark: '', type: 'detail' }
            )
            return true
          }
        })
        // this.mainList4.push(
        //   { content: '', type: 'title' },
        //   { content: '', target: '', time: '', rule: '', selfScore: 0, supScore: 0, remark: '', type: 'detail' },
        //   { content: '', target: '', time: '', rule: '', selfScore: 0, supScore: 0, remark: '', type: 'detail' }
        // )
      }
    },
    // 删除工作效率主项
    delMainList (idx, tableIdx) {
      this.$Modal.confirm({
        title: '提示',
        content: '确认删除这一项？',
        loading: true,
        onOk: () => {
          this.loading = false
          this.$Modal.remove()
          let endIdx = 0
          if (tableIdx === 1) {
            for (let i = idx + 1; i < this.mainList1.length; i++) {
              if (this.mainList1[i].type === 'title') {
                endIdx = i
                break
              } else {
                endIdx = i + 1
              }
            }
            this.mainList1.splice(idx, (endIdx - idx))
          }
          if (tableIdx === 2) {
            for (let i = idx + 1; i < this.mainList2.length; i++) {
              if (this.mainList2[i].type === 'title') {
                endIdx = i
                break
              } else {
                endIdx = i + 1
              }
            }
            this.mainList2.splice(idx, (endIdx - idx))
          }
          if (tableIdx === 3) {
            for (let i = idx + 1; i < this.mainList3.length; i++) {
              if (this.mainList3[i].type === 'title') {
                endIdx = i
                break
              } else {
                endIdx = i + 1
              }
            }
            this.mainList3.splice(idx, (endIdx - idx))
          }
          if (tableIdx === 4) {
            for (let i = idx + 1; i < this.mainList4.length; i++) {
              if (this.mainList4[i].type === 'title') {
                endIdx = i
                break
              } else {
                endIdx = i + 1
              }
            }
            this.mainList4.splice(idx, (endIdx - idx))
          }
        }
      })
    },
    // 工作效率添加一行
    addMainLine (idx, tableIdx) {
      if (tableIdx === 1) {
        this.mainList1.splice(idx + 1, 0, { content: '', target: '', time: '', rule: '', selfScore: 0, supScore: 0, remark: '', type: 'detail' })
      }
      if (tableIdx === 2) {
        this.mainList2.splice(idx + 1, 0, { content: '', target: '', time: '', rule: '', selfScore: 0, supScore: 0, remark: '', type: 'detail' })
      }
      if (tableIdx === 3) {
        this.mainList3.splice(idx + 1, 0, { content: '', target: '', time: '', rule: '', selfScore: 0, supScore: 0, remark: '', type: 'detail' })
      }
      if (tableIdx === 4) {
        this.mainList4.splice(idx + 1, 0, { content: '', target: '', time: '', rule: '', selfScore: 0, supScore: 0, remark: '', type: 'detail' })
      }
    },
    // 工作效率删除一行
    delMainLine (idx, tableIdx) {
      this.$Modal.confirm({
        title: '提示',
        content: '确认删除这一行？',
        loading: true,
        onOk: () => {
          this.loading = false
          this.$Modal.remove()
          if (tableIdx === 1) {
            this.mainList1.splice(idx, 1)
          }
          if (tableIdx === 2) {
            this.mainList2.splice(idx, 1)
          }
          if (tableIdx === 3) {
            this.mainList3.splice(idx, 1)
          }
          if (tableIdx === 4) {
            this.mainList4.splice(idx, 1)
          }
        }
      })
    },
    // 判断工作效率多少项，显隐增加、删除按钮
    isMainLen1 () { // 判断有title类型的大于1个就显示  否则隐藏
      let _isMainArr = this.mainList1.filter(item => item.type === 'title')
      return _isMainArr.length > 1
    },
    // 判断工作效率细项行数，显隐增加、删除按钮
    isMainLineLen1 (idx) { // 判断上一个类型是title且后面那个也是title或者无则隐藏
      if (this.mainList1[idx - 1].type === 'title' && (!this.mainList1[idx + 1] || this.mainList1[idx + 1].type === 'title')) {
        return false
      }
      return true
    },
    // 判断工作质量多少项，显隐增加、删除按钮
    isMainLen2 () { // 判断有title类型的大于1个就显示  否则隐藏
      let _isMainArr = this.mainList2.filter(item => item.type === 'title')
      return _isMainArr.length > 1
    },
    // 判断工作质量细项行数，显隐增加、删除按钮
    isMainLineLen2 (idx) { // 判断上一个类型是title且后面那个也是title或者无则隐藏
      if (this.mainList2[idx - 1].type === 'title' && (!this.mainList2[idx + 1] || this.mainList2[idx + 1].type === 'title')) {
        return false
      }
      return true
    },
    // 判断日常事务多少项，显隐增加、删除按钮
    isMainLen3 () { // 判断有title类型的大于1个就显示  否则隐藏
      let _isMainArr = this.mainList3.filter(item => item.type === 'title')
      return _isMainArr.length > 1
    },
    // 判断日常事务细项行数，显隐增加、删除按钮
    isMainLineLen3 (idx) { // 判断上一个类型是title且后面那个也是title或者无则隐藏
      if (this.mainList3[idx - 1].type === 'title' && (!this.mainList3[idx + 1] || this.mainList3[idx + 1].type === 'title')) {
        return false
      }
      return true
    },
    // 判断学习与成长多少项，显隐增加、删除按钮
    isMainLen4 () { // 判断有title类型的大于1个就显示  否则隐藏
      let _isMainArr = this.mainList4.filter(item => item.type === 'title')
      return _isMainArr.length > 1
    },
    // 判断学习与成长细项行数，显隐增加、删除按钮
    isMainLineLen4 (idx) { // 判断上一个类型是title且后面那个也是title或者无则隐藏
      if (this.mainList4[idx - 1].type === 'title' && (!this.mainList4[idx + 1] || this.mainList4[idx + 1].type === 'title')) {
        return false
      }
      return true
    },
    flowColor (idx) { // 顶部流程标题颜色
      let _backStr = 'back-flow-color'
      if (this.isComplete) {
        _backStr = 'pre-flow-color'
      } else {
        if (this.approveIdx === idx) {
          _backStr = 'cur-flow-color'
        }
        if (this.approveIdx > idx) {
          _backStr = 'pre-flow-color'
        }
        if (this.approveIdx < idx) {
          _backStr = 'back-flow-color'
        }
      }
      return _backStr
    },
    monthChange (date) {
      this.belong_month = date
    },
    handleInport () { // 导入
      this.importData.modal = true
    },
    // 导入返回数据
    importBackData (Obj) {
      if (JSON.stringify(Obj) === '{}') return
      let _formObj = Obj.form_json
      this.detailObj = Obj
      this.mainList1 = _formObj.mainList1
      this.mainList2 = _formObj.mainList2
      this.mainList3 = _formObj.mainList3
      this.mainList4 = _formObj.mainList4
      // this.flowObj.flowList = _formObj.flowList
      this.backResetSupData('import')
    },
    handleCancel () { // 取消
      this.$router.go(-1)
    }
  },
  beforeDestroy () {
    this.$store.commit('setCollapsed', false)
    window.removeEventListener('keydown', this.handleEvent)
  },
  mounted () {
    window.addEventListener('keydown', this.handleEvent)
  },
  created () {
    this.$store.commit('setCollapsed', true)
    this.type = this.$route.params.id
    this.sysUser = JSON.parse(localStorage.getItem('userData')).user_name
    this.flowObj = JSON.parse(localStorage.getItem('userFlow'))
    if (this.type !== 'add') { // 编辑或者详情
      let _backArr = this.type.split('&id=')
      this.type = _backArr[0]
      this.form_id = _backArr[1]
      this.getFormDetail()
    } else {
      if (this.flowObj.flowList && this.flowObj.flowList.length > 0) {
        this.sysIdx = this.flowObj.flowList.findIndex(item => item.user_name === this.sysUser)
        if (this.sysIdx < (this.flowObj.flowList.length - 1)) {
          this.next_unified_account_id = this.flowObj.flowList[this.sysIdx + 1].unified_account_id
        } else {
          this.next_unified_account_id = ''
        }
      }
    }
    window.addEventListener('paste', e => {
      const clipdata = e.clipboardData || window.clipboardData
      let text = clipdata.getData('text/plain')
      let arr = text.split('\n')
      if (this.pasteMainIdx === 1) {
        this.mainList1[this.pasteLine][this.pasteStr] = ''
      }
      if (this.pasteMainIdx === 2) {
        this.mainList2[this.pasteLine][this.pasteStr] = ''
      }
      if (this.pasteMainIdx === 3) {
        this.mainList3[this.pasteLine][this.pasteStr] = ''
      }
      if (this.pasteMainIdx === 4) {
        this.mainList4[this.pasteLine][this.pasteStr] = ''
      }
      setTimeout(() => {
        if (this.pasteMainIdx && this.pasteStr && this.pasteLine !== '' && arr.length > 0) {
          arr.forEach((item, idx) => {
            if (item === '') return
            item = item.replace(/\r|\n|\s/g, '')
            if (this.pasteMainIdx === 1) {
              this.mainList1[this.pasteLine + idx][this.pasteStr] = item
            }
            if (this.pasteMainIdx === 2) {
              this.mainList2[this.pasteLine + idx][this.pasteStr] = item
            }
            if (this.pasteMainIdx === 3) {
              this.mainList3[this.pasteLine + idx][this.pasteStr] = item
            }
            if (this.pasteMainIdx === 4) {
              this.mainList4[this.pasteLine + idx][this.pasteStr] = item
            }
          })
        }
      }, 100)
    })
  }
})
</script>
<style lang="less">
  .table_scroll {
    overflow-x: auto !important;
  }
  .btn-area {
    text-align: right;
    position: absolute;
    bottom: 0;
    right: 0;
    width: 100%;
    border-top: 1px solid #e8e8e8;
    padding: 10px 16px;
    background: #fff;
  }
  .btn-area button {
    margin-left: 15px;
  }
  .approve-steps {
    position: absolute;
    width: 30px;
    right: 20px;
    top: 160px;
  }
  .step-list {
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    font-size: 14px;
    background: #70B603;
    color: #fff;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.4);
    cursor: pointer;
    margin-top: 10px;
  }
  .step-cur {
    background: #D9001B;
  }
  .cur-flow-color {
    background: #D9001B;
  }
  .back-flow-color {
    background: #aaa;
  }
  .pre-flow-color {
    background: #70B603;
  }
  .table-bordered {
    position: relative;
    border-left:2px solid #e8eaec;
    border-top:2px solid #e8eaec;
  }
  .table-bordered tr {
    position: relative;
  }
  .table-bordered th{
    border-right:2px solid #e8eaec;
    border-bottom:2px solid #e8eaec;
    min-width: 0;
    height: 24px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    text-align: left;
    text-overflow: ellipsis;
    text-align: center;
    vertical-align: middle;
    padding: 0 10px;
    input {
      color: #999;
    }
  }
  .table-bordered td{
    border-right:2px solid #e8eaec;
    border-bottom:2px solid #e8eaec;
    min-width: 0;
    height: 24px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    text-align: left;
    text-overflow: ellipsis;
    vertical-align: middle;
    padding: 0 10px;
  }
  .table-bordered .center {
    text-align: center;
  }
  .title_font {
    font-weight: bold;
    font-size: 16px;
    height: 34px;
    .ivu-input-type-textarea .ivu-input {
      font-weight: bold;
    }
  }
  .temp1_add_btn {
    position: absolute;
    margin-left: 5px;
  }
  .remove_btn {
    position: absolute;
    background: #ccc;
    border: 0;
    margin-left: 35px;
  }
</style>

<template>
  <div>
    <Card>
      <search @searchResults='searchResults' :setSearch='setSearchData' @resetResults='resetResults'></search>
      <div class="add_btn">
        <Button :disabled="selectList.length === 0" icon="md-cloud-done" type="primary" @click="downLoadSelect">批量下载</Button>
      </div>
    </Card>
    <Card style="margin-top: 10px">
      <Spin size="large" fix v-if="spinShow">
        <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
        <div>{{ spinTxt }}</div>
      </Spin>
      <Table ref="tableSelection" border :loading="isLoading" :columns="columns" @on-row-dblclick="showPdf" :data="list" @on-selection-change="selectChange"></Table>
      <Page show-sizer :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="listQuery.pageSize" :current.sync="listQuery.pageIndex"
          :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
    </Card>
  </div>
</template>
<script>
import search from '_c/search' // 查询组件
import BasicAPI from '@/api/basicData'
import API from '@/api/shipperOwner/contract'
import axios from 'axios'
import { getToken } from '@/libs/util'
import Clipboard from 'clipboard'
import CryptoJS from 'crypto-js'
import config from '@/config'
const baseUrl = process.env.NODE_ENV === 'development' ? config.baseUrl.dev : config.baseUrl.pro

export default ({
  components: {
    search
  },
  data () {
    return {
      progress: 0,
      spinShow: false,
      selectList: [],
      secretKey: 'UNIFIED_Xt603209',
      listQuery: {
        pageSize: 10,
        pageIndex: 1,
        audit_status: 3
      },
      isLoading: false,
      total: 0,
      setSearchData: {
        seafarer_name: {
          type: 'text',
          label: '姓名',
          value: '',
          width: 140,
          isdisable: false
        },
        crt_duty_id: {
          type: 'select',
          label: '职务',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 140,
          value: '',
          filterable: true
        },
        audit_status: {
          type: 'select',
          label: '状态',
          selectData: [
            {label: '履行中', value: 3},
            {label: '已完成', value: 4}
          ],
          selected: 3,
          placeholder: '请选择',
          selectName: '',
          width: 120,
          value: '',
          filterable: true
        },
        international_flag: {
          type: 'select',
          label: '区域',
          selectData: [
            { label: '内贸', value: 0 },
            { label: '内外贸', value: 1 }
          ],
          selected: '',
          placeholder: '请选择',
          width: 120,
          value: ''
        }
      },
      columns: [
        {
          type: 'selection',
          width: 60,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '姓名',
          key: 'seafarer_name',
          align: 'center'
        },
        {
          title: '合同职务',
          key: 'crt_duty_name',
          align: 'center'
        },
        {
          title: '合同编号',
          key: 'contract_no',
          align: 'center',
          render: (h, params) => {
            if (parseFloat(params.row.audit_status) === 4 && params.row.termination_reason !== '') {
              return h('Tooltip', {
                props: {
                  maxWidth: 300,
                  transfer: true
                }
              }, [
                h('div', {
                  slot: 'content'
                }, params.row.termination_reason),
                h('div', {
                  style: {
                    display: 'inline-flex',
                    alignItems: 'center',
                    marginTop: '5px'
                  }
                }, params.row.contract_no)
              ])
            } else {
              return h('div', {
                style: {
                  display: 'inline-flex',
                  alignItems: 'center',
                  marginTop: '5px'
                }
              }, params.row.contract_no)
            }
          }
        },
        {
          title: '合同类型',
          key: 'contract_type',
          align: 'center',
          render: (h, params) => {
            // let typeStr = params.row.contract_type === '0' ? '首次合同' : '续签合同'
            let typeStr = params.row.offline_flag === '1' ? '线下合同' : '云签合同'
            let offLineStr = params.row.international_flag === '0' ? '(内贸)' : '(内外贸)'
            return h('div', {}, typeStr + offLineStr)
          }
        },
        {
          title: '身份证',
          key: 'seafarer_id_no',
          align: 'center',
          ellipsis: true,
          tooltip: true,
          minWidth: 80
        },
        {
          title: '开始时间',
          key: 'contract_period_from',
          align: 'center'
        },
        {
          title: '结束时间',
          key: 'contract_period_to',
          align: 'center'
        },
        {
          title: '合同状态',
          key: 'audit_status',
          align: 'center',
          render: (h, params) => {
            let typeStr = ''
            if (params.row.audit_status === '1') typeStr = '待签订'
            if (params.row.audit_status === '2') typeStr = '待审核'
            if (params.row.audit_status === '3') typeStr = '履行中'
            if (params.row.audit_status === '4') typeStr = '已完成'
            if (params.row.audit_status === '5') typeStr = '废弃'
            return h('div', {}, typeStr)
          }
        },
        {
          title: '下载次数',
          key: 'download_count',
          align: 'center'
        },
        {
          title: '操作',
          key: 'seafarer_name',
          align: 'center',
          render: (h, params) => {
            return  h('Icon', {
              slot: 'content',
              style: {
                display: 'block',
                cursor: 'pointer'
              },
              class: 'list_btn',
              props: {
                type: 'md-download',
                size: '14'
              },
              domProps: {
                innerHTML: ' 下载'
              },
              on: {
                click: () => {
                  this.handleDownload(params.row)
                }
              }
            })
          }
        }
      ],
      list: []
    }
  },
  methods: {
    getList () {
      this.isLoading = true
      API.querySeafarerContractPage(this.listQuery).then(res => {
        this.isLoading = false
        if (res.data.Code === 10000) {
          this.list = res.data.Result
          this.total = res.data.Total
        }
      })
    },
    async showPdf (row) { // 预览pdf
      if (!row.wpsUrl) {
        this.$Message.warning('文件还未生成')
        return
      }
      try {
        const encryptedData = CryptoJS.enc.Base64.parse(row.wpsUrl)
        const key = CryptoJS.enc.Utf8.parse(this.secretKey)

        const decrypted = CryptoJS.AES.decrypt(
          { ciphertext: encryptedData },
          key,
          { mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7 }  // ECB 模式，不需要 IV
        )
        const decryptedText = decrypted.toString(CryptoJS.enc.Utf8)
        sessionStorage.setItem('wpsUrl', decryptedText)
        sessionStorage.setItem('token', getToken())
        const jump = this.$router.resolve({ name: 'viewFile' })
        window.open(jump.href, '_blank')
      } catch (err) {
        console.log('出错了： ' + err)
      }
    }, 
    getDutyList () { // 船员职务列表
      BasicAPI.queryDictCacheList({
        dic_code: 'unCrewDuty'
      }).then(res => {
        if (res.data.Code === 10000) {
          res.data.Result.map(item => {
            this.setSearchData.crt_duty_id.selectData.push({
              label: item.entryDesc || item.name,
              value: item.id
            })
          })
        }
      })
    },
    selectChange (arr) { // 选中
      this.selectList = arr
    },
    downLoadSelect () { // 批量下载
      if (this.selectList.length > 0) {
        let idList = this.selectList.map(item => item.seafarer_contract_id)
        let _param = {
          seafarer_contract_ids: idList.join(),
          // zip_name: ''
        }
        this.spinShow = true
        this.spinTxt = '正在下载'
        API.downloadFileDirectory(_param).then(res => {
          if (res.status === 200) {
            this.spinShow = false
            const link = document.createElement('a')
            const name = '船员合同.zip'
            this.countDownNum(idList.join())
            try {
              const blob = new Blob([res.data], { type: 'application/zip' })
              const url = window.URL || window.webkitURL || window.moxURL
              link.href = url.createObjectURL(blob)
              link.setAttribute('download', name)
              document.body.appendChild(link)
              link.click()
              document.body.removeChild(link)
              url.revokeObjectURL(link.href)
              this.$refs.tableSelection.selectAll(false)
            } catch (e) {
              console.log('下载出错', e)
            }
          } else {
            console.log(res)
          }
        })
      }
    },
    async handleDownload (row) {
      try {
        const encryptedData = CryptoJS.enc.Base64.parse(row.url)
        const key = CryptoJS.enc.Utf8.parse(this.secretKey)

        const decrypted = CryptoJS.AES.decrypt(
          { ciphertext: encryptedData },
          key,
          { mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7 }  // ECB 模式，不需要 IV
        )
        const downUrl = decrypted.toString(CryptoJS.enc.Utf8)
        const link = document.createElement('a')
        const name = row.seafarer_name + '(' + row.crt_duty_name + ')' + '.pdf'
        try {
          const response = await axios({
            url: downUrl,
            method: 'GET',
            responseType: 'arraybuffer', // 以数组缓冲区的方式接收数据
            onDownloadProgress: (event) => {
              if (event.lengthComputable) {
                this.progress = Math.round((event.loaded * 100) / event.total)
              }
            }
          })
          const blob = new Blob([response.data], { type: 'application/pdf' })
          const url = window.URL || window.webkitURL || window.moxURL
          // 统计下载次数
          this.countDownNum(row.seafarer_contract_id)

          link.href = url.createObjectURL(blob)
          link.setAttribute('download', name)
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          url.revokeObjectURL(link.href)
        } catch (err) {
          console.log('下载出错', err)
        }
      } catch (err) {
        console.log('出错了： ' + err)
      }
    },
    countDownNum(id) {
      let _param = {
        seafarer_contract_ids: id
      }
      API.updateSeafarerContractDownloadCount(_param).then(res => {
        this.getList()
      })
    },
    // 查询
    searchResults (e) {
      this.listQuery.seafarer_name = e.seafarer_name
      this.listQuery.crt_duty_id = e.crt_duty_id
      this.listQuery.audit_status = e.audit_status
      this.listQuery.international_flag = e.international_flag
      delete e.target
      this.listQuery.pageIndex = 1
      this.getList()
    },
    // 重置
    resetResults () {
      this.listQuery = {
        seafarer_name: '',
        crt_duty_id: '',
        audit_status: 3,
        international_flag: '',
        pageSize: 10,
        pageIndex: 1
      }
      this.setSearchData.seafarer_name.value = ''
      this.setSearchData.crt_duty_id.selected = ''
      this.setSearchData.audit_status.selected = 3
      this.setSearchData.international_flag.selected = ''
      this.getList()
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    }
  },
  created () {
    this.getDutyList()
    this.getList()
  }
})
</script>
<style>
  .add_btn {
    position: absolute;
    right: 16px;
    top: 22px;
  }
  .list_btn:hover {
    color: #007DFF;
  }
  .demo-spin-icon-load{
    animation: ani-demo-spin 1s linear infinite;
  }
  @keyframes ani-demo-spin {
    from { transform: rotate(0deg);}
    50%  { transform: rotate(180deg);}
    to   { transform: rotate(360deg);}
  }
</style>

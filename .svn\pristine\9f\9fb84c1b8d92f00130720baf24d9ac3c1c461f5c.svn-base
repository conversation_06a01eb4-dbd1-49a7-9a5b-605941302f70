<template>
  <Card>
    <Button type="primary" v-if="showList" @click="changeBtn">编辑</Button>
    <div v-else>
      <Button type="primary" @click="addTitle('score')" icon="md-add">评分题</Button>
      <Button type="primary" @click="addTitle('QAndA')" icon="md-add" style="margin-left: 15px;">问答题</Button>
    </div>
    <div v-for="(item, idx) in list" :key="idx" class="start_div">
      <span style="min-width: 30px;text-align: right;display: inline-block;">{{ idx + 1 }}.</span>
      <label>
        <span class="red">* </span>题目
      </label>
      <Input type="textarea" v-model="item.evaluate_template_title" :readonly="showList"></Input>
      <Button v-if="showOptionRemove" type="text" @click="handleRemove(idx)" icon="md-remove-circle" size="large"></Button>
      <Rate disabled v-if="item.evaluate_template_type !== '2'" :value="parseInt(item.evaluate_template_score)" :count='10' />
      <Input disabled v-if="item.evaluate_template_type === '2'" type="textarea" style="margin: 10px 0 0 70px;"></Input>
    </div>
    <div class="demo-drawer-footer" v-if="!showList">
      <Button type="primary" @click="updateData">保存</Button>
    </div>
  </Card>
</template>
<script>
import { queryEvaluateTemplateList, evaluateTemplateAddAll } from '@/api/examSystem/dataConfigure/evaluateTemplate'
export default {
  data () {
    return {
      showList: true,
      loading: false,
      list: [],
      showOptionRemove: false,
      listQuery: {
        evaluate_template_id: '',
        evaluate_template_score: '',
        evaluate_template_type: ''
      }
    }
  },
  created () {
    this.getList()
  },
  methods: {
    // 获取列表
    getList () {
      this.loading = true
      queryEvaluateTemplateList(this.listQuery).then(res => {
        if (res.data.Code === 10000) {
          this.loading = false
          this.list = res.data.Result
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 点击编辑按钮触发
    changeBtn () {
      this.showList = false
      this.showOptionRemove = true
    },
    // 增加题目
    addTitle (type) {
      if (type === 'score') {
        this.list.push({
          evaluate_template_title: '',
          evaluate_template_score: '10',
          evaluate_template_type: '1'
        })
      } else {
        this.list.push({
          evaluate_template_title: '',
          evaluate_template_score: '10',
          evaluate_template_type: '2'
        })
      }
    },
    // 移除题目
    handleRemove(idx) {
      this.list.splice(idx, 1)
    },
    // 编辑保存
    updateData () {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确认保存模板？</p>',
        loading: true,
        onOk: () => {
          evaluateTemplateAddAll({evaluateJson: JSON.stringify(this.list)}).then(res => {
            if (res.data.Code === 10000) {
              this.loading = false
              this.$Modal.remove()
              this.$Message.success(res.data.Message)
              this.getList()
              this.showList = true
              this.showOptionRemove = false
            } else {
              this.loading = false
              this.$Modal.remove()
              this.$Message.error(res.data.Message)
            }
          })
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.start_div {
  margin-top: 25px;
  .ivu-input-wrapper {
    width: 50%;
    vertical-align: top;
  }
  .ivu-rate {
    display: block;
    margin-left: 70px;
  }
  .red {
    color: red;
  }
  .ivu-btn-icon-only.ivu-btn-large {
    vertical-align: top;
  }
}
.demo-drawer-footer {
  width: 100%;
  margin-top: 10px;
  display: block;
  text-align: right;
}
</style>

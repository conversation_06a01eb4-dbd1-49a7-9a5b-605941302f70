<template>
    <div class="agent-chat">
        <!-- Dify Chat 组件 -->
        <DifyChat ref="difyChat" :api-url="chatConfig.apiUrl" :api-key="chatConfig.apiKey" :user="currentUser"
            :post-name="postName" displayMode="page" @go-agent="goAgent" />
    </div>
</template>

<script>
import DifyChat from '@/libs/dify-chat.js'

export default {
    name: 'AgentChat',
    components: {
        DifyChat
    },
    data() {
        return {
            // 当前用户信息
            currentUser: localStorage.getItem('userDataId'),
            // 聊天配置
            chatConfig: {
                apiUrl: 'http://************/v1',
                apiKey: localStorage.getItem('agentApiKey') // 请替换为实际的API密钥
            },
            postName: JSON.parse(localStorage.getItem('userData')).post_name,
        }
    },
    methods: {
        goAgent() {
            this.$router.push({ name: 'agent' })
        }
    },
    async created() {
        // await this.loadInitialConversationId()
    },
    mounted() {
        if (!localStorage.getItem('agentApiKey') || !localStorage.getItem('userDataId')) {
            this.$router.push({ name: 'agent' })
        }
        console.log('Vue项目集成示例已加载')
        console.log('Dify Chat组件引用:', this.$refs.difyChat)
    }
}
</script>

<style scoped>
.agent-chat {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    height: 100vh;
    width: 100vw;
}
</style>
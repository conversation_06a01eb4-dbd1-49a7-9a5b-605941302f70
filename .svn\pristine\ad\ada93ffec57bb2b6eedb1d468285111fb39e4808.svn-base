<template>
  <div class="chat-container" :class="{ 'chat-expanded': isExpanded, 'chat-fullscreen': isFullscreen }">
    <!-- 聊天图标按钮 -->
    <div class="chat-icon" @click="toggleChat" v-if="!isExpanded">
      <Icon type="ios-chatbubbles" size="28" />
      <Badge :count="unreadCount" class="unread-badge" v-if="unreadCount > 0" />
    </div>

    <!-- 聊天窗口 -->
    <div class="chat-window" v-if="isExpanded">
      <div class="chat-header">
        <div class="chat-title">
          <Icon type="ios-headset" size="18" /> 智能助手
        </div>
        <div class="chat-actions">
          <Button type="primary" size="small" class="action-btn clear-btn" @click="clearChat" title="清除聊天记录">
            <Icon type="ios-trash" /> 清除
          </Button>
          <Button type="primary" size="small" class="action-btn minimize-btn" @click="toggleChat" title="最小化">
            <Icon type="md-remove" />
          </Button>
          <Button type="primary" size="small" class="action-btn maximize-btn" @click="toggleFullscreen" :title="isFullscreen ? '退出全屏' : '全屏显示'">
            <Icon :type="isFullscreen ? 'ios-browsers-outline' : 'ios-browsers'" />
          </Button>
          <Button type="error" size="small" class="action-btn close-btn" @click="closeChat" title="关闭">
            <Icon type="md-close" />
          </Button>
        </div>
      </div>

      <div class="chat-messages" ref="messageContainer">
        <div v-for="(message, index) in messages" :key="index" 
             :class="['message', message.isUser ? 'user-message' : 'system-message']">
          <Avatar v-if="!message.isUser" icon="ios-contact" class="message-avatar" />
          <div class="message-content">
            <div class="message-text" v-html="formatMessage(message.content)"></div>
            <div class="thinking-section" v-if="message.thinking" @click="toggleThinking(index)">
              <div class="thinking-header">
                <span><Icon type="ios-bulb" size="14" /> 思考过程</span>
                <Icon :type="message.showThinking ? 'ios-arrow-up' : 'ios-arrow-down'" size="14" />
              </div>
              <div class="thinking-content" v-if="message.showThinking" v-html="message.thinking"></div>
            </div>
            <div class="message-time">
              <Icon type="ios-time-outline" size="12" /> {{ formatTime(message.timestamp) }}
            </div>
          </div>
          <Avatar v-if="message.isUser" icon="ios-person" class="message-avatar" />
        </div>
        <div class="typing-indicator" v-if="isTyping">
          <Icon type="ios-loading" size="18" class="spin-icon-loading"></Icon>
          <span>正在输入...</span>
        </div>
      </div>

      <div class="chat-input-container">
        <div class="input-wrapper">
          <Input 
            type="textarea" 
            v-model="newMessage" 
            @on-keydown="handleKeyDown"
            placeholder="请输入消息... (Shift+Enter 发送)" 
            :autosize="{ minRows: 1, maxRows: 4 }"
            ref="messageInput"
            class="chat-input"
          />
          <!-- 附件按钮已注释
          <Button type="text" class="attachment-btn" title="添加附件">
            <Icon type="ios-attach" size="20" />
          </Button>
          -->
        </div>
        <Button type="primary" class="send-button" @click="sendMessage" :disabled="!newMessage.trim()">
          <Icon type="ios-send" /> 发送
        </Button>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  name: 'ChatComponent',
  data() {
    return {
      isExpanded: false,
      isFullscreen: false,
      messages: [],
      newMessage: '',
      isTyping: false,
      unreadCount: 0,
      apiUrl: 'http://************/v1/chat-messages',
      api_key: 'app-4aMjGyH2y0qkx3yMFodsXZv7',
      userId: this.generateUserId(), // 生成用户ID
      pollingInterval: null, // 轮询间隔引用
      isPollingEnabled: false, // 控制是否启用轮询
      windowPosition: { right: '20px', bottom: '20px' } // 保存窗口位置
    }
  },
  mounted() {
    // 添加Font Awesome
    if (!document.getElementById('font-awesome-css')) {
      const link = document.createElement('link');
      link.id = 'font-awesome-css';
      link.rel = 'stylesheet';
      link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css';
      document.head.appendChild(link);
    }
    
    this.loadMessages(); // 加载保存的消息
    
    // 监听窗口大小变化，调整聊天窗口位置
    window.addEventListener('resize', this.handleWindowResize)
  },
  beforeDestroy() {
    // 清除轮询定时器
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval)
    }
    
    // 移除窗口大小变化监听
    window.removeEventListener('resize', this.handleWindowResize)
  },
  methods: {
    toggleThinking(index) {
      if (this.messages[index] && this.messages[index].thinking) {
        this.messages[index].showThinking = !this.messages[index].showThinking
        this.saveMessages()
        this.$nextTick(() => {
          if (this.messages[index].showThinking) {
            this.scrollToBottom()
          }
        })
      }
    },
    generateUserId() {
      // 从localStorage获取用户ID，如果没有则生成新的
      let userId = localStorage.getItem('chatUserId')
      if (!userId) {
        userId = 'user_' + Math.random().toString(36).substr(2, 9)
        localStorage.setItem('chatUserId', userId)
      }
      return userId
    },
    loadMessages() {
      const savedMessages = localStorage.getItem('chatMessages')
      if (savedMessages) {
        try {
          this.messages = JSON.parse(savedMessages)
          // 确保所有消息都有showThinking属性
          this.messages.forEach(message => {
            if (message.thinking && message.showThinking === undefined) {
              message.showThinking = false
            }
          })
        } catch (e) {
          console.error('加载消息失败:', e)
        }
      }
    },
    saveMessages() {
      localStorage.setItem('chatMessages', JSON.stringify(this.messages))
    },
    clearChat() {
      this.$Modal.confirm({
        title: '确认清除',
        content: '确定要清除所有聊天记录吗？',
        onOk: () => {
          this.messages = []
          localStorage.removeItem('chatMessages')
          this.$Message.success('聊天记录已清除')
        }
      })
    },
    toggleChat() {
      this.isExpanded = !this.isExpanded
      
      if (this.isExpanded) {
        this.unreadCount = 0
        this.$nextTick(() => {
          this.scrollToBottom()
          if (this.$refs.messageInput) {
            this.$refs.messageInput.focus()
          }
        })
      }
    },
    closeChat() {
      this.isExpanded = false
    },
    toggleFullscreen() {
      this.isFullscreen = !this.isFullscreen
      
      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },
    handleWindowResize() {
      if (this.isExpanded && !this.isFullscreen) {
        this.applyWindowPosition()
      }
    },
    formatMessage(content) {
      if (!content) return ''
      // 将URL转换为链接
      const urlRegex = /(https?:\/\/[^\s]+)/g
      return content.replace(urlRegex, '<a href="$1" target="_blank">$1</a>')
    },
    extractThinking(text) {
      // 查找思考过程部分
      const thinkingRegex = /<details style="color:gray;background-color: #f8f8f8;padding: 8px;border-radius: 4px;" open>\s*<summary>\s*Thinking...\s*<\/summary>([\s\S]*?)<\/details>/g;
      
      let thinking = '';
      let cleanedText = text;
      
      // 提取所有思考过程
      const matches = text.matchAll(thinkingRegex);
      for (const match of matches) {
        if (match[1]) {
          thinking += match[1].trim() + '\n\n';
          // 不立即从文本中移除思考部分，而是标记它
          cleanedText = cleanedText.replace(match[0], '<!-- thinking-placeholder -->');
        }
      }
      
      return { thinking, cleanedText };
    },
    async fetchMessages() {
      try {
        // 使用POST请求替代GET请求
        const response = await fetch(this.apiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.api_key}`
          },
          body: JSON.stringify({
            inputs: {},
            query: "获取历史消息",
            response_mode: 'streaming',
            conversation_id: '',
            user: this.userId,
            files: []
          })
        });
        
        // 创建一个临时变量来存储完整响应
        let fullResponse = '';
        let hasNewMessages = false;
        let messageId = null; // 用于跟踪消息ID
        
        // 处理流式响应
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let partialLine = '';
        
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          
          // 解码二进制数据
          const text = decoder.decode(value);
          const lines = (partialLine + text).split('\n');
          partialLine = lines.pop() || '';
          
          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const jsonStr = line.substring(6); // 移除 "data: " 前缀
                const data = JSON.parse(jsonStr);
                
                // 检查是否是同一条消息的更新
                if (messageId === null) {
                  messageId = data.message_id
                } else if (data.message_id && data.message_id !== messageId) {
                  console.log('收到新消息ID，忽略:', data.message_id)
                  continue // 忽略不同ID的消息
                }
                
                // 如果是消息事件，添加到响应中
                if (data.event === 'message' && data.answer) {
                  fullResponse += data.answer;
                  hasNewMessages = true;
                }
              } catch (e) {
                console.error('解析流式响应失败:', e);
              }
            }
          }
        }
        
        // 如果获取到了新消息，添加到消息列表
        if (hasNewMessages && fullResponse.trim()) {
          // 提取思考过程
          const { thinking, cleanedText } = this.extractThinking(fullResponse);
          
          // 添加一条完整的系统消息
          const newMessage = {
            content: cleanedText,
            thinking: thinking,
            showThinking: false,
            timestamp: new Date(),
            isUser: false
          };
          
          // 更新未读消息计数
          if (!this.isExpanded) {
            this.unreadCount += 1;
          }
          
          this.messages.push(newMessage);
          this.saveMessages(); // 保存新消息
          this.$nextTick(() => {
            this.scrollToBottom();
          });
        }
      } catch (error) {
        console.error('获取消息失败:', error);
      }
    },
    async sendMessage(event) {
      const messageText = this.newMessage.trim()
      if (!messageText) return
      
      // 添加用户消息到列表
      const userMessage = {
        content: messageText,
        timestamp: new Date(),
        isUser: true
      }
      this.messages.push(userMessage)
      this.saveMessages() // 保存消息
      this.newMessage = ''
      this.scrollToBottom()
      
      // 显示"正在输入"状态
      this.isTyping = true
      
      try {
        // 准备请求参数
        const requestData = { 
          inputs: {},
          query: messageText,
          response_mode: 'streaming',
          conversation_id: '',
          user: this.userId, // 使用用户ID
          files: []
        }
        
        // 发送消息到API并处理流式响应
        const response = await fetch(this.apiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.api_key}`
          },
          body: JSON.stringify(requestData)
        })
        
        // 创建一个临时的系统消息对象
        const systemMessage = {
          content: '',
          thinking: '',
          showThinking: false,
          timestamp: new Date(),
          isUser: false,
          isComplete: false // 标记消息是否完成
        }
        this.messages.push(systemMessage)
        
        // 处理流式响应
        const reader = response.body.getReader()
        const decoder = new TextDecoder()
        let partialLine = ''
        let fullResponse = '' // 用于累积完整响应
        let messageId = null // 用于跟踪消息ID
        let hasThinking = false // 标记是否有思考过程
        
        while (true) {
          const { done, value } = await reader.read()
          if (done) break
          
          // 解码二进制数据
          const text = decoder.decode(value)
          const lines = (partialLine + text).split('\n')
          partialLine = lines.pop() || ''
          
          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const jsonStr = line.substring(6) // 移除 "data: " 前缀
                const data = JSON.parse(jsonStr)
                
                // 检查是否是同一条消息的更新
                if (messageId === null) {
                  messageId = data.message_id
                } else if (data.message_id && data.message_id !== messageId) {
                  console.log('收到新消息ID，忽略:', data.message_id)
                  continue // 忽略不同ID的消息
                }
                
                // 更新系统消息内容
                if (data.event === 'message' && data.answer) {
                  // 检测是否包含思考过程
                  if (data.answer.includes('<summary>Thinking...</summary>')) {
                    hasThinking = true
                  }
                  
                  // 累积到完整响应中
                  fullResponse += data.answer;
                  
                  // 提取思考过程和正文
                  const { thinking, cleanedText } = this.extractThinking(fullResponse);
                  
                  // 更新当前消息
                  systemMessage.thinking = thinking;
                  
                  // 如果有思考过程，在思考过程完成前显示占位内容
                  if (hasThinking && cleanedText.includes('<!-- thinking-placeholder -->')) {
                    // 思考过程仍在进行，显示打字指示器作为内容
                    systemMessage.content = '正在思考...';
                    systemMessage.isComplete = false;
                  } else {
                    // 思考过程已完成或没有思考过程，显示实际内容
                    systemMessage.content = cleanedText.replace(/<!-- thinking-placeholder -->/g, '');
                    systemMessage.isComplete = true;
                  }
                  
                  this.saveMessages(); // 保存更新的消息
                  this.scrollToBottom();
                }
              } catch (e) {
                console.error('解析流式响应失败:', e)
              }
            }
          }
        }
        
        // 确保最终消息是完整的
        if (!systemMessage.isComplete) {
          const { thinking, cleanedText } = this.extractThinking(fullResponse);
          systemMessage.thinking = thinking;
          systemMessage.content = cleanedText.replace(/<!-- thinking-placeholder -->/g, '');
          systemMessage.isComplete = true;
          this.saveMessages();
        }
        
      } catch (error) {
        console.error('发送消息失败:', error)
        // 显示错误消息
        this.messages.push({
          content: '消息发送失败，请稍后重试。',
          timestamp: new Date(),
          isUser: false
        })
        this.saveMessages() // 保存错误消息
      } finally {
        this.isTyping = false
        this.scrollToBottom()
      }
    },
    scrollToBottom() {
      if (this.$refs.messageContainer) {
        this.$refs.messageContainer.scrollTop = this.$refs.messageContainer.scrollHeight
      }
    },
    formatTime(timestamp) {
      const date = new Date(timestamp)
      const hours = date.getHours().toString().padStart(2, '0')
      const minutes = date.getMinutes().toString().padStart(2, '0')
      return `${hours}:${minutes}`
    },
    // 添加手动刷新方法
    refreshMessages() {
      this.fetchMessages()
    },
    handleKeyDown(event) {
      // 如果按下Shift+Enter，发送消息
      if (event.key === 'Enter' && event.shiftKey) {
        event.preventDefault() // 阻止默认行为
        this.sendMessage()
        this.$nextTick(() => {
          this.newMessage = ''
        })
      }
    }
  }
}
</script>

<style scoped>
.chat-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
}

.chat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #4a90e2;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  position: relative;
  transition: transform 0.3s ease;
}

.chat-icon:hover {
  transform: scale(1.1);
}

.unread-badge {
  position: absolute;
  top: -5px;
  right: -5px;
}

.chat-window {
  width: 500px;
  height: calc(100% - 40px);
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: fixed;
  bottom: 0px;
  right: 0px;
  transition: all 0.3s ease;
}

.chat-fullscreen .chat-window {
  width: calc(100% - 240px);
  height: calc(100% - 240px);
  position: fixed;
  top: 120px;
  left: 120px;
  right: 120px;
  bottom: 120px;
  border-radius: 8px;
  z-index: 1001;
}

.chat-header {
  background-color: #4a90e2;
  color: white;
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-title {
  font-weight: bold;
  font-size: 16px;
  display: flex;
  align-items: center;
}

.chat-title i {
  margin-right: 6px;
}

.chat-actions {
  display: flex;
  align-items: center;
  gap: 8px; /* 统一按钮间距 */
}

/* 统一按钮基础样式 */
.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
  height: 28px;
  padding: 0 10px;
}

.action-btn i {
  margin-right: 4px;
}

/* 清除按钮样式 */
.clear-btn {
  background-color: #4a90e2 !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

.clear-btn:hover {
  background-color: #3a80d2 !important;
}

/* 最小化按钮样式 */
.minimize-btn {
  background-color: #4a90e2 !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  padding: 0 8px !important;
}

.minimize-btn:hover {
  background-color: #3a80d2 !important;
}

.minimize-btn i {
  margin-right: 0;
}

/* 最大化按钮样式 */
.maximize-btn {
  background-color: #4a90e2 !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  padding: 0 8px !important;
}

.maximize-btn:hover {
  background-color: #3a80d2 !important;
}

.maximize-btn i {
  margin-right: 0;
}

/* 关闭按钮样式 */
.close-btn {
  background-color: #e81123 !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  padding: 0 8px !important;
}

.close-btn:hover {
  background-color: #f1707a !important;
}

.close-btn i {
  margin-right: 0;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
  background-color: #f5f8fb;
}

.message {
  display: flex;
  margin-bottom: 16px;
  align-items: flex-start;
}

.system-message {
  flex-direction: row;
}

.user-message {
  flex-direction: row-reverse;
}

.message-avatar {
  margin: 0 8px;
  background-color: #2d8cf0 !important;
}

.user-message .message-avatar {
  background-color: #19be6b !important;
}

.message-content {
  max-width: 70%;
  display: flex;
  flex-direction: column;
}

.system-message .message-content {
  align-items: flex-start;
}

.user-message .message-content {
  align-items: flex-end;
}

.message-text {
  padding: 10px 14px;
  border-radius: 4px;
  margin-bottom: 4px;
  white-space: pre-wrap;
  word-break: break-word;
  transition: opacity 0.3s ease;
}

.system-message .message-text {
  background-color: white;
  color: #515a6e;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1);
}

.user-message .message-text {
  background-color: #2d8cf0;
  color: white;
}

.message-time {
  font-size: 12px;
  color: #808695;
  margin-top: 4px;
  display: flex;
  align-items: center;
}

.message-time i {
  margin-right: 4px;
}

.thinking-section {
  width: 100%;
  margin-top: 8px;
  border-radius: 4px;
  overflow: hidden;
  background-color: #f0f5ff;
  border: 1px solid #d0e0ff;
  transition: all 0.3s ease;
}

.thinking-header {
  padding: 8px 12px;
  background-color: #e0eaff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  font-size: 13px;
  color: #4a6baf;
  font-weight: 500;
  transition: background-color 0.2s;
}

.thinking-header:hover {
  background-color: #d5e2ff;
}

.thinking-header span {
  display: flex;
  align-items: center;
}

.thinking-header span i {
  margin-right: 6px;
}

.thinking-content {
  padding: 12px;
  font-size: 13px;
  line-height: 1.5;
  color: #515a6e;
  background-color: #f8faff;
  white-space: pre-wrap;
  overflow-x: auto;
}

.chat-input-container {
  padding: 12px 16px;
  background-color: white;
  border-top: 1px solid #e8eaec;
  display: flex;
  align-items: flex-end;
}

.input-wrapper {
  flex: 1;
  position: relative;
  margin-right: 10px;
}

.chat-input {
  border-radius: 4px;
}

.attachment-btn {
  position: absolute;
  right: 5px;
  bottom: 5px;
  color: #808695 !important;
  padding: 0 !important;
  margin: 0 !important;
  z-index: 10;
}

.attachment-btn:hover {
  color: #2d8cf0 !important;
}

.send-button {
  display: flex;
  align-items: center;
}

.send-button i {
  margin-right: 6px;
}

.typing-indicator {
  display: flex;
  padding: 10px 14px;
  background-color: white;
  border-radius: 18px;
  width: fit-content;
  margin-bottom: 15px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  align-items: center;
}

.typing-indicator span {
  margin-left: 8px;
  font-size: 13px;
  color: #515a6e;
}

.spin-icon-loading {
  animation: ani-spin 1s linear infinite;
}

@keyframes ani-spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .chat-fullscreen .chat-window {
    width: calc(100% - 40px);
    height: calc(100% - 40px);
    top: 20px;
    left: 20px;
    right: 20px;
    bottom: 20px;
  }
}

@media (max-width: 480px) {
  .chat-window {
    width: 100%;
    height: 100%;
    position: fixed;
    bottom: 0;
    right: 0;
    border-radius: 0;
  }
  
  .chat-fullscreen .chat-window {
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 0;
  }
  
  .chat-container {
    bottom: 10px;
    right: 10px;
  }
}
</style> 
<template>
  <div>
    <Card>
      <search @searchResults='searchResults' @selectOnChanged='selectOnChanged' :setSearch='setSearchData' @resetResults='resetResults'></search>
    </Card>
    <Card style="margin-top: 10px">
      <chart-line style="height: 300px;" unit="元/吨" :value="lineData" :color="lineColor" rotate="45" :showOnemarkLine="true" text="油料均价走势"/>
      <h3 class="text_con">
        油料采购数据表
        <Button type="text" @click="exportData">数据导出 <Icon type="ios-download-outline" size="18"></Icon></Button>
      </h3>
      <Table border :loading="loading" ref="selection" :columns="columns" :data="list" class="data_list"></Table>
       <!-- show-summary :summary-method="handleSummary" -->
    </Card>
  </div>
</template>

<script>
import '@/assets/menu_icon/iconfont.css'
import { ChartLine } from '_c/charts'
import search from '_c/search' // 查询组件
import API from '@/api/basicData'
import { queryAreaList } from '@/api/materialPurchaseSystem/areaConfigure'
import { queryOilListByMonth, exportOilListByMonth } from '@/api/materialPurchaseSystem/oilPrice'
export default {
  components: {
    ChartLine,
    search
  },
  data () {
    return {
      queryParam: {
        date_month_st: '',
        date_month_et: '',
        shipname: '',
        vendorname: '',
        inventoryname: '重油',
        area_name: ''
      },
      lineData: {
        xAxis: [],
        legend: ['', '三方价格'],
        smooth: 0,
        symbol: ['emptyCircle', 'none'],
        data: [[], []]
      },
      lineColor: ['#6699FF', '#E74823'],
      loading: false,
      list: [],
      columns: [],
      // 以下合计为横向，月份为纵向排版
      // columns: [
      //   {
      //     title: '月份',
      //     key: 'date_month',
      //     align: 'center',
      //     width: 130
      //   },
      //   {
      //     title: '',
      //     key: 'price_avg',
      //     align: 'center',
      //     render: (h, params) => {
      //       params.column.title = this.queryParam.inventoryname + '均值（元/吨）'
      //       let curIcon = params.row.increase_avg_rate < 0 ? '↓' : '↑'
      //       let val = '(' + curIcon + params.row.increase_avg_amount + ' ' + params.row.increase_avg_rate + '%)'
      //       return h('div',[
      //         h('div', {}, params.row.price_avg),
      //         h('div', {
      //           style: {
      //             display: params.index === 0 ? 'none' : '',
      //             color: params.row.increase_avg_rate < 0 ? 'green' : 'red'
      //           }
      //         }, val)
      //       ])
      //     }
      //   },
      //   {
      //     title: '',
      //     key: 'third_price_avg',
      //     align: 'center',
      //     render: (h, params) => {
      //       params.column.title = '三方' + this.queryParam.inventoryname + '均值（元/吨）'
      //       let curIcon = params.row.increase_third_avg_rate < 0 ? '↓' : '↑'
      //       let val = '(' + curIcon + params.row.increase_third_avg_amount + ' ' + params.row.increase_third_avg_rate + '%)'
      //       return h('div',[
      //         h('div', {}, params.row.third_price_avg),
      //         h('div', {
      //           style: {
      //             display: params.index === 0 ? 'none' : '',
      //             color: params.row.increase_third_avg_rate < 0 ? 'green' : 'red'
      //           }
      //         }, val)
      //       ])
      //     }
      //   },
      //   {
      //     title: '',
      //     key: '',
      //     align: 'center',
      //     render: (h, params) => {
      //       params.column.title = '三方' + this.queryParam.inventoryname + '价格区间（元/吨）'
      //       return h('div', {}, params.row.third_bottom_price + '~' + params.row.third_top_price)
      //     }
      //   },
      //   {
      //     title: '采购量（吨）',
      //     key: 'quantity_sum',
      //     align: 'center',
      //     render: (h, params) => {
      //       let curIcon = params.row.increase_quantity_rate < 0 ? '↓' : '↑'
      //       let val = '(' + curIcon + params.row.increase_quantity_amount + ' ' + params.row.increase_quantity_rate + '%)'
      //       return h('div',[
      //         h('div', {}, params.row.quantity_sum),
      //         h('div', {
      //           style: {
      //             display: params.index === 0 ? 'none' : '',
      //             color: params.row.increase_quantity_rate < 0 ? 'green' : 'red'
      //           }
      //         }, val)
      //       ])
      //     }
      //   },
      //   {
      //     title: '',
      //     key: 'cost_sum',
      //     align: 'center',
      //     render: (h, params) => {
      //       params.column.title = this.queryParam.inventoryname + '总金额（元）'
      //       let curIcon = params.row.increase_cost_rate < 0 ? '↓' : '↑'
      //       let val = '(' + curIcon + params.row.increase_cost_amount + ' ' + params.row.increase_cost_rate + '%)'
      //       return h('div',[
      //         h('div', {}, params.row.cost_sum),
      //         h('div', {
      //           style: {
      //             display: params.index === 0 ? 'none' : '',
      //             color: params.row.increase_cost_rate < 0 ? 'green' : 'red'
      //           }
      //         }, val)
      //       ])
      //     }
      //   }
      // ],
      setSearchData: {
        date_month_st: {
          type: 'month',
          label: '时间',
          selected: '',
          width: 100,
          value: '',
          isdisable: false
        },
        date_month_et: {
          type: 'month_end',
          label: '-',
          selected: '',
          width: 100,
          value: '',
          isdisabled: false
        },
        inventoryname: {
          type: 'select',
          label: '名称',
          selectData: [
            {
              value: '轻油',
              label: '轻油'
            },
            {
              value: '重油',
              label: '重油'
            }
          ],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 115,
          value: '',
          filterable: true,
          isdisabled: false
        },
        area_name: {
          type: 'select',
          label: '区域',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 115,
          value: '',
          filterable: true,
          isdisabled: false
        },
        shipname: {
          type: 'select',
          label: '船舶',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 115,
          value: '',
          filterable: true
        },
        vendorname: {
          type: 'select',
          label: '供应商',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 180,
          value: '',
          filterable: true
        }
      }
    }
  },
  created () {
    this.getSysDate()
    this.getData()
  },
  beforeDestroy () {
    this.$store.commit('setMonthAnalyse', this.queryParam)
  },
  methods: {
    // 获取列表
    getList () {
      this.loading = true
      this.list = []
      this.columns = []
      this.lineData.xAxis = []
      this.lineData.data = [[], []]
      this.queryParam.date_month_st = this.date_month_st
      this.queryParam.date_month_et = this.date_month_et
      queryOilListByMonth(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          this.loading = false
          // this.list = res.data.Result
          this.lineData.legend[0] = this.queryParam.inventoryname
          this.columns.push({
            title: '月份',
            key: 'title',
            width: 145,
            align: 'center',
            fixed: 'left'
          })
          res.data.Result.forEach((e, idx) => {
            this.lineData.xAxis.push(e.date_month)
            this.lineData.data[0].push(e.price_avg)
            this.lineData.data[1].push(e.third_price_avg + ',' + e.third_bottom_price + '~' + e.third_top_price)
            this.columns.push({
              title: e.date_month,
              key: 'value' + idx,
              align: 'center',
              minWidth: 135
            })
          })
          this.columns.push({
            align: 'center',
            key: 'sumary',
            title: '合计',
            width: 135,
            fixed: 'right'
          })
          this.resetTableData(res.data.Result, this.list, 0, 'price_avg')
          this.resetTableData(res.data.Result, this.list, 1, 'increase_avg_amount')
          this.resetTableData(res.data.Result, this.list, 2, 'increase_avg_rate')
          this.resetTableData(res.data.Result, this.list, 3, 'third_price_avg')
          this.resetTableData(res.data.Result, this.list, 4, 'third_bottom_price', 'third_top_price')
          this.resetTableData(res.data.Result, this.list, 5, 'quantity_sum')
          this.resetTableData(res.data.Result, this.list, 6, 'cost_sum')
          this.list[0].sumary = (this.list[6].sumary / this.list[5].sumary).toFixed(2)
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    resetTableData (arr, data, index, str1, str2) {
      switch (index) {
        case 0:
          data.push({
            title: this.queryParam.inventoryname + '均值(元/吨)'
          })
          break
        case 1:
          data.push({
            title: '增长量'
          })
          break
        case 2:
          data.push({
            title: '增长率'
          })
          break
        case 3:
          data.push({
            title: '三方' + this.queryParam.inventoryname + '均值(元/吨)'
          })
          break
        case 4:
          data.push({
            title: '三方' + this.queryParam.inventoryname + '价格区间(元/吨)'
          })
          break
        case 5:
          data.push({
            title: '采购量(吨)'
          })
          break
        case 6:
          data.push({
            title: this.queryParam.inventoryname + '总金额(元)'
          })
          break
        default:
          break
      }
      let _curSum = 0
      let bottomSum = 0
      let topSum = 0
      let classObj = {}
      arr.forEach((item, idx) => {
        let valueIdx = `value${idx}`
        if (str2 !== undefined) {
          Object.assign(data[index], {
            ['value' + idx]: item[str1] + '~' + item[str2]
          })
        } else if (index === 1 || index === 2) {
          Object.assign(classObj, {
            ['value' + idx]: item[str1] < 0 ? 'downClass' : 'upClass'
          })
          Object.assign(data[index], {
            ['value' + idx]: item[str1] < 0 ? '↓' + item[str1] : '↑' + item[str1],
            cellClassName: classObj
          })
        } else {
          Object.assign(data[index], {
            ['value' + idx]: item[str1]
          })
        }
        if (index === 4) {
          bottomSum += parseFloat(data[index][valueIdx].split('~')[0])
          topSum += parseFloat(data[index][valueIdx].split('~')[1])
          _curSum = (bottomSum / arr.length).toFixed(2) + '~' + (topSum / arr.length).toFixed(2)
        } else {
          _curSum += parseFloat(data[index][valueIdx])
        }
      })
      let curSumary
      if (index === 1 || index === 2) {
        curSumary = '-'
      } else if (index === 5 || index === 6) {
        curSumary = _curSum.toFixed(2)
      } else if (index === 4) {
        curSumary = _curSum
      } else {
        curSumary = (_curSum / arr.length).toFixed(2)
      }
      Object.assign(data[index], {
        sumary: curSumary
      })
    },
    // 获取表格平均值
    // handleSummary ({ columns, data }) {
    //   // 以下合计为横向，月份为纵向排版
    //   const avg = {}
    //   columns.forEach((column, index) => {
    //     const key = column.key
    //     const bottomColumn = []
    //     const topColumn = []
    //     const values = data.map(item => {
    //       bottomColumn.push(Number(item.third_bottom_price))
    //       topColumn.push(Number(item.third_top_price))
    //       return Number(item[key])
    //     })
    //     if (index === 13) {
    //       avg[key] = {
    //         key,
    //         value: '合计'
    //       }
    //       return
    //     }
    //     if (index === 3) {
    //       const bottomVal = bottomColumn.reduce((prev, curr) => {
    //         const value = Number(curr)
    //         if (!isNaN(value)) {
    //           return prev + curr
    //         } else {
    //           return prev
    //         }
    //       }, 0)
    //       const topVal = topColumn.reduce((prev, curr) => {
    //         const value = Number(curr)
    //         if (!isNaN(value)) {
    //           return prev + curr
    //         } else {
    //           return prev
    //         }
    //       }, 0)
    //       avg[key] = {
    //         key,
    //         value: (bottomVal / data.length).toFixed(2) + '~' + (topVal / data.length).toFixed(2)
    //       }
    //       return
    //     }
    //     if (!values.every(value => isNaN(value))) {
    //       const v = values.reduce((prev, curr) => {
    //         const value = Number(curr)
    //         if (!isNaN(value)) {
    //           return prev + curr
    //         } else {
    //           return prev
    //         }
    //       }, 0)
    //       avg[key] = {
    //         key,
    //         value: index === 4 || index === 5 ? v.toFixed(2) : (v / data.length).toFixed(2)
    //       }
    //     } else {
    //       avg[key] = {
    //         key,
    //         value: ''
    //       }
    //     }
    //   })
    //   avg.price_avg.value = (avg.cost_sum.value / avg.quantity_sum.value).toFixed(2)
    //   return avg
    // },
    // 查询
    searchResults (e) {
      this.queryParam.shipname = e.shipname
      this.queryParam.vendorname = e.vendorname
      this.queryParam.inventoryname = e.inventoryname
      this.queryParam.area_name = e.area_name
      delete e.target
      this.getList()
    },
    selectOnChanged (e) {
      if (e.flag === 'month_start') {
        this.date_month_st = e.key
      } else if (e.flag === 'month_end') {
        this.date_month_et = e.key
      }
    },
    // 重置
    resetResults () {
      this.queryParam.shipname = ''
      this.queryParam.vendorname = ''
      this.queryParam.area_name = ''
      this.setSearchData.shipname.selected = ''
      this.setSearchData.vendorname.selected = ''
      this.setSearchData.area_name.selected = ''
      this.setSearchData.inventoryname.selected = this.setSearchData.inventoryname.selectData[1].value
      this.queryParam.inventoryname = this.setSearchData.inventoryname.selectData[1].value
      this.$store.state.setState.monthAnalyseParam = {}
      this.getSysDate()
    },
    // 数据导出
    exportData () {
      exportOilListByMonth(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          window.open(res.data.fileUrl, '_blank')
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 获取系统时间
    getSysDate () {
      if (Object.keys(this.$store.state.setState.monthAnalyseParam).length > 0 && this.$store.state.setState.monthAnalyseParam.date_month_st && this.$store.state.setState.monthAnalyseParam.date_month_et) {
        // 获取存储的入参
        let storeQuery = this.$store.state.setState.monthAnalyseParam
        this.setSearchData.date_month_st.selected = storeQuery.date_month_st
        this.setSearchData.date_month_et.selected = storeQuery.date_month_et
        this.date_month_st = storeQuery.date_month_st
        this.date_month_et = storeQuery.date_month_et
        this.getList()
      } else {
        API.getSysTime().then(res => {
          if (res.data.Code === 10000) {
            this.setSearchData.date_month_st.selected = res.data.now_date.substring(0, 5) + '01'
            this.setSearchData.date_month_et.selected = res.data.now_date.substring(0, 7)
            this.date_month_st = res.data.now_date.substring(0, 5) + '01'
            this.date_month_et = res.data.now_date.substring(0, 7)
            this.queryParam.date_month_st = this.date_month_st
            this.queryParam.date_month_et = this.date_month_et
            this.getList()
          }
        })
      }
    },
    getData () {
      API.materialWarehousename().then(res => { // 获取船舶
        if (res.data.Code === 10000) {
          res.data.Result.map(item => {
            this.setSearchData.shipname.selectData.push({
              value: item.shipname,
              label: item.shipname
            })
          })
        }
      })
      API.materialVendorname().then(res => { // 获取供应商
        if (res.data.Code === 10000) {
          res.data.Result.map(item => {
            this.setSearchData.vendorname.selectData.push({
              value: item.vendorname,
              label: item.vendorname
            })
          })
        }
      })
      // 获取区域
      queryAreaList().then(res => {
        if (res.data.Code === 10000) {
          res.data.Result.map(item => {
            this.setSearchData.area_name.selectData.push({
              value: item.unified_area_id,
              label: item.area_name
            })
          })
        }
      })
      this.queryParam.inventoryname = this.setSearchData.inventoryname.selectData[1].value
      this.setSearchData.inventoryname.selected = this.setSearchData.inventoryname.selectData[1].value
    }
  }
}
</script>
<style lang="less" scoped>
  .text_con {
    margin: 10px 0;
    button {
      float: right;
      color: #007DFF;
      font-size: 14px;
      font-weight: bold;
      margin-top: -5px;
      .ivu-icon {
        font-weight: bold;
      }
    }
  }
</style>
<style lang="less">
.data_list {
  .upClass {
    color: red;
  }
  .downClass {
    color: green;
  }
}
</style>

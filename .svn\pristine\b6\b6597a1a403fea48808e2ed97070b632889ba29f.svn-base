<template>
  <Drawer :title="modalData.title" v-model="modalData.modal" :data="modalData.data" @on-visible-change="modalShowHide" width="600">
    <Form label-colon>
      <Row>
        <Col span="8">
          <FormItem label="姓名">
            <span>{{ modalData.data ? modalData.data.down_seafarer_name : '-'}}</span>
          </FormItem>
        </Col>
        <Col span="8">
          <FormItem label="等级">
            <span>{{ modalData.data ? modalData.data.crt_level_name : '-'}}</span>
          </FormItem>
        </Col>
        <Col span="8">
          <FormItem label="职务">
            <span>{{ modalData.data ? modalData.data.down_duty_name : '-'}}</span>
          </FormItem>
        </Col>
      </Row>
    </Form>
    <Table
      :columns="columns"
      :data="tableData"
      :loading="loading"
      >
    </Table>
  </Drawer>
</template>

<script>
import API from '@/api/shipperOwner/crewSchedule'

export default ({
  props: {
    modalData: Object
  },
  data () {
    return {
      tableData: [],
      loading: false,
      columns: [
        {
          title: '证书名称',
          key:'chinese_name',
          align: 'center'  
        },
        {
          title: '到期日期',
          key: 'expire_date',
          align: 'center',
          render: (h, params) => {
            var expire_date = ''
            if (params.row.expire_date && params.row.expire_date.time) {
              expire_date = new Date(params.row.expire_date.time).getFullYear() + '-' + this.getTenStr(new Date(params.row.expire_date.time).getMonth() + 1) + '-' + this.getTenStr(new Date(params.row.expire_date.time).getDate())
            }
            return h('div', expire_date)
          }
        },
        {
          title: '剩余时间',
          key:'certificate_expire_left_days',
          align: 'center',
          render: (h, params) => {
            return h('div', params.row.certificate_expire_left_days + '天')
          }
        }
     ],
    }
  },
  methods: {
    getTenStr (str) {
      return str < 10 ? '0' + str : str
    },
    getList () {
      this.loading = true
      this.tableData = []
      API.querySeafarerCertificateInfo({seafarer_id: this.modalData.seafarer_id}).then(res => {
        this.loading = false
        if (res.data.Code === 10000) {
          this.tableData = res.data.Result.filter(item => item.has_expiring_certificate === 1)
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    modalShowHide (val) {
      if (val) {
        this.getList()
      }
    }
  }
})
</script>

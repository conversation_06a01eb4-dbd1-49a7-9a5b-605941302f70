<template>
  <div class="user-avatar-dropdown">
    <Dropdown>
      <Avatar :src="userAvatar" v-if="userAvatar" />
      <img src="../../../../assets/images/header.png" alt="" width="28" class="default_head" v-else />
      <span class="user-name user-fullname">{{ userFullName }} {{ companyName ? '|' : '| 兴通海运股份有限公司'}}</span>
      <span class="user-name">{{ companyName }}</span>
      <Icon size="18" color="#000" type="md-arrow-dropdown" v-show="companyList.length > 1"></Icon>
      <DropdownMenu v-show="companyList.length > 1" slot="list">
        <DropdownItem v-for="(list, index) in companyList" :key="index">
          <div @click="handleClick(list)">{{ list.company_name }}</div>
        </DropdownItem>
      </DropdownMenu>
    </Dropdown>
  </div>
</template>

<script>
import './user.less'
import { mapActions } from 'vuex'
import Cookies from 'js-cookie'
export default {
  name: 'User',
  props: {
    userAvatar: {
      type: String,
      default: ''
    },
    messageUnreadCount: {
      type: Number,
      default: 0
    }
  },
  data () {
    return {
      companyName: '',
      companyList: [],
      userFullName: ''
    }
  },
  created () {
    this.userFullName = Cookies.get('full_name') // localStorage.fullName
    this.companyName = Cookies.get('company_name')
    this.companyList = localStorage.companyList ? JSON.parse(localStorage.companyList) : []
  },
  methods: {
    ...mapActions(['handleLogOut']),
    logout () {
      this.handleLogOut().then(() => {
        this.$router.push({
          name: 'login'
        })
      })
    },
    message () {
      this.$router.push({
        name: 'message_page'
      })
    },
    handleClick (list) {
      let oriCompanyName = this.companyName
      if (list.company_name !== oriCompanyName) {
        this.companyName = list.company_name
        Cookies.set('company_name', this.companyName)
        this.$store.commit('setShipCompanyId', list.company_id)
        let companyList = JSON.parse(window.localStorage.companyList)
        companyList.map(item => {
          if (item.company_id === list.company_id) {
            window.localStorage.shipNameList = JSON.stringify(item.companyShipData)
            window.localStorage.bussiShipList = JSON.stringify(item.businessShipData)
            if (item.company_user_type === 3) { // 浏览者权限
              this.$store.commit('setAccess', 'vistor')
            }
            if (item.company_user_type === 4) { // 商务权限
              this.$store.commit('setAccess', ['business', 'vistor'])
            }
          }
        })
        location.reload()
      }
    }
  }
}
</script>
<style scoped>
.user-name {
  color: #000;
  margin: 0 5px 0 0;
}
.user-fullname {
  margin: 0 5px;
}
.default_head {
  margin-top: -5px;
  vertical-align: middle;
}
</style>

import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 题库列表
export function queryQuestionPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/exam/question/queryExamQuestionPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 题库新增
export function addExamQuestion (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/exam/question/addExamQuestion',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 题库编辑
export function updateExamQuestion (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/exam/question/updateExamQuestion',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 题库删除
export function deleteExamQuestion (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/exam/question/delExamQuestion',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  queryQuestionPage,
  addExamQuestion,
  updateExamQuestion,
  deleteExamQuestion
}
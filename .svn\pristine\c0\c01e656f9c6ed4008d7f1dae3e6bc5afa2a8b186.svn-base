<template>
  <Modal v-model="modalData.modal" width="800" :title="modalData.title" @on-visible-change="modalShowHide" :mask-closable="false">
    <Card>
      <search @searchResults='searchResults' :setSearch='setSearchData' @selectOnChanged='selectOnChanged' @resetResults='resetResults'></search>
    </Card>
    <Card>
      <Table border :loading="loading" :columns="columns" :data="planList" @on-row-click="rowClick" @on-row-dblclick="rowSelect"></Table>
    </Card>
    <div slot="footer">
      <Button @click="handleCancel">取消</Button>
      <Button @click="importSave" type="primary">导入</Button>
    </div>
  </Modal>
</template>

<script>
import search from '_c/search' // 查询组件
import API from '@/api/voyagePlan'
import basicAPI from '@/api/basicData'

export default ({
  props: {
    modalData: Object
  },
  components: {
    search
  },
  data () {
    return {
      loading: false,
      defaultMonth: null,
      tabLineData: {},
      shipList: [],
      planList: [],
      listQuery: {
        plan_month: '', // 月份
        ship_id: '', // 船舶id
        business_model: '' // 船舶类型
      },
      setSearchData: {
        date: {
          type: 'month',
          label: '日期',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 140,
          value: '',
          filterable: true
        },
        ship_id: {
          type: 'select',
          label: '船舶',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 120,
          value: '',
          filterable: true
        }
      },
      columns: [
        {
          title: '受载期',
          key: 'loading_date',
          align: 'center',
          render: (h, params) => {
            let dateStr = params.row.loading_date
            if (params.row.loading_date.length === 2) {
              let dateArr = params.row.loading_date
              dateStr = dateArr[0].substring(5, 10) + ' - ' + dateArr[1].substring(5, 10)
            }
            return h('div', {}, dateStr)
          }
        },
        {
          title: '航次',
          key: 'voyage_no',
          align: 'center',
          render: (h, params) => {
            return h('div', {}, 'V.' + params.row.voyage_no)
          }
        },
        {
          title: '航线',
          key: '',
          align: 'center',
          render: (h, params) => {
            let voyageLine = params.row.load_port_names + ' - ' + params.row.unload_port_names
            return h('div', {}, voyageLine)
          }
        },
        {
          title: '货品',
          key: 'goods_names',
          align: 'center'
        },
        {
          title: '货量(吨)',
          key: 'goods_amount',
          align: 'center'
        }
      ]
    }
  },
  methods: {
    getList () { // 获取列表
      this.planList = []
      this.loading = true
      API.queryGroupVmpPlanList(this.listQuery).then(res => {
        this.loading = false
        if (res.data.Code === 10000) {
          this.planList = res.data.Result.length > 0 ? res.data.Result[0].planList : []
        }
      })
    },
    getBasicList () { // 获取基础数据
      basicAPI.queryBasicShipList({ business_model_in: '1,2,3' }).then(res => {
        if (res.data.Code === 10000) {
          this.shipList = res.data.Result
          this.setSearchData.ship_id.selectData = this.shipList.map(item => {
            return {
              value: item.ship_id,
              label: item.ship_name
            }
          })
        }
      })
    },
    rowClick (row) {
      this.tabLineData = row
    },
    rowSelect (row) { // 双击选中
      this.$emit('importBack', row)
    },
    importSave () {
      if (this.tabLineData) {
        this.$emit('importBack', this.tabLineData)
      } else {
        this.$Message.warning('请先选择一条数据再导入！')
      }
    },
    handleCancel () {
      this.modalData.modal = false
    },
    selectOnChanged (e) {
      if (e.flag === 'month_start') {
        this.listQuery.plan_month = e.key
      }
    },
    // 查询
    searchResults (e) {
      this.listQuery.ship_id = e.ship_id
      this.getList()
    },
    // 重置
    resetResults () {
      this.listQuery = {
        plan_month: this.defaultMonth,
        ship_id: '',
        business_model: ''
      }
      this.setSearchData.date.selected = this.defaultMonth
      this.setSearchData.ship_id.selected = ''
      this.getList()
    },
    // 获取当前年月
    getCurrentYearMonth() {
      const today = new Date()
      const currentDay = today.getDate()
      const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0).getDate() // 本月最后一天
      const daysLeftInMonth = lastDayOfMonth - currentDay  // 本月最后一天 - 当前日期（天）
      let currentYear, currentMonth

      if (daysLeftInMonth <= 5) { // 如果是月底 则默认是下个月 与月底差5天情况下则视为月底
        const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1)
        // const nextMonth = new Date(2024, 12, 1)
        currentYear = nextMonth.getFullYear()
        currentMonth = nextMonth.getMonth() + 1
      } else { // 如果非月底，则默认本月
        currentYear = today.getFullYear()
        currentMonth = today.getMonth() + 1
      }
      let month = currentMonth < 10 ? '0' + currentMonth : currentMonth
      return currentYear + '-' + month
    },
    modalShowHide (val) {
      if (val) {
        this.defaultMonth = this.getCurrentYearMonth()
        this.listQuery.plan_month = this.defaultMonth
        this.setSearchData.ship_id.selected = this.modalData.data.ship_id
        this.listQuery.ship_id = this.modalData.data.ship_id
        this.setSearchData.date.selected = this.defaultMonth
        this.getBasicList()
        this.getList()
      }
    }
  }
})
</script>

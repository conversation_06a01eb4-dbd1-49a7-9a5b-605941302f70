<template>
  <div>
    <Card>
      <search @searchResults='searchResults' :setSearch='setSearchData' @resetResults='resetResults'></search>
      <div class="extra" slot="extra">
        <formAction :setFormAction='setFormAction' @handleCreate="handleEditModal('create')"></formAction>
      </div>
    </Card>
    <Card style="margin-top: 10px">
      <div class="tab_btn">
        <Button :type="listQuery.theme_state === '1' ? 'primary' : 'default'" size="large" @click="changeTab('1')">进行中</Button>
        <Button :type="listQuery.theme_state === '0' ? 'primary' : 'default'" size="large" @click="changeTab('0')">已计划</Button>
        <Button :type="listQuery.theme_state === '2' ? 'primary' : 'default'" size="large" @click="changeTab('2')">已完成</Button>
      </div>
      <Table border :loading="listLoading" ref="selection" :columns="columns" :data="list"></Table>
      <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
        :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
    </Card>
    <detailModal :modalData="modalData" @callback="getList"></detailModal><!-- 新增/编辑弹窗内容 -->
    <loadingStatisticsModal :loadingStatisticsModalData="loadingStatisticsModalData"></loadingStatisticsModal><!-- 进行中/已完成，统计弹窗 -->
    <plannedStatisticsModal :plannedStatisticsModalData="plannedStatisticsModalData"></plannedStatisticsModal><!-- 已计划统计弹窗 -->
    <fileManageModal :fileModalData="fileModalData" @callback="getList"></fileManageModal><!-- 文件管理 -->
    <summaryModal :summaryModalData="summaryModalData"></summaryModal><!-- 汇总 -->
    <!-- 二维码内容 -->
    <Modal v-model="wxCodeModal" fullscreen footer-hide class="modal_bg">
      <div class="login_head">
        <img class="head-logo" :src="logo" alt="">
        <span class="head_text">
          <span class="heead-text-cnname">兴通海运股份有限公司</span>
          <span class="heead-text-enname">XINGTONG SHIPPING CO., LTD.</span>
        </span>
      </div>
      <div class="wxCodeModal_content">
        <h3>{{ wxCodeModalTitle }}</h3>
        <div v-if="wxCodeModal" class="qrcode" ref="qrCodeUrl">
          <img class="logo_boat" :src="logoBoat" />
        </div>
        <p>请打开微信进行扫码签到</p>
        <div class="info_class">
          <span>报名人数： {{ listModalData.member_apply_num }}人</span>
          <span>签到人数： {{ listModalData.member_sign_num }}人</span>
        </div>
      </div>
      <div slot="close">
        <Button type="text" style="color: #fff;font-size: 24px;" @click="cancelWxCode">取消</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import search from '_c/search' // 查询组件
import QRCode from 'qrcodejs2'
import formAction from '_c/formAction' // 表单操作组件
import logo from '@/assets/images/logo_wxcode.png'
import logoBoat from '@/assets/images/logo-boat.png'
import API from '@/api/examSystem/trainingModule/trainingTopics'
import detailModal from './detailModal'
import loadingStatisticsModal from './loadingStatisticsModal'
import plannedStatisticsModal from './plannedStatisticsModal'
import fileManageModal from './fileManageModal'
import summaryModal from './summaryModal'
export default {
  components: {
    search,
    formAction,
    detailModal,
    loadingStatisticsModal,
    plannedStatisticsModal,
    fileManageModal,
    summaryModal
  },
  data () {
    return {
      logo,
      logoBoat,
      listLoading: false,
      total: 0,
      listCurrent: 1,
      setFormAction: {
        operation: ['create']
      },
      codeTime: null,
      listQuery: {
        theme_id: '',
        theme_type: '',
        theme_title: '',
        train_dept: '',
        train_lecturer: '',
        train_date_start: '',
        train_date_end: '',
        start_time_start: '',
        start_time_end: '',
        end_time_start: '',
        end_time_end: '',
        theme_state: '1', // 状态（0计划；1进行；2完成）
        pageSize: 10,
        pageIndex: 1
      },
      setSearchData: {
        theme_title: {
          type: 'text',
          label: '标题',
          width: 180,
          value: '',
          isdisable: false
        },
        train_lecturer: {
          type: 'text',
          label: '姓名',
          width: 180,
          value: '',
          isdisable: false
        }
      },
      wxCodeModal: false,
      wxCodeModalTitle: '',
      listModalData: {
        member_apply_num: '',
        member_dine_num: '',
        member_sign_num: '',
        theme_id: ''
      },
      modalData: {
        modal: false,
        title: '',
        type: '',
        data: undefined
      },
      loadingStatisticsModalData: {
        modal: false,
        title: '',
        data: undefined
      },
      plannedStatisticsModalData: {
        modal: false,
        title: '',
        data: undefined
      },
      fileModalData: {
        modal: false,
        title: '',
        data: undefined
      },
      summaryModalData: {
        modal: false,
        title: '',
        data: undefined
      },
      list: [],
      columns: [
        {
          title: '标题',
          key: 'theme_title',
          align: 'center'
        },
        {
          title: '类型',
          key: 'theme_type_name',
          align: 'center',
          width: 120
        },
        {
          title: '部门',
          key: 'train_dept',
          align: 'center'
        },
        {
          title: '姓名',
          key: 'train_lecturer',
          align: 'center'
        },
        {
          title: '总时长',
          key: 'total_time',
          align: 'center',
          width: 120
        },
        {
          title: '培训/调研时间',
          key: 'train_date',
          align: 'center',
          width: 120,
          render: (h, params) => {
            return h('div', {}, params.row.train_date.substring(0, 10))
          }
        },
        {
          title: '操作',
          align: 'center',
          width: 430,
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: {
                  icon: 'md-paper',
                  size: 'small'
                },
                style: {
                  margin: '0 5px',
                  display: params.row.theme_state !== '2' ? 'inline-block' : 'none'
                },
                on: {
                  click: () => {
                    this.handleEditModal('update', params.row)
                  }
                }
              }, '编辑'),
              h('Button', {
                props: {
                  icon: 'md-paper',
                  size: 'small'
                },
                style: {
                  margin: '0 5px',
                  display: params.row.theme_state === '0' ? 'inline-block' : 'none'
                },
                on: {
                  click: () => {
                    this.handleDelete(params.row.theme_id)
                  }
                }
              }, '删除'),
              h('Button', {
                props: {
                  icon: 'md-paper',
                  size: 'small'
                },
                style: {
                  margin: '0 5px',
                  display: params.row.theme_state !== '0' || (params.row.theme_state === '0' && params.row.theme_type === '1') ? 'inline-block' : 'none' // theme_type:1.培训 2.调研
                },
                on: {
                  click: () => {
                    this.handleEditModal('statistics', params.row)
                  }
                }
              }, '统计'),
              h('Button', {
                props: {
                  icon: 'md-paper',
                  size: 'small'
                },
                style: {
                  margin: '0 5px',
                  display: params.row.theme_type === '1' && params.row.theme_state === '1' ? 'inline-block' : 'none' // （theme_state:0计划；1进行；2完成）
                },
                on: {
                  click: () => {
                    this.handleEditModal('wxCode', params.row)
                  }
                }
              }, '二维码'),
              h('Button', {
                props: {
                  icon: 'md-paper',
                  size: 'small'
                },
                style: {
                  margin: '0 5px',
                  display: params.row.theme_state === '2' ? 'inline-block' : 'none'
                },
                on: {
                  click: () => {
                    this.handleEditModal('summary', params.row)
                  }
                }
              }, '汇总'),
              h('Button', {
                props: {
                  icon: 'md-paper',
                  size: 'small'
                },
                style: {
                  margin: '0 5px'
                },
                on: {
                  click: () => {
                    this.handleEditModal('file', params.row)
                  }
                }
              }, '文件管理'),
              h('Button', {
                props: {
                  icon: 'md-paper',
                  size: 'small'
                },
                style: {
                  margin: '0 5px',
                  display: params.row.theme_state === '0' ? 'inline-block' : 'none'
                },
                on: {
                  click: () => {
                    this.handleEditModal('changeStart', params.row)
                  }
                }
              }, '开始'),
              h('Button', {
                props: {
                  icon: 'md-paper',
                  size: 'small'
                },
                style: {
                  margin: '0 5px',
                  display: params.row.theme_state === '1' ? 'inline-block' : 'none'
                },
                on: {
                  click: () => {
                    this.handleEditModal('changeEnding', params.row)
                  }
                }
              }, '结束')
            ])
          }
        }
      ]
    }
  },
  created () {
    this.getList()
  },
  beforeDestroy () {
    clearInterval(this.codeTime)
  },
  methods: {
    // 获取列表
    getList () {
      this.listLoading = true
      API.queryTrainThemePage(this.listQuery).then(res => {
        if (res.data.Code === 10000) {
          this.listLoading = false
          this.list = res.data.Result
          this.total = res.data.Total
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // tab切换
    changeTab (type) { // 0计划；1进行；2完成
      this.listQuery.pageIndex = 1
      if (type === '1') {
        this.columns[6].width = '430'
        this.listQuery.theme_state = '1'
      } else if (type === '0') {
        this.columns[6].width = '430'
        this.listQuery.theme_state = '0'
      } else if (type === '2') {
        this.listQuery.theme_state = '2'
        this.columns[6].width = '300'
      }
      this.getList()
    },
    // 取消清除二维码数据
    cancelWxCode () {
      var qrcode = new QRCode(this.$refs.qrCodeUrl, {
        text: '',
        width: 0,
        height: 0,
        colorDark: '#000',
        colorLight: '#fff',
        correctLevel: QRCode.CorrectLevel.H
      })
      qrcode.clear()
      this.wxCodeModal = false
      clearInterval(this.codeTime)
    },
    // 开启弹窗
    handleEditModal (type, row) {
      if (type === 'create') {
        this.modalData.modal = true
        this.modalData.title = '新增'
        this.modalData.data = undefined
        this.modalData.type = type
      } else if (type === 'update') {
        this.modalData = {
          modal: true,
          data: row,
          title: '编辑',
          type: type,
          state: this.listQuery.theme_state
        }
      } else if (type === 'changeStart' || type === 'changeEnding') { // 开始/结束
        let data = {
          theme_id: row.theme_id,
          theme_type: row.theme_type,
          theme_title: row.theme_title,
          train_dept: row.train_dept,
          train_lecturer: row.train_lecturer,
          train_date: row.train_date,
          total_time: row.total_time,
          start_time: row.start_time,
          end_time: row.end_time,
          theme_state: type === 'changeStart' ? '1' : '2'
        }
        API.trainThemeUpdateList(data).then(res => {
          if (res.data.Code === 10000) {
            this.getList()
          } else {
            this.$Message.error(res.data.Message)
          }
        })
      } else if (type === 'statistics') { // 统计
        if (this.listQuery.theme_state !== '0') {
          this.loadingStatisticsModalData.modal = true
          this.loadingStatisticsModalData.data = row
          this.loadingStatisticsModalData.title = row.theme_title
        } else {
          this.plannedStatisticsModalData.modal = true
          this.plannedStatisticsModalData.data = row
          this.plannedStatisticsModalData.title = row.theme_title
        }
      } else if (type === 'file') { // 文件管理
        this.fileModalData.modal = true
        this.fileModalData.data = row
        this.fileModalData.title = row.theme_title + '文件管理'
      } else if (type === 'wxCode') { // 二维码生成
        this.wxCodeModal = true
        this.wxCodeModalTitle = row.theme_title
        this.listModalData = {}
        API.queryTrainMemberSum({ theme_id: row.theme_id }).then(res => {
          if (res.data.Code === 10000) {
            this.listModalData = {
              member_apply_num: res.data.member_apply_num,
              member_dine_num: res.data.member_dine_num,
              member_sign_num: res.data.member_sign_num,
              theme_id: row.theme_id
            }
            this.codeTime = setInterval(() => {
              this.getJoinNum(row.theme_id)
            }, 3000)
          }
          this.$nextTick(() => {
            this.creatQrCode()
          })
        })
      } else if (type === 'summary') {
        this.summaryModalData = {
          modal: true,
          title: row.theme_title + '考评结果',
          data: row
        }
      }
    },
    // 获取参与人员数据
    getJoinNum (theme_id) {
      API.queryTrainMemberSum({ theme_id: theme_id }).then(res => {
        if (res.data.Code === 10000) {
          Object.assign(this.listModalData, {
            member_apply_num: res.data.member_apply_num,
            member_dine_num: res.data.member_dine_num,
            member_sign_num: res.data.member_sign_num
          })
        }
      })
    },
    // 小程序二维码
    creatQrCode () {
      var qrcode = new QRCode(this.$refs.qrCodeUrl, {
        text: 'http://info.xtshipping.com/wx_exam?theme_id=' + this.listModalData.theme_id,
        width: 300,
        height: 300,
        colorDark: '#000',
        colorLight: '#fff',
        correctLevel: QRCode.CorrectLevel.H
      })

      qrcode.clear()
      qrcode.makeCode('http://info.xtshipping.com/wx_exam?theme_id=' + this.listModalData.theme_id)
    },
    // 删除
    handleDelete (d) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确认删除该培训课题？</p>',
        loading: true,
        onOk: () => {
          API.trainThemedeleteAll({ theme_id: d }).then(res => {
            if (res.data.Code === 10000) {
              this.loading = false
              this.$Modal.remove()
              this.$Message.success(res.data.Message)
              this.getList()
            } else {
              this.loading = false
              this.$Modal.remove()
              this.$Message.error(res.data.Message)
            }
          })
        }
      })
    },
    // 查询
    searchResults (e) {
      this.listCurrent = 1
      this.listQuery.pageIndex = 1
      this.listQuery.theme_title = e.theme_title
      this.listQuery.train_lecturer = e.train_lecturer
      this.getList()
    },
    // 重置
    resetResults () {
      this.listCurrent = 1
      this.listQuery = {
        theme_id: '',
        theme_type: '',
        theme_title: '',
        train_dept: '',
        train_lecturer: '',
        train_date_start: '',
        train_date_end: '',
        start_time_start: '',
        start_time_end: '',
        end_time_start: '',
        end_time_end: '',
        theme_state: '1',
        pageSize: 10,
        pageIndex: 1
      }
      this.setSearchData.theme_title.value = ''
      this.setSearchData.train_lecturer.value = ''
      this.getList()
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    }
  }
}
</script>
<style lang="less" scoped>
.tab_btn {
  margin-bottom: 15px;
  button {
    margin-right: 10px;
  }
}
.login_head {
  .head-logo {
    margin: 7px 10px 0 0;
    vertical-align: top;
  }
  .head_text {
    display: inline-block;
    .heead-text-cnname {
      display: block;
      font-size: 24px;
      letter-spacing: 3px;
    }
    .heead-text-enname {
      display: block;
      font-weight: 500;
      font-size: 16px;
      letter-spacing: 1px;
    }
  }
}
.modal_bg {
  color: #fff;
}
.wxCodeModal_content {
  text-align: center;
  margin-top: 70px;
  > h3 {
    font-size: 48px;
    font-weight: normal;
  }
  > p {
    font-size: 24px;
  }
  .info_class {
    position: relative;
    text-align: left;
    width: 70%;
    margin: 90px auto 0;
    span {
      font-size: 30px;
    }
    span:last-child {
      right: 0;
      position: absolute;
    }
  }
  .qrcode {
    margin: 30px auto 10px;
    display: inline-flex;
    border: 12px solid #fff;
    border-radius: 10px;
    height: 324px;
    overflow: hidden;
    justify-content: center;
    align-items: center;
    .logo_boat {
      position: absolute;
      width: 50px;
      height: 50px;
    }
    img {
      width: 132px;
      height: 132px;
      background-color: #fff;
      padding: 6px;
      box-sizing: border-box;
    }
  }
}
</style>
<style lang="less">
.modal_bg {
  .ivu-modal-wrap {
    z-index: 9999 !important;
  }
  .ivu-modal-content {
    box-shadow: none;
    background-color: transparent;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center center;
    background-image: url(../../../../assets/images/wxcode_bg.png);
  }
  .ivu-btn-text:hover {
    background-color: transparent;
  }
}
</style>

<template>
  <Modal draggable v-model="modalData.modal" width="600" :title="modalData.title" @on-visible-change="modalShowHide" :mask-closable="false">
    <CheckboxGroup v-model="checkAllGroup" @on-change="checkAllGroupChange">
      <Checkbox style="margin-bottom: 10px;" v-for="(item, idx) in groupList" :key="item.value" :label="item.value" border>{{ item.name }}</Checkbox>
    </CheckboxGroup>
    <div slot="footer">
      <div style="position: absolute; margin-top: 5px;">
        <Checkbox
          :indeterminate="indeterminate"
          :value="checkAll"
          @click.prevent.native="handleCheckAll">全选</Checkbox>
      </div>
      <Button @click="handleCancel">取消</Button>
      <Button @click="handleSave" type="primary" :disabled="checkAllGroup.length === 0">推送</Button>
    </div>
  </Modal>
</template>

<script>
import API from '@/api/voyagePlanSet'
import sAPI from '@/api/voyagePlan'

export default ({
  props: {
    modalData: Object
  },
  data () {
    return {
      indeterminate: false,
      checkAll: false,
      checkAllGroup: [], // 双向绑定选中的人员open_id集合
      groupList: [], // 带有人员id及姓名的集合 初始数据
      allUserList: [], // 所有人员open_id集合
      userLen: 0
    }
  },
  methods: {
    getList () {
      API.queryVmpSendConfigList().then(res => {
        this.allUserList = []
        this.groupList = []
        if (res.data.Code === 10000) {
          res.data.Result.map(item => {
            let _curArr = item.open_ids.split(',')
            let _nameArr = item.user_names.split(',')
            _curArr.map((list, idx) => {
              this.allUserList.push(list)
              this.groupList.push({
                name: _nameArr[idx],
                value: list
              })
            })
          })
          this.userLen = this.allUserList.length
          this.checkAllGroup = this.allUserList
          this.checkAllGroupChange(this.allUserList)
        }
      })
    },
    handleCancel () { // 取消
      this.modalData.modal = false
    },
    handleSave () { // 推送
      if (this.allUserList.length === 0) {
        this.$Message.warning('请至少选择一位人员！')
        return
      }
      let _param = {
        plan_month: this.modalData.plan_month,
        open_ids: this.checkAllGroup.join()
      }
      console.log(_param)
      sAPI.sendTipsToYunZhiJia(_param).then(res => {
        if (res.data.Code === 10000) {
          this.modalData.modal = false
          this.$Message.success(res.data.Message)
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    handleCheckAll () {
      if (this.indeterminate) {
        this.checkAll = false
      } else {
        this.checkAll = !this.checkAll
      }
      this.indeterminate = false

      if (this.checkAll) {
        this.checkAllGroup = this.allUserList
      } else {
        this.checkAllGroup = []
      }
    },
    checkAllGroupChange (data) {
      // this.allUserList = data
      if (data.length === this.userLen) {
        this.indeterminate = false
        this.checkAll = true
      } else if (data.length > 0) {
        this.indeterminate = true
        this.checkAll = false
      } else {
        this.indeterminate = false
        this.checkAll = false
      }
    },
    modalShowHide (val) {
      if (val)  {
        this.getList()
      }
    }
  }
})
</script>

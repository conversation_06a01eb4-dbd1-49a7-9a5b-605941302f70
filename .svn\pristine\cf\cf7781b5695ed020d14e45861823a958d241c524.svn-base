<template>
  <div>
    <div class="forms_area">
      <div style="display: flex; align-items: baseline;">
        <span>Date: {{ item.biztime.split(' ')[0].substring(0, 7) }}</span>
        <span style="margin-left: 20px;">Days: {{ item.xtgf_datanum }}</span>
        <span style="margin-left: 20px;">CUR:USD</span>
      </div>
      <!-- OPEX -->
      <div class="forms_col">
        <div>
          <span class="box_title">OPEX</span>
          <!-- <span>
            <span style="font-weight: bold;">BUDGET/DAY:</span>
            ${{ (contractOpexTotal / 31).toFixed(2) }}
          </span>
          <span style="margin-left: 20px;">
            <span style="font-weight: bold;">OPEX/DAY:</span>
            ${{ (contractActualTotal / 31).toFixed(2) }}
          </span> -->
        </div>
        <div class="table_area">
          <table>
            <thead class="table_head">
              <th style="width: 135px;">ITEMS</th>
              <th style="width: 95px;">OPEX/month</th>
              <th style="width: 135px;">Actual Amt/month</th>
              <th style="width: 190px;">Variance  Budget vs Actual</th>
              <th style="width: 80px;">%</th>
              <th style="width: 35px;">Remarks</th>
            </thead>
            <tbody class="table_body">
              <tr v-for="(item, idx) in contractList" :key="idx">
                <td>{{ enName(item.material_name) }}</td>
                <td>{{ (!item.xtgf_cgapplyqty || item.xtgf_cgapplyqty === '') ? '' : `$${item.xtgf_cgapplyqty}` }}</td>
                <td>
                  <input style="width: 130px; height: 20px;" v-model="item.applyqty" @input="applyChange" @blur="handleBlur(item)"/>
                </td>
                <td>{{ getDisPay(item) }}</td>
                <td>{{ getDisRate(item) }}</td>
                <td>
                  <textarea ref="textarea1" class="remark_text" rows="1" v-model="item.entrycomment" @input="limitMaxRows"/>
                  <!-- <input style="width: 530px; height: 20px;" v-model="item.entrycomment"/> -->
                </td>
              </tr>
              <tr>
                <td style="font-weight: bold;">Subtotal</td>
                <td>${{ contractOpexTotal }}</td>
                <td>${{ contractActualTotal }}</td>
                <td>{{ contractVsTotal }}</td>
                <td>{{ contractRateTotal ? `${contractRateTotal}%` : '' }}</td>
                <td></td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- PRE-DELIVERY COST -->
      <div class="forms_col">
        <div class="box_title">PRE-DELIVERY COST</div>
        <div class="table_area">
          <table>
            <thead class="table_head">
              <th style="width: 135px;">ITEMS</th>
              <th style="width: 95px;">OPEX/month</th>
              <th style="width: 135px;">Actual Amt/month</th>
              <th style="width: 190px;">Variance  Budget vs Actual</th>
              <th style="width: 80px;">%</th>
              <th style="width: 135px;">Remarks</th>
            </thead>
            <tbody class="table_body">
              <tr v-for="(item, idx) in shipList" :key="idx">
                <td>{{ enName(item.material_name) }}</td>
                <td>{{ (!item.xtgf_cgapplyqty || item.xtgf_cgapplyqty === '') ? '' : `$${item.xtgf_cgapplyqty}` }}</td>
                <td>
                  <input style="width: 130px; height: 20px;" v-model="item.applyqty" @input="applyChange" @blur="handleBlur(item)"/>
                </td>
                <td>{{ getDisPay(item) }}</td>
                <td>{{ getDisRate(item) }}</td>
                <td>
                  <textarea ref="textarea2" class="remark_text" rows="1" v-model="item.entrycomment" @input="limitMaxRows"/>
                  <!-- <input style="width: 530px; height: 20px;" v-model="item.entrycomment"/> -->
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- AUTHORIZATION FOR EXPENDITURE -->
      <div class="forms_col">
        <div class="box_title">AUTHORIZATION FOR EXPENDITURE</div>
        <div class="table_area">
          <table>
            <thead class="table_head">
              <th style="width: 135px;">ITEMS</th>
              <th style="width: 95px;">OPEX/month</th>
              <th style="width: 135px;">Actual Amt/month</th>
              <th style="width: 190px;">Variance  Budget vs Actual</th>
              <th style="width: 80px;">%</th>
              <th style="width: 135px;">Remarks</th>
            </thead>
            <tbody class="table_body">
              <tr v-for="(item, idx) in otherList" :key="idx">
                <td>{{ enName(item.material_name) }}</td>
                <td></td> <!--{{ item.xtgf_cgapplyqty === '' ? '' : `$${item.xtgf_cgapplyqty}` }}-->
                <td>
                  <input style="width: 130px; height: 20px;" v-model="item.applyqty" @input="applyChange" @blur="handleBlur(item)"/>
                </td>
                <td></td>
                <td></td>
                <td>
                  <textarea ref="textarea3" class="remark_text" rows="1" v-model="item.entrycomment" @input="limitMaxRows"/>
                </td>
              </tr>
            </tbody>
            <!-- <tbody class="table_body">
              <tr>
                <td style="font-weight: bold;">Total</td>
                <td>${{ totalOpex() }}</td>
                <td>${{ totalActual() }}</td>
                <td>{{ totalDis() }}</td>
                <td>{{ totalDisRate() ? `${totalDisRate()}%` : '' }}</td>
                <td></td>
              </tr>
            </tbody> -->
          </table>
        </div>
      </div>
      <div class="forms_col">
        <div class="box_title">MONTHLY EXPENSE STATISTICS</div>
        <div class="table_area">
          <table>
            <thead class="table_head">
              <th style="width: 135px;">ITEMS</th>
              <th style="width: 95px;">OPEX/month</th>
              <th style="width: 135px;">Actual Amt/month</th>
              <th style="width: 190px;">Variance  Budget vs Actual</th>
              <th style="width: 80px;">%</th>
              <th style="width: 135px;"></th>
            </thead>
            <tbody class="table_body">
              <tr>
                <td style="font-weight: bold;">Total</td>
                <td>${{ totalOpex() }}</td>
                <td>${{ totalActual() }}</td>
                <td>{{ totalDis() }}</td>
                <td>{{ totalDisRate() ? `${totalDisRate()}%` : '' }}</td>
                <td style="width: 530px;"></td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <Button class="save_btn" type="primary" @click="handleSubmit">Submit</Button>
    </div>
  </div>
</template>
<script>
import BAPI from '@/api/erpSys/common'
import API from '@/api/shipApplication/index'

export default ({
  props: {
    list: Array,
    item: Object
  },
  data () {
    return {
      shipId: '', // 当前船舶id
      opexObj: {},
      contractOpexTotal: 0, // 单项Opex统计
      contractActualTotal: 0, // 单项实际费用统计
      contractVsTotal: 0, // 单项差异统计
      contractRateTotal: 0, // 单项差异百分比
      totalOpexPay: 0, // 预算总费用
      totalActualPay: 0, // 实际总费用
      contractList: [
        {
          "entrycomment":"",
          "material_number":"06020101-00001",
          "material_masterid_modelnum": "合同约定项目",
          "material_name":"船员工资",
          "baseunit_number":"UM-204",
          "unit_number":"UM-204",
          "linetype_number":"020-2"
        },
        {
          "entrycomment":"",
          "material_number":"06020101-00002",
          "material_masterid_modelnum": "合同约定项目",
          "material_name":"船员支出",
          "baseunit_number":"UM-204",
          "unit_number":"UM-204",
          "linetype_number":"020-2"
        },
        {
          "entrycomment":"",
          "material_number":"06020101-00003",
          "material_masterid_modelnum": "合同约定项目",
          "material_name":"船员伙食",
          "baseunit_number":"UM-204",
          "unit_number":"UM-204",
          "linetype_number":"020-2"
        },
        {
          "entrycomment":"",
          "material_number":"06020101-00004",
          "material_masterid_modelnum": "合同约定项目",
          "material_name":"物料备件",
          "baseunit_number":"UM-204",
          "unit_number":"UM-204",
          "linetype_number":"020-2"
        },
        {
          "entrycomment":"",
          "material_number":"06020101-00005",
          "material_masterid_modelnum": "合同约定项目",
          "material_name":"滑油费用",
          "baseunit_number":"UM-204",
          "unit_number":"UM-204",
          "linetype_number":"020-2"
        },
        {
          "entrycomment":"",
          "material_number":"06020101-00006",
          "material_masterid_modelnum": "合同约定项目",
          "material_name":"船舶维修",
          "baseunit_number":"UM-204",
          "unit_number":"UM-204",
          "linetype_number":"020-2"
        },
        {
          "entrycomment":"",
          "material_number":"06020101-00007",
          "material_masterid_modelnum": "合同约定项目",
          "material_name":"船员家庭健康保险",
          "baseunit_number":"UM-204",
          "unit_number":"UM-204",
          "linetype_number":"020-2"
        },
        {
          "entrycomment":"",
          "material_number":"06020101-00008",
          "material_masterid_modelnum": "合同约定项目",
          "material_name":"船舶管理费",
          "baseunit_number":"UM-204",
          "unit_number":"UM-204",
          "linetype_number":"020-2"
        },
        {
          "entrycomment":"",
          "material_number":"06020101-00009",
          "material_masterid_modelnum": "合同约定项目",
          "material_name":"其他",
          "baseunit_number":"UM-204",
          "unit_number":"UM-204",
          "linetype_number":"020-2"
        }
      ], // 合同约定列表
      shipList: [
        {
          "entrycomment":"",
          "material_number":"06020102-00001",
          "material_masterid_modelnum": "接船费用",
          "material_name":"船员工资",
          "baseunit_number":"UM-204",
          "unit_number":"UM-204",
          "linetype_number":"020-2"
        },
        {
          "entrycomment":"",
          "material_number":"06020102-00002",
          "material_masterid_modelnum": "接船费用",
          "material_name":"船员支出",
          "baseunit_number":"UM-204",
          "unit_number":"UM-204",
          "linetype_number":"020-2"
        },
        {
          "entrycomment":"",
          "material_number":"06020102-00003",
          "material_masterid_modelnum": "接船费用",
          "material_name":"船员伙食",
          "baseunit_number":"UM-204",
          "unit_number":"UM-204",
          "linetype_number":"020-2"
        },
        {
          "entrycomment":"",
          "material_number":"06020102-00004",
          "material_masterid_modelnum": "接船费用",
          "material_name":"物料备件",
          "baseunit_number":"UM-204",
          "unit_number":"UM-204",
          "linetype_number":"020-2"
        },
        {
          "entrycomment":"",
          "material_number":"06020102-00005",
          "material_masterid_modelnum": "接船费用",
          "material_name":"滑油费用",
          "baseunit_number":"UM-204",
          "unit_number":"UM-204",
          "linetype_number":"020-2"
        },
        {
          "entrycomment":"",
          "material_number":"06020102-00006",
          "material_masterid_modelnum": "接船费用",
          "material_name":"船舶维修",
          "baseunit_number":"UM-204",
          "unit_number":"UM-204",
          "linetype_number":"020-2"
        },
        {
          "entrycomment":"",
          "material_number":"06020102-00007",
          "material_masterid_modelnum": "接船费用",
          "material_name":"船员家庭健康保险",
          "baseunit_number":"UM-204",
          "unit_number":"UM-204",
          "linetype_number":"020-2"
        },
        {
          "entrycomment":"",
          "material_number":"06020102-00008",
          "material_masterid_modelnum": "接船费用",
          "material_name":"船舶管理费",
          "baseunit_number":"UM-204",
          "unit_number":"UM-204",
          "linetype_number":"020-2"
        },
        {
          "entrycomment":"",
          "material_number":"06020102-00009",
          "material_masterid_modelnum": "接船费用",
          "material_name":"其他",
          "baseunit_number":"UM-204",
          "unit_number":"UM-204",
          "linetype_number":"020-2"
        }
      ], // 接船列表
      otherList: [
        // {
        //   "entrycomment":"",
        //   "material_number":"06020103-00002",
        //   "material_masterid_modelnum": "预算外费用",
        //   "material_name":"船员工资",
        //   "baseunit_number":"UM-204",
        //   "unit_number":"UM-204",
        //   "linetype_number":"020-2"
        // },
        {
          "entrycomment":"",
          "material_number":"06020103-00003",
          "material_masterid_modelnum": "预算外费用",
          "material_name":"船员支出",
          "baseunit_number":"UM-204",
          "unit_number":"UM-204",
          "linetype_number":"020-2"
        },
        // {
        //   "entrycomment":"",
        //   "material_number":"06020103-00004",
        //   "material_masterid_modelnum": "预算外费用",
        //   "material_name":"船员伙食",
        //   "baseunit_number":"UM-204",
        //   "unit_number":"UM-204",
        //   "linetype_number":"020-2"
        // },
        {
          "entrycomment":"",
          "material_number":"06020103-00005",
          "material_masterid_modelnum": "预算外费用",
          "material_name":"物料备件",
          "baseunit_number":"UM-204",
          "unit_number":"UM-204",
          "linetype_number":"020-2"
        },
        // {
        //   "entrycomment":"",
        //   "material_number":"06020103-00006",
        //   "material_masterid_modelnum": "预算外费用",
        //   "material_name":"滑油费用",
        //   "baseunit_number":"UM-204",
        //   "unit_number":"UM-204",
        //   "linetype_number":"020-2"
        // },
        {
          "entrycomment":"",
          "material_number":"06020103-00007",
          "material_masterid_modelnum": "预算外费用",
          "material_name":"船舶维修",
          "baseunit_number":"UM-204",
          "unit_number":"UM-204",
          "linetype_number":"020-2"
        },
        // {
        //   "entrycomment":"",
        //   "material_number":"06020103-00008",
        //   "material_masterid_modelnum": "预算外费用",
        //   "material_name":"船员家庭健康保险",
        //   "baseunit_number":"UM-204",
        //   "unit_number":"UM-204",
        //   "linetype_number":"020-2"
        // },
        // {
        //   "entrycomment":"",
        //   "material_number":"06020103-00009",
        //   "material_masterid_modelnum": "预算外费用",
        //   "material_name":"船舶管理费",
        //   "baseunit_number":"UM-204",
        //   "unit_number":"UM-204",
        //   "linetype_number":"020-2"
        // },
        {
          "baseunit_number": "UM-204",
          "entrycomment": '',
          "linetype_number": "020-2",
          "material_masterid_modelnum": "预算外费用",
          "material_name": "高风险补贴",
          "material_number": "06020103-00001",
          "unit_number": "UM-204"
        },
        {
          "entrycomment":"",
          "material_number":"06020103-00011",
          "material_masterid_modelnum": "预算外费用",
          "material_name":"坞修",
          "baseunit_number":"UM-204",
          "unit_number":"UM-204",
          "linetype_number":"020-2"
        },
        {
          "entrycomment":"",
          "material_number":"06020103-00010",
          "material_masterid_modelnum": "预算外费用",
          "material_name":"其他",
          "baseunit_number":"UM-204",
          "unit_number":"UM-204",
          "linetype_number":"020-2"
        }
      ] // 预算外费用
    }
  },
  methods: {
    handleSubmit () {
      let _paramList = [...this.contractList, ...this.shipList, ...this.otherList]
      const data = {
        data:[{
          billno: this.item.billno,
          id: this.item.id,
          billentry: _paramList
        }]
      }
      let dataParam = {
        data: JSON.stringify(data),
        token: localStorage.getItem('token'),
        url: '/ierp/kapi/v2/xtgf/pm/pm_purapplybill/purchaseedit',
        user: 'cengyabin'
      }
      BAPI.transferStation(dataParam).then(res => {
        if (res.data.Result.status) {
          this.$Message.success("Success")
          this.$emit('updateBack')
        } else {
          this.$Message.error(res.data.Result.message)
        }
      })
    },
    applyChange (e) {
      this.$forceUpdate()
    },
    handleBlur (item) {
      if (item.applyqty === '') {
        item.applyqty = '0'
      }
    },
    adjustAllHeights () { // 加载时适配高度
      this.$nextTick(() => {
        // 遍历所有 textarea，使用 this.$refs 访问每个 ref 引用的 textarea
        Object.keys(this.$refs).forEach(refName => {
          this.$refs[refName].forEach(textarea => {
            textarea.style.height = 'auto'
            textarea.style.height = `${textarea.scrollHeight}px`
          })
        })
      })
    },
    totalOpex () { // 计算总额度 预算
      let contractTotal = this.contractList.reduce((sum, item) => {
        const qty = parseFloat(item.xtgf_cgapplyqty) || 0
        return sum + qty
      }, 0)
      this.contractOpexTotal = contractTotal.toFixed(2)
      let shipTotal = this.shipList.reduce((sum, item) => {
        const qty = parseFloat(item.xtgf_cgapplyqty) || 0
        return sum + qty
      }, 0)
      let otherTotal = this.otherList.reduce((sum, item) => {
        const qty = parseFloat(item.xtgf_cgapplyqty) || 0
        return sum + qty
      }, 0)
      this.totalOpexPay = (contractTotal + shipTotal + otherTotal).toFixed(2)
      return this.totalOpexPay
    },
    totalActual () { // 计算总额度 实际
      let contractTotal = this.contractList.reduce((sum, item) => {
        const qty = parseFloat(item.applyqty) || 0
        return sum + qty
      }, 0)
      this.contractActualTotal = contractTotal.toFixed(2)
      let shipTotal = this.shipList.reduce((sum, item) => {
        const qty = parseFloat(item.applyqty) || 0
        return sum + qty
      }, 0)
      let otherTotal = this.otherList.reduce((sum, item) => {
        const qty = parseFloat(item.applyqty) || 0
        return sum + qty
      }, 0)
      this.totalActualPay = (contractTotal + shipTotal + otherTotal).toFixed(2)
      return this.totalActualPay
    },
    totalDis () { // 总价差额
      if (parseFloat(this.totalActual()) === 0) {
        return ''
      }
      this.contractVsTotal = (this.contractActualTotal - this.contractOpexTotal).toFixed(2)
      return (this.totalActualPay - this.totalOpexPay).toFixed(2)
    },
    totalDisRate () { // 总价差额率
      if (parseFloat(this.totalActual()) === 0) {
        return ''
      }
      this.contractRateTotal = ((this.contractActualTotal - this.contractOpexTotal) / this.contractOpexTotal * 100).toFixed(2)
      return ((this.totalActualPay - this.totalOpexPay) / this.totalOpexPay * 100).toFixed(2)
    },
    getDisPay (item) { // 计算差异额
      let payStr = ''
      if (item.xtgf_cgapplyqty && item.applyqty) {
        payStr = (parseFloat(item.applyqty) - parseFloat(item.xtgf_cgapplyqty)).toFixed(2)
      }
      return payStr
    },
    getDisRate (item) { // 计算差异率
      let payRate = ''
      if (item.xtgf_cgapplyqty && item.applyqty) {
        payRate = (parseFloat(item.applyqty) - parseFloat(item.xtgf_cgapplyqty)) / parseFloat(item.xtgf_cgapplyqty)
      }
      if (payRate) return (payRate * 100).toFixed(2) + '%'
      else return ''
    },
    limitMaxRows (e) { // 输入是自适配高度
      const textarea = e.target
      const maxRows = 15 // 最大行数
      const lineHeight = 18 // 每行的高度 (px)

      // 自动调整高度
      textarea.style.height = 'auto'
      textarea.style.height = `${textarea.scrollHeight}px`

      // 如果超出最大行数，限制高度为最大行数
      const maxHeight = lineHeight * maxRows
      if (textarea.scrollHeight > maxHeight) {
        textarea.style.height = `${maxHeight}px`
        textarea.style.overflowY = 'auto' // 启用滚动条
      } else {
        textarea.style.overflowY = 'hidden' // 隐藏滚动条
      }
    },
    getShipDetail (shipId) { // 获取船舶详细配置数据
      API.queryImosShipCostList({ ship_id: shipId }).then(res => {
        if (res.data.Code === 10000 && res.data.Result.length > 0) {
          this.opexObj = res.data.Result[0]
          // 合同约定项目
          if (this.opexObj.crew_wages) Object.assign(this.contractList[0], { 'xtgf_cgapplyqty': (parseFloat(this.opexObj.crew_wages) * parseFloat(this.item.xtgf_datanum)).toFixed(2) })
          if (this.opexObj.crew_expenses) Object.assign(this.contractList[1], { 'xtgf_cgapplyqty': (parseFloat(this.opexObj.crew_expenses) * parseFloat(this.item.xtgf_datanum)).toFixed(2) })
          if (this.opexObj.crew_meals) Object.assign(this.contractList[2], { 'xtgf_cgapplyqty': (parseFloat(this.opexObj.crew_meals) * parseFloat(this.item.xtgf_datanum)).toFixed(2) })
          if (this.opexObj.material_parts) Object.assign(this.contractList[3], { 'xtgf_cgapplyqty': (parseFloat(this.opexObj.material_parts) * parseFloat(this.item.xtgf_datanum)).toFixed(2) })
          if (this.opexObj.oil_cost) Object.assign(this.contractList[4], { 'xtgf_cgapplyqty': (parseFloat(this.opexObj.oil_cost) * parseFloat(this.item.xtgf_datanum)).toFixed(2) })
          if (this.opexObj.ship_maintenance) Object.assign(this.contractList[5], { 'xtgf_cgapplyqty': (parseFloat(this.opexObj.ship_maintenance) * parseFloat(this.item.xtgf_datanum)).toFixed(2) })
          if (this.opexObj.crew_insurance) Object.assign(this.contractList[6], { 'xtgf_cgapplyqty': (parseFloat(this.opexObj.crew_insurance) * parseFloat(this.item.xtgf_datanum)).toFixed(2) })
          if (this.opexObj.ship_management_fee) Object.assign(this.contractList[7], { 'xtgf_cgapplyqty': (parseFloat(this.opexObj.ship_management_fee) * parseFloat(this.item.xtgf_datanum)).toFixed(2) })
          if (this.opexObj.other_cost) Object.assign(this.contractList[8], { 'xtgf_cgapplyqty': (parseFloat(this.opexObj.other_cost) * parseFloat(this.item.xtgf_datanum)).toFixed(2) })
          // 接船费用
          if (this.opexObj.cost_crew_wages) Object.assign(this.shipList[0], { 'xtgf_cgapplyqty': (parseFloat(this.opexObj.cost_crew_wages) * parseFloat(this.item.xtgf_datanum)).toFixed(2) })
          if (this.opexObj.cost_crew_expenses) Object.assign(this.shipList[1], { 'xtgf_cgapplyqty': (parseFloat(this.opexObj.cost_crew_expenses) * parseFloat(this.item.xtgf_datanum)).toFixed(2) })
          if (this.opexObj.cost_crew_meals) Object.assign(this.shipList[2], { 'xtgf_cgapplyqty': (parseFloat(this.opexObj.cost_crew_meals) * parseFloat(this.item.xtgf_datanum)).toFixed(2) })
          if (this.opexObj.cost_material_parts) Object.assign(this.shipList[3], { 'xtgf_cgapplyqty': (parseFloat(this.opexObj.cost_material_parts) * parseFloat(this.item.xtgf_datanum)).toFixed(2) })
          if (this.opexObj.cost_oil_cost) Object.assign(this.shipList[4], { 'xtgf_cgapplyqty': (parseFloat(this.opexObj.cost_oil_cost) * parseFloat(this.item.xtgf_datanum)).toFixed(2) })
          if (this.opexObj.cost_ship_maintenance) Object.assign(this.shipList[5], { 'xtgf_cgapplyqty': (parseFloat(this.opexObj.cost_ship_maintenance) * parseFloat(this.item.xtgf_datanum)).toFixed(2) })
          if (this.opexObj.cost_crew_insurance) Object.assign(this.shipList[6], { 'xtgf_cgapplyqty': (parseFloat(this.opexObj.cost_crew_insurance) * parseFloat(this.item.xtgf_datanum)).toFixed(2) })
          if (this.opexObj.cost_ship_management_fee) Object.assign(this.shipList[7], { 'xtgf_cgapplyqty': (parseFloat(this.opexObj.cost_ship_management_fee) * parseFloat(this.item.xtgf_datanum)).toFixed(2) })
          if (this.opexObj.cost_other_cost) Object.assign(this.shipList[8], { 'xtgf_cgapplyqty': (parseFloat(this.opexObj.cost_other_cost) * parseFloat(this.item.xtgf_datanum)).toFixed(2) })
          this.$forceUpdate()
        }
      })
    },
    objectDesign (list, item) {
      let idx
      switch (item.material_name.trim()) {
        case '船员工资':
          idx = list.findIndex(obj => obj.material_name.trim() === item.material_name.trim())
          if (idx < 0) return
          list[idx].id = item.id ? item.id : ''
          item.applyqty ? Object.assign(list[idx], {applyqty: parseFloat(item.applyqty)}) : ''
          item.xtgf_cgapplyqty ? Object.assign(list[idx], {xtgf_cgapplyqty: parseFloat(item.xtgf_cgapplyqty)}) : ''
          list[idx].entrycomment = item.entrycomment
          break
        case '船员支出':
          idx = list.findIndex(obj => obj.material_name.trim() === item.material_name.trim())
          list[idx].id = item.id ? item.id : ''
          item.applyqty ? Object.assign(list[idx], {applyqty: parseFloat(item.applyqty)}) : ''
          item.xtgf_cgapplyqty ? Object.assign(list[idx], {xtgf_cgapplyqty: parseFloat(item.xtgf_cgapplyqty)}) : ''
          list[idx].entrycomment = item.entrycomment
          break
        case '船员伙食':
          idx = list.findIndex(obj => obj.material_name.trim() === item.material_name.trim())
          if (idx < 0) return
          list[idx].id = item.id ? item.id : ''
          item.applyqty ? Object.assign(list[idx], {applyqty: parseFloat(item.applyqty)}) : ''
          item.xtgf_cgapplyqty ? Object.assign(list[idx], {xtgf_cgapplyqty: parseFloat(item.xtgf_cgapplyqty)}) : ''
          list[idx].entrycomment = item.entrycomment
          break
        case '物料备件':
          idx = list.findIndex(obj => obj.material_name.trim() === item.material_name.trim())
          list[idx].id = item.id ? item.id : ''
          item.applyqty ? Object.assign(list[idx], {applyqty: parseFloat(item.applyqty)}) : ''
          item.xtgf_cgapplyqty ? Object.assign(list[idx], {xtgf_cgapplyqty: parseFloat(item.xtgf_cgapplyqty)}) : ''
          list[idx].entrycomment = item.entrycomment
          break
        case '滑油费用':
          idx = list.findIndex(obj => obj.material_name.trim() === item.material_name.trim())
          if (idx < 0) return
          list[idx].id = item.id ? item.id : ''
          item.applyqty ? Object.assign(list[idx], {applyqty: parseFloat(item.applyqty)}) : ''
          item.xtgf_cgapplyqty ? Object.assign(list[idx], {xtgf_cgapplyqty: parseFloat(item.xtgf_cgapplyqty)}) : ''
          list[idx].entrycomment = item.entrycomment
          break
        case '船舶维修':
          idx = list.findIndex(obj => obj.material_name.trim() === item.material_name.trim())
          list[idx].id = item.id ? item.id : ''
          item.applyqty ? Object.assign(list[idx], {applyqty: parseFloat(item.applyqty)}) : ''
          item.xtgf_cgapplyqty ? Object.assign(list[idx], {xtgf_cgapplyqty: parseFloat(item.xtgf_cgapplyqty)}) : ''
          list[idx].entrycomment = item.entrycomment
          break
        case '船员家庭健康保险':
          idx = list.findIndex(obj => obj.material_name.trim() === item.material_name.trim())
          if (idx < 0) return
          list[idx].id = item.id ? item.id : ''
          item.applyqty ? Object.assign(list[idx], {applyqty: parseFloat(item.applyqty)}) : ''
          item.xtgf_cgapplyqty ? Object.assign(list[idx], {xtgf_cgapplyqty: parseFloat(item.xtgf_cgapplyqty)}) : ''
          list[idx].entrycomment = item.entrycomment
          break
        case '船舶管理费':
          idx = list.findIndex(obj => obj.material_name.trim() === item.material_name.trim())
          if (idx < 0) return
          list[idx].id = item.id ? item.id : ''
          item.applyqty ? Object.assign(list[idx], {applyqty: parseFloat(item.applyqty)}) : ''
          item.xtgf_cgapplyqty ? Object.assign(list[idx], {xtgf_cgapplyqty: parseFloat(item.xtgf_cgapplyqty)}) : ''
          list[idx].entrycomment = item.entrycomment
          break
        case '其他':
          idx = list.findIndex(obj => obj.material_name.trim() === item.material_name.trim())
          list[idx].id = item.id ? item.id : ''
          item.applyqty ? Object.assign(list[idx], {applyqty: parseFloat(item.applyqty)}) : ''
          item.xtgf_cgapplyqty ? Object.assign(list[idx], {xtgf_cgapplyqty: parseFloat(item.xtgf_cgapplyqty)}) : ''
          list[idx].entrycomment = item.entrycomment
          break
        case '高风险补贴':
          idx = list.findIndex(obj => obj.material_name.trim() === item.material_name.trim())
          list[idx].id = item.id ? item.id : ''
          item.applyqty ? Object.assign(list[idx], {applyqty: parseFloat(item.applyqty)}) : ''
          item.xtgf_cgapplyqty ? Object.assign(list[idx], {xtgf_cgapplyqty: parseFloat(item.xtgf_cgapplyqty)}) : ''
          list[idx].entrycomment = item.entrycomment
          break
        case '坞修':
          idx = list.findIndex(obj => obj.material_name.trim() === item.material_name.trim())
          list[idx].id = item.id ? item.id : ''
          item.applyqty ? Object.assign(list[idx], {applyqty: parseFloat(item.applyqty)}) : ''
          item.xtgf_cgapplyqty ? Object.assign(list[idx], {xtgf_cgapplyqty: parseFloat(item.xtgf_cgapplyqty)}) : ''
          list[idx].entrycomment = item.entrycomment
          break
        default:
      }
    },
    enName (str) {
      let enStr = ''
      switch (str.trim()) {
        case '船员工资':
          enStr = 'CREW WAGES'
          break
        case '船员支出':
          enStr = 'CREW EXPENSE'
          break
        case '船员伙食':
          enStr = 'CREW VICTUALS'
          break
        case '物料备件':
          enStr = 'SPARES/STORES'
          break
        case '滑油费用':
          enStr = 'LUBOIL'
          break
        case '船舶维修':
          enStr = 'REPAIR'
          break
        case '船员家庭健康保险':
          enStr = 'INSURANCE'
          break
        case '船舶管理费':
          enStr = 'MANAGEMENT FEE'
          break
        case '其他':
          enStr = 'OTHERS'
          break
        case '高风险补贴':
          enStr = 'HIGH RISK BONUS'
          break
        case '坞修':
          enStr = 'DOCKING EXPENSES'
          break
        default:

      }
      return enStr
    },
    totalPay (str) {
      if (!str || str === '') return ''
      const totalPay = parseFloat(str) * parseFloat(this.item.xtgf_datanum)
      return totalPay.toFixed(2)
    }
  },
  mounted () {
    if (this.item && this.item.xtgf_vesselid_id) {
      this.getShipDetail(this.item.xtgf_vesselid_id)
    }
    if (!this.list) return
    // if (!this.list.some(item => item.xtgf_cgapplyqty)) {
    //   this.getShipDetail(this.item.xtgf_vesselid_id)
    // }
    this.list.map(item => {
      if (item.material_masterid_modelnum === '合同约定项目') {
        // this.contractList.push(item)
        this.objectDesign(this.contractList, item)
      }
      if (item.material_masterid_modelnum === '接船费用') {
        // this.shipList.push(item)
        this.objectDesign(this.shipList, item)
      }
      if (item.material_masterid_modelnum === '预算外费用') {
        // this.otherList.push(item)
        this.objectDesign(this.otherList, item)
      }
    })
    this.$nextTick(() => {
      this.$forceUpdate()
      this.adjustAllHeights()
    })
  }
})
</script>
<style scoped>
  .box_title {
    display: inline-block;
    background: #073d85;
    color: #fff;
    height: 28px;
    padding: 8px 16px;
    margin-right: 16px;
    box-sizing: border-box;
    cursor: pointer;
    text-decoration: none;
    position: relative;
    transition: color .3s ease-in-out;
  }
  .forms_area {
    display: inline-grid;
    padding: 0 30px;
    overflow: auto;
    width: 100%;
    height: calc(100vh - 50px);
    font-family: Verdana, Arial, Helvetica, sans-serif;
    font-size: 10px;
  }
  .forms_col {
    margin-bottom: 20px;
  }
  .table_area {
    width: 1180px;
    border: solid 1px #ccc;
    padding: 8px;
  }
  .table_area table {
    background-color: White;
    border-collapse: collapse;
    border-spacing: 0px;
  }
  .table_head {
    background-color: rgb(181,180,182);
    color: White;
    font-weight: bold;
    text-align: center;
  }
  .table_head th {
    padding: 0px 2px 0px 2px;
    border: 1px solid #c0c0c0;
  }
  .table_body td {
    text-align: center;
    margin: 0;
    padding: 0px 2px 0px 2px;
    border: 1px solid #c0c0c0;
  }
  .table_body input, .table_body textarea {
    margin: 2px 0;
    border: 1px solid #C4C4C4;
  }
  .remark_text {
    width: 530px;
    min-height: 20px;
    max-height: 300px;
    height: auto;
    overflow: hidden;
    resize: none;
    line-height: 1;
    margin-bottom: -3px !important;
    padding: 2px;
  }
  /* .list_area {
    display: flex;
    align-content: center;
  }
  .list_area:last-child {
    border-bottom: 1px solid #ccc;
  }
  .list_area:last-child .list_span:nth-last-child(1), .list_area:last-child .list_span:nth-last-child(2), .list_area:last-child .list_span:nth-last-child(3) {
    border-bottom: none;
  }
  .title_font {
    width: 540px;
    padding: 5px;
    border: 1px solid #ccc;
    border-bottom: none;
  }
  .title_font::before {
    content: "";
    margin-right: 5px;
    border-left: 5px solid #195bdd;
  }
  .list_title {
    background: #195bdd;
    color: #fff;
    font-size: 14px;
    font-weight: bold;
  }
  .list_span {
    display: inline-block;
    width: 180px;
    line-height: 35px;
    border: 1px solid #ccc;
    padding: 5px;
  }
  .cost_list_content {
    margin-right: 30px;
    margin-bottom: 30px;
  }
  */
  .save_btn {
    background: #204681 !important;
    width: 80px;
    margin-left: 1100px;
    margin-bottom: 20px;
  }
</style>

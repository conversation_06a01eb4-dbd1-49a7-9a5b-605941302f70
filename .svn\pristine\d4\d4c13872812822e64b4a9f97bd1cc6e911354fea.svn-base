<template>
  <div>
    <Card>
      <search @searchResults="searchResults" :setSearch="setSearchData" @resetResults="resetResults" searchText="AI搜索" resetText="智能重载"></search>
    </Card>
    <Card style="margin-top: 12px;">
      <Row>
        <Col :span="loading ? 18 : 24">
          <Table
            row-key="id"
            stripe 
            border
            ref="selection"
            :loading="loading"
            :columns="columns"
            :data="list"
            :load-data="handleLoadData"
            update-show-children
            @on-row-click="tableClick">
            <div slot="loading">
              <img style="margin-top: 5px; width: 66px; height: 66px;" :src="LoadingImg" />
              <div style="font-size: 12px;font-weight: bold;">AI兴仔解析中...</div>
            </div>
          </Table>
          <!-- <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listQuery.pageIndex"
          :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/> -->
        </Col>
        <Col v-if="loading" :span="loading ? 6 : 0">
          <Card style="margin: 0 0 0 20px; font-size: 12px;">
            <span v-html="typeText"></span>
          </Card>
        </Col>
      </Row>
    </Card>

    <!-- <div>
      <ul id="messages">
        <li v-for="(msg, index) in messages" :key="index">{{ msg }}</li>
      </ul>
      <input v-model="newMessage" @keyup.enter="sendMessage" placeholder="Type a message" />
    </div> -->
    <CertificateDrawer :modalData="certificateModal"></CertificateDrawer>
  </div>
</template>

<script>
import search from '_c/search'
import LoadingImg from '@/assets/images/xingzi_loading.gif'
import API from '@/api/shipperOwner/crewSchedule'
import CertificateDrawer from './drawer/index'
// import io from 'socket.io-client'
// import axios from 'axios'

export default ({
  components: {
    search,
    CertificateDrawer
  },
  data () {
    return {
      LoadingImg,
      certificateModal: {
        modal: false,
        title: '证书详情',
        seafarer_id: ''
      },
      interval: null,
      typeText: '',
      thinkText: '',
      messages: [], // socket测试
      newMessage: '', // socket测试
      socket: null,
      loading: false,
      listQuery: {
        // pageSize: 10,
        // pageIndex: 1,
        vessel_id_in: '',
        apply_duty_id_in: '',
        crt_level_id_in: '',
        on_board_days: '',
        down_board_days: ''
      },
      setSearchData: {
        vessel_id_in: {
          label: '船舶',
          type: 'selectMultiple',
          width: 188,
          selected: [],
          isdisable: false,
          placeholder: '请选择船舶,默认全部',
          selectData: []
        },
        apply_duty_id_in: {
          label: '职务',
          type: 'selectMultiple',
          width: 198,
          selected: [],
          isdisable: false,
          placeholder: '请选择职务，默认全部',
          selectData: []
        },
        crt_level_id_in: {
          label: '证书等级',
          type: 'selectMultiple',
          width: 225,
          selected: [],
          isdisable: false,
          placeholder: '请选择证书等级，默认全部',
          selectData: []
        },
        on_board_days: {
          label: '在船天数',
          type: 'text',
          width: 120,
          value: '',
          isdisable: false,
          placeholder: '请过滤在船天数'
        },
        down_board_days_st: {
          label: '休假时长',
          type: 'text',
          width: 120,
          value: '',
          isdisable: false,
          placeholder: '休假最短时长'
        },
        down_board_days_ed: {
          label: '-',
          type: 'text',
          width: 120,
          value: '',
          isdisable: false,
          placeholder: '休假最高时长'
        },
        certificate_expire_date: {
          label: '证书到期时长',
          type: 'text',
          width: 140,
          value: '',
          isdisable: false,
          placeholder: '请过滤证书到期时长'
        }
      },
      expandRow: [],
      total: 0,
      columns: [
        // {
        //   type: 'expand',
        //   width: 50,
        //   render: (h, params) => {

        //   }
        // },
        {

          title: '船名',
          key: 'vessel_name',
          align: 'center',
          tree: true
        },
        {
          title: '职务',
          key: 'apply_duty_name',
          align: 'center'
        },
        {
          title: '在船人员',
          align: 'center',
          children: [
            {
              title: '交班人员',
              key: 'seafarer_name',
              align: 'center'
            },
            {
              title: '上船时间',
              key: 'on_board_date',
              align: 'center',
              render: (h, params) => {
                let onBoardDate = params.row.on_board_date ? params.row.on_board_date.split(' ')[0] : ''
                return h('div', {}, onBoardDate)
              }
            },
            {
              title: '在船时长',
              key: 'on_board_days',
              align: 'center',
              render: (h, params) => {
                let onBoardDays = params.row.on_board_days ? params.row.on_board_days + '天' : ''
                return h('div', {}, onBoardDays)
              }
            }
          ]
        },
        {
          title: '休假人员',
          align: 'center',
          children: [
            {
              title: '接班人员',
              key: 'down_seafarer_name',
              align: 'center'
            },
            {
              title: '下船时间',
              key: 'down_board_date',
              align: 'center'
            },
            {
              title: '下船时长',
              key: 'down_board_days',
              align: 'center'
            },
            {
              title: '证书等级',
              key: 'crt_level_name',
              align: 'center'
            },
            {
              title: '表现评级',
              key: 'evaluate_value',
              align: 'center'
            },
            {
              title: '是否证书将到期',
              key: 'expir',
              align: 'center',
              render: (h, params) => {
                let expir = params.row.has_expiring_certificate === '1' ? '是' : '否'
                return h('div', {
                  on: {
                    click: () => {
                      if (params.row.has_expiring_certificate === '1') {
                        this.certificateModal = {
                          modal: true,
                          title: '证书详情',
                          data: params.row,
                          seafarer_id: params.row.down_seafarer_id 
                        }
                      }
                    }
                  }
                }, expir)
              }
            },
            {
              title: '是否有江证',
              key: 'jiang',
              align: 'center',
              render: (h, params) => {
                let jiang = params.row.have_jiang_certificate === '1'? '是' : '否'
                return h('div', {}, jiang)
              }
            }
          ]
        }
      ],
      list: [{}]
    }
  },
  methods: {
    getList () {
      this.list = [{},{},{},{},{},{},{},{}]
      this.loading = true
      clearInterval(this.interval)
      API.querySeafarerInfoMatchAllList(this.listQuery).then(res => { // querySeafarerInfoList
        if (res.data.Code === 10000) {
          setTimeout(() => {
            this.loading = false
            let idDis = 0
            this.list = res.data.Result.map((item, idx) => {
              if (idx > 0 && item.apply_duty_id !== res.data.Result[idx - 1].apply_duty_id) { // 说明一个新的职务开头
                idDis = idx
              }
              Object.assign(item, {
                id: (idx + 1) < 10 ? '0' + (idx + 1) : idx + 1,
                down_seafarer_name: (item.shiftCrew && item.shiftCrew[idx - idDis]) ? item.shiftCrew[idx - idDis].seafarer_name : '-',
                down_seafarer_id: (item.shiftCrew && item.shiftCrew[idx - idDis])? item.shiftCrew[idx - idDis].seafarer_id : '',
                down_duty_name: (item.shiftCrew && item.shiftCrew[idx - idDis] && item.shiftCrew[idx - idDis].apply_duty_name)? item.shiftCrew[idx - idDis].apply_duty_name : '-',
                down_board_date: (item.shiftCrew && item.shiftCrew[idx - idDis] && item.shiftCrew[idx - idDis].down_board_date)? item.shiftCrew[idx - idDis].down_board_date.split(' ')[0] : '-',
                down_board_days: (item.shiftCrew && item.shiftCrew[idx - idDis] && item.shiftCrew[idx - idDis].down_board_days)? item.shiftCrew[idx - idDis].down_board_days + '天' : '-',
                crt_level_name: (item.shiftCrew && item.shiftCrew[idx - idDis] && item.shiftCrew[idx - idDis].crt_level_name)? item.shiftCrew[idx - idDis].crt_level_name : '-',
                evaluate_value: (item.shiftCrew && item.shiftCrew[idx - idDis] && item.shiftCrew[idx - idDis].evaluate_value)? item.shiftCrew[idx - idDis].evaluate_value : '-',
                has_expiring_certificate: (item.shiftCrew && item.shiftCrew[idx - idDis] && item.shiftCrew[idx - idDis].has_expiring_certificate === '1') ? item.shiftCrew[idx - idDis].has_expiring_certificate : '',
                have_jiang_certificate: (item.shiftCrew && item.shiftCrew[idx - idDis] && item.shiftCrew[idx - idDis].have_jiang_certificate === '1') ? item.shiftCrew[idx - idDis].have_jiang_certificate : ''
              })
              if (item.shiftCrew && item.shiftCrew.length > 0) {
                item.shiftCrew.map((list, listIdx) => {
                  let listId = (listIdx + 1) < 10 ? '0' + (listIdx + 1) : listIdx + 1
                  let _tempList = Object.assign(list, {
                    id: item.id + listId,
                    down_seafarer_name: list.seafarer_name,
                    down_seafarer_id: list.seafarer_id,
                    down_board_date: list.down_board_date.split(' ')[0],
                    down_board_days: list.down_board_days + '天',
                    down_duty_name: list.apply_duty_name,
                    crt_level_name: list.crt_level_name,
                    evaluate_value: list.evaluate_value,
                    has_expiring_certificate: list.has_expiring_certificate,
                    have_jiang_certificate: list.have_jiang_certificate
                  })
                  if (list.has_expiring_certificate === '1') { // 如果有证书将预期，加个样式
                    list.cellClassName = {expir: 'warning_style'}
                    if (list.have_jiang_certificate === '1') { // 如果有江证，加个样式
                      if (list.cellClassName) {
                        Object.assign(list.cellClassName, {
                          jiang: 'jiang_style'
                        })
                      } else {
                        Object.assign(list, {
                          cellClassName: {
                            jiang: 'jiang_style'
                          }
                        })
                      }
                    }
                  }
                  delete _tempList.seafarer_name
                  delete _tempList.apply_duty_name
                  delete _tempList.on_board_date
                  delete _tempList.on_board_days
                  return _tempList                   
                })
              }
              if (item.has_expiring_certificate === '1') { // 如果有证书将预期，加个样式
                item.cellClassName = {expir: 'warning_style'}
                if (item.have_jiang_certificate === '1') { // 如果有江证，加个样式
                  if (item.cellClassName) {
                    Object.assign(item.cellClassName, {
                      jiang: 'jiang_style'
                    })
                  } else {
                    Object.assign(item, {
                      cellClassName: {
                        jiang: 'jiang_style'
                      }
                    })
                  }
                }
              }
              Object.assign(item, {
                _loading: false,
                children: [] // item.shiftCrew
              })
              return item
            })
          }, 5500)
        } else {
          this.$Message.error(res.data.Message)
        }
      })
      // this.showNotice()
      // this.getThinkMess()
      this.getTypeText()
    },
    handleLoadData (item, callback) {
      let _tempList = item.shiftCrew.filter(list => list.down_seafarer_id !== item.down_seafarer_id)
      setTimeout(() => {
        const data = _tempList
        callback(data)
      }, 1000)
    },
    getDictData () {
      this.setSearchData.vessel_id_in.selectData = []
      this.setSearchData.apply_duty_id_in.selectData = []
      this.setSearchData.crt_level_id_in.selectData = []
      API.querySeafarerBaseInfo().then(res => {
        if (res.data.Code === 10000) {
          this.setSearchData.vessel_id_in.selectData = res.data.vesselList.map(item => { // 重置船舶key值
            return {
              label: item.vessel_name,
              value: item.vessel_id
            }
          })
          this.setSearchData.apply_duty_id_in.selectData = res.data.crtDutyList.map(item => { // 重置职务key值
            return {
              label: item.apply_duty_name,
              value: item.apply_duty_id
            }
          })
          this.setSearchData.crt_level_id_in.selectData = res.data.crtLevelList.map(item => { // 重置职务key值
            return {
              label: item.crt_level_name,
              value: item.crt_level_id
            }
          })
        }
      })
    },
    getTypeText () {
      this.typeText = ''
      let index = 0
      let fullText = `<H3>正在整合船员基础信息…</H3><br/>`
      let randomText = ['结构化数据转换及执行数据并行任务中', '消除噪声及数据标准化处理中', '多个数据源深度分析与关联匹配中', '调用历史数据和实时数据进行交叉推理', '变量赋予不同的权重，确保决策依据最相关的数据和需求优先级', '数据通过标准化处理，清洗无关信息，填补缺失值', '对不同来源的多种数据进行关联分析', '分析过去相似任务的结果，优化当前决策路径', '自动调整各因素的权重，确保最关键的因素在决策过程中得到优先处理']
      let afterText = ['基于任务的多维度要求，对不同执行路径进行模拟和评估', '基于实时推理,调整推理模型中的决策路径,优化决策结果', '自动进行信息关联，生成符合任务要求的推理图谱', '实时推理模拟，系统对多个决策路径进行评估，选择最合适的路径', '推理验证不同策略的可行性与效果，自动优化决策路径，确保决策的可靠性', '分析任务中的每个影响因素，并通过智能算法为每个变量分配优先级', '基于实时数据反馈动态调整决策模型，确保每次计算和路径选择都符合当前的最优策略']
      let textIdx = Math.floor(Math.random() * randomText.length)
      let afterIdx = Math.floor(Math.random() * afterText.length)
      let firstText = randomText[textIdx] + '<br/>'
      let secText = afterText[afterIdx] + '<br/>' + '<Strong>数据整合完毕，渲染中……</Strong>'
      fullText += firstText + secText
      this.interval = setInterval(() => {
        this.typeText += fullText[index] // 逐个添加字符
        index++
        if (index === fullText.length) {
          clearInterval(this.interval) // 当文本全部显示时停止
        }
      }, 66) // 控制每个字符的显示速度
    },
    async getThinkMess () {
      const jsonData = {
        model: 'deepseek-r1:32b',
        options: {},
        messages: [
          {
            role: 'user',
            content: '帮我快速筛查已知船员上下船分配计划',
            images: []
          }
        ]
      }
      try {
        const response = await axios.post('http://203.83.237.110:11434/api/chat', jsonData)
        if (response) {
          // 将接口返回的字符串按行分割成多个JSON对象
          const responseArray = response.data.split('\n')
          let extractedText = ''
          
          // 将每个JSON对象解析并提取message.content，然后拼接成完整消息
          let combinedMessage = ''
          responseArray.forEach(item => {
            try {
              const parsedItem = item === '' ? {message: {content: ''}} : JSON.parse(item) // 解析每行JSON
              combinedMessage += parsedItem.message.content // 提取content并拼接
            } catch (error) {
              console.error('Error parsing JSON:', error)
            }
          })
          const regex = /<think>(.*?)<\/think>/s // 正则表达式提取<think>到</think>之间的内容
          const match = combinedMessage.match(regex)
          if (match) {
            extractedText = match[1] // 提取出的文本
          }
          // 更新拼接后的消息
          this.thinkText = ''
          let index = 0
          const interval = setInterval(() => {
            this.thinkText += extractedText[index] // 逐个添加字符
            index++
            if (index === extractedText.length) {
              clearInterval(interval) // 当文本全部显示时停止
            }
          }, 80) // 控制每个字符的显示速度
        }
      } catch (err) {
        console.error('Error', err)
      }
    },
    showNotice () {
      this.$Notice.config({
        top: 60
      })
      this.$Notice.open({
        name: 'loadNotice',
        title: 'AI兴仔解析中…',
        duration: 0,
        render: h => {
          const boxMessage = [
            '数据正在提取并进行RAG向量处理及规则引擎筛选中…',
          ]
          return h('span', boxMessage)
        }
      })
    },
    sendMessage () {
      if (this.newMessage.trim()) {
        // 发送消息到服务器
        this.socket.emit('chat message', this.newMessage)
        this.newMessage = ''
      }
    },
    tableClick () {

    },
    searchResults (e) {
      if (e.vessel_id_in) this.listQuery.vessel_id_in = e.vessel_id_in.join()
      if (e.apply_duty_id_in) this.listQuery.apply_duty_id_in = e.apply_duty_id_in.join()
      if (e.crt_level_id_in) this.listQuery.crt_level_id_in = e.crt_level_id_in.join()
      this.listQuery.on_board_days = e.on_board_days
      this.listQuery.down_board_days_st = e.down_board_days_st
      this.listQuery.down_board_days_ed = e.down_board_days_ed
      this.listQuery.certificate_expire_date = e.certificate_expire_date
      this.getList()
    },
    resetResults () {
      this.listQuery.vessel_id_in = ''
      this.listQuery.apply_duty_id_in = ''
      this.listQuery.crt_level_id_in = ''
      this.listQuery.on_board_days = ''
      this.listQuery.down_board_days_st = ''
      this.listQuery.down_board_days_ed = ''
      this.listQuery.certificate_expire_date = ''
      this.setSearchData.vessel_id_in.selected = []
      this.setSearchData.apply_duty_id_in.selected = []
      this.setSearchData.crt_level_id_in.selected = []
      this.setSearchData.on_board_days.value = ''
      this.setSearchData.down_board_days_st.value = ''
      this.setSearchData.down_board_days_ed.value = ''
      this.setSearchData.certificate_expire_date.value = ''
      this.getList()
    },
    handleCurrentChange () {

    },
    handleSizeChange () {

    }
  },
  created () {
    // 连接到服务器
    // this.socket = io('http://localhost:3000')

    // // 监听服务器发送的消息
    // this.socket.on('chat message', (msg) => {
    //   console.log(msg)
    // })
    this.getDictData() // 获取字典数据
    // this.getList() // 获取列表数据
  },
  beforeDestroy() {
    // 组件销毁前断开连接
    // if (this.socket) {
    //   this.socket.disconnect()
    // }
  }
})
</script>
<style>
.warning_style {
  background-color: #f60 !important;
  color: #fff;
}
.jiang_style {
  background-color: #187 !important;
  color: #fff;
}
</style>

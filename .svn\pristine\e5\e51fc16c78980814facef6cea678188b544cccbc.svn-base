<template>
  <div>
    <Card>
      <search @searchResults='searchResults' @selectOnChanged='selectOnChanged' :setSearch='setSearchData' @resetResults='resetResults'></search>
      <div class="extra" slot="extra">
        <formAction :setFormAction='setFormAction' @handleCreate="handleEdit('create')"></formAction>
      </div>
    </Card>
    <Card style="margin-top: 10px">
      <Table border :loading="loading" ref="selection" :columns="columns" :data="list"></Table>
      <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
        :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
    </Card>
    <editModal :modalData="modalData" @callback="getList"></editModal>
  </div>
</template>
<script>
import search from '_c/search' // 查询组件
import formAction from '_c/formAction' // 表单操作组件
import editModal from './editModal'
import API from '@/api/basicData'
import { queryOilPricePage, delOilPrice } from '@/api/materialPurchaseSystem/trilateralOilPrice'
export default {
  components: {
    search,
    formAction,
    editModal
  },
  data () {
    return {
      loading: false,
      total: 0,
      list: [],
      listCurrent: 1,
      listQuery: {
        oil_price_id: '',
        release_date: '',
        product_model_type: '',
        pageSize: 10,
        pageIndex: 1
      },
      setFormAction: {
        operation: ['create']
      },
      modalData: {
        modal: false,
        title: '',
        type: '',
        data: undefined
      },
      setSearchData: {
        release_date: {
          type: 'date',
          label: '时间',
          selected: '',
          width: 135,
          value: '',
          isdisable: false
        },
        product_model_type: {
          type: 'select',
          label: '规格型号',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 135,
          value: '',
          filterable: true
        }
      },
      columns: [
        {
          title: '时间',
          key: 'release_date',
          align: 'center'
        },
        {
          title: '规格型号',
          key: 'product_model',
          align: 'center'
        },
        {
          title: '区域',
          key: 'area_name',
          align: 'center'
        },
        {
          title: '最低价(元/吨)',
          key: 'bottom_price',
          align: 'center'
        },
        {
          title: '最高价(元/吨)',
          key: 'top_price',
          align: 'center'
        },
        {
          title: '平均价(元/吨)',
          key: 'middle_price',
          align: 'center'
        },
        {
          title: '涨跌幅(%)',
          key: 'rose_rate',
          align: 'center'
        },
        {
          title: '操作',
          align: 'center',
          width: 180,
          render: (h, params) => {
            return h('div', [
              h('Button', {
                style: {
                  margin: '0 5px'
                },
                props: {
                  icon: 'md-brush',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleEdit('update', params.row)
                  }
                }
              }, '编辑'),
              h('Button', {
                style: {
                  margin: '0 5px'
                },
                props: {
                  icon: 'md-trash',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleDelete(params.row.oil_price_id)
                  }
                }
              }, '删除')
            ])
          }
        }
      ]
    }
  },
  created () {
    API.materialInventoryname({ usefor: '油料' }).then(res => { // 获取名称
      if (res.data.Code === 10000) {
        res.data.Result.map(item => {
          this.setSearchData.product_model_type.selectData.push({
            value: item.inventoryname,
            label: item.inventoryname
          })
        })
      }
    })
    this.getList()
  },
  methods: {
    // 获取列表
    getList () {
      this.loading = true
      queryOilPricePage(this.listQuery).then(res => {
        if (res.data.Code === 10000) {
          this.loading = false
          this.list = res.data.Result
          this.total = res.data.Total
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 新增/编辑
    handleEdit (type, row) {
      if (type === 'create') {
        this.modalData = {
          modal: true,
          title: '新增',
          data: undefined,
          type: type
        }
      } else {
        this.modalData = {
          modal: true,
          type: type,
          title: '编辑',
          data: row
        }
      }
    },
    // 删除
    handleDelete (d) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确认删除所选项？</p>',
        loading: true,
        onOk: () => {
          delOilPrice({ oil_price_id: d }).then(res => {
            this.loading = false
            if (res.data.Code === 10000) {
              this.$Modal.remove()
              this.$Message.success(res.data.Message)
              this.getList()
            } else {
              this.$Modal.remove()
              this.$Message.error(res.data.Message)
            }
          })
        }
      })
    },
    // 查询
    searchResults (e) {
      this.listQuery.release_date = this.release_date
      this.listQuery.product_model_type = e.product_model_type
      this.listCurrent = 1
      this.listQuery.pageIndex = 1
      delete e.target
      this.getList()
    },
    selectOnChanged (e) {
      this.release_date = e.key
    },
    // 重置
    resetResults () {
      this.listCurrent = 1
      this.listQuery = {
        oil_price_id: '',
        release_date: '',
        product_model_type: '',
        pageSize: 10,
        pageIndex: 1
      }
      this.release_date = ''
      this.setSearchData.release_date.selected = ''
      this.setSearchData.product_model_type.selected = ''
      this.getList()
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    }
  }
}
</script>
<style lang="less" scoped>
  .ivu-form-item:last-child {
    margin-bottom: 0;
  }
</style>

<template>
  <Card>
    <Tabs @on-click="tabClick">
      <TabPane label="油品信息" name="oil">
        <div style="overflow: hidden;">
          <Button type="primary" @click="handleCreate('oil')">新增</Button>
          <search @searchResults="searchResults" :setSearch="setSearchData" @resetResults="resetResults" class="search-vie"></search>
        </div>
        <Table border :loading="loading" :columns="oilColumns" :data="oilList" maxHeight="500" :draggable="true" @on-drag-drop="onDragDrop"></Table>
      </TabPane>
      <TabPane label="油舱信息" name="oilTank">
        <Button type="primary" @click="handleCreate('oilTank')" style="margin-bottom: 5px;">新增</Button>
        <search @searchResults="searchResults" :setSearch="setSearchData" @resetResults="resetResults" class="search-vie"></search>
        <Row v-if="tabType === 'oilTank'" class="all_num">
          <Col span="12">
            <label>最大舱容总量统计(m³)</label>
            <span>36613.218</span>
          </Col>
          <Col span="12">
            <label>安全舱容总量统计(m³)</label>
            <span>13949.213</span>
          </Col>
        </Row>
        <Table border :loading="loading1" :columns="oilTankColumns" :data="oilTankList" maxHeight="500"></Table>
        <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
        :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
      </TabPane>
    </Tabs>
    <!-- 油品弹窗内容 -->
    <Modal v-model="oilModal" :title="oilTitle" @on-ok="handleSubmit('formInline')" @on-cancel="handleCancel" width="40%" :mask-closable="false">
      <Form ref="formInline" :model="formInline" :rules="ruleInline" inline :label-width="140">
        <div class="form-class-div">
          <Row>
            <Col span="12">
              <Form-item prop="oil_type" label="油品类型">
                <Select v-model="formInline.oil_type" placeholder="请选择">
                  <Option v-for="(item, idx) in oilTypeList" value="item.id">{{item.type_name}}</Option>
                </Select>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item prop="oil_name" label="油品名称（中文）">
                <Input type="text" v-model="formInline.oil_name"></Input>
              </Form-item>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <Form-item label="油品名称（英文）">
                <Input type="text" v-model="formInline.oil_name_en"></Input>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item prop="oil_brand" label="牌号">
                <Input type="text" v-model="formInline.oil_brand"></Input>
              </Form-item>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <Form-item label="单位">
                <div v-model="formInline.unit" v-if="formInline.unit === ''">--</div>
                <div v-model="formInline.unit" v-else>{{formInline.unit}}</div>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item prop="precise_digits" label="精准位数">
                <Select v-model="formInline.precise_digits" placeholder="请选择">
                  <Option value="0">0</Option>
                  <Option value="1">1</Option>
                  <Option value="2">2</Option>
                  <Option value="3">3</Option>
                  <Option value="4">4</Option>
                  <Option value="5">5</Option>
                </Select>
              </Form-item>
            </Col>
          </Row>
          <Row>
            <Form-item label="记录频率">
              <Select v-model="formInline.record_rate" multiple placeholder="请选择">
                <Option v-for="(item, idx) in recordRateList" value="item.id">{{item.record_rate}}</Option>
              </Select>
            </Form-item>
          </Row>
        </div>
      </Form>
    </Modal>
    <!-- 油舱弹窗 -->
    <Modal v-model="oilTankModal" :title="oilTankTitle" @on-ok="handleSubmit('formValidate')" width="40%"  @on-cancel="handleCancelTank" :mask-closable="false">
      <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" inline :label-width="140">
        <div class="form-class-div">
          <Row>
            <Col span="12">
              <Form-item prop="ship_name" label="船舶名称">
                <Select v-model="formValidate.ship_name" placeholder="请选择">
                  <Option v-for="(item, idx) in shipList" value="item.id">{{item.ship_name}}</Option>
                </Select>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item prop="oil_tank_name" label="油舱名称">
                <Input type="text" v-model="formValidate.oil_tank_name"></Input>
              </Form-item>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <Form-item prop="oil_tank_type" label="油舱类型">
                <Select v-model="formValidate.oil_tank_type" placeholder="请选择">
                  <Option v-for="(item, idx) in oilTankTypeList" value="item.id">{{item.oil_tank_type}}</Option>
                </Select>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item label="总深（m）">
                <Input-number v-model="formInline.total_depth" placeholder="请输入" style="width:100%"></Input-number>
              </Form-item>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <Form-item label="最大舱容（m³）">
                <Input-number v-model="formInline.max_stock" placeholder="请输入" style="width:100%"></Input-number>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item label="安全舱容（m³）">
                <Input-number v-model="formInline.max_stock" placeholder="请输入" style="width:100%"></Input-number>
              </Form-item>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <Form-item prop="oil_brand" label="储存油品及牌号">
                <Select v-model="formValidate.oil_brand" placeholder="请选择">
                  <Option v-for="(item, idx) in oilBrandList" value="item.id">{{item.oil_brand}}</Option>
                </Select>
              </Form-item>
            </Col>
          </Row>
        </div>
      </Form>
    </Modal>
  </Card>
</template>
<script>
import search from '_c/search'
export default {
  components: {
    search
  },
  data () {
    return {
      tabType: 'oil',
      // 油品信息
      setSearchData: {
        member_name: {
          type: 'text',
          label: '共条结果',
          width: 180,
          value: '',
          isdisable: false,
          placeholder: '请输入油品名称/牌号'
        }
      },
      loading: false,
      oilColumns: [
        {
          type: 'index',
          title: '序号',
          width: 70,
          align: 'center'
        },
        {
          title: '油品类型',
          key: 'type_name',
          align: 'center',
          filters: [
            {
              label: '燃油',
              value: '燃油'
            },
            {
              label: '润滑油',
              value: '润滑油'
            }
          ],
          filterMethod (value, row) {
            return row.type_name.indexOf(value) > -1
          }
        },
        {
          title: '油品名称（中文）',
          key: 'oil_name',
          align: 'center'
        },
        {
          title: '油品名称（英文）',
          key: '',
          align: 'center'
        },
        {
          title: '牌号',
          key: '',
          align: 'center'
        },
        {
          title: '单位',
          key: '',
          align: 'center'
        },
        {
          title: '精准位数',
          key: 'date',
          align: 'center'
        },
        {
          title: '记录频率',
          key: 'level',
          align: 'center'
        },
        {
          title: '拖拽排序',
          key: '',
          width: 100,
          align: 'center',
          render: (h, params) => {
            return h('Button', {
              props: {
                icon: 'ios-paper',
                size: 'small'
              },
              on: {
                change: () => {
                  this.getOilList()
                }
              }
            })
          }
        },
        {
          title: '操作',
          key: '',
          width: 130,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                style: {
                  margin: '0 5px'
                },
                props: {
                  icon: 'md-brush',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleEdit(params.row, 'oil')
                  }
                }
              }),
              h('Button', {
                style: {
                  margin: '0 5px'
                },
                props: {
                  icon: 'md-trash',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleDelete(params.row.id, 'oil')
                  }
                }
              })
            ])
          }
        }
      ],
      oilList: [{
        oil_name: '兴通789',
        type_name: '润滑油'
      }],
      oilModal: false,
      oilTitle: '',
      oilTypeList: [], // 油品类型下拉
      recordRateList: [], // 记录频率下拉
      formInline: {
        unit: ''
      },
      ruleInline: {
        oil_type: [{ required: true, message: '此处不能为空!', trigger: 'change' }],
        oil_name: [{ required: true, message: '此处不能为空!', trigger: 'blur' }],
        oil_brand: [{ required: true, message: '此处不能为空!', trigger: 'blur' }],
        precise_digits: [{ required: true, message: '此处不能为空!', trigger: 'change' }]
      },
      // 油舱信息
      shipList: [],
      total: 0,
      listQuery: {
        sort_id: '',
        pageSize: 10,
        pageIndex: 1
      },
      listCurrent: 1,
      oilTankTypeList: [],
      oilBrandList: [],
      oilTankModal: false,
      oilTankTitle: '',
      loading1: false,
      formValidate: {},
      ruleValidate: {
        ship_name: [{ required: true, message: '此处不能为空!', trigger: 'change' }],
        oil_tank_name: [{ required: true, message: '此处不能为空!', trigger: 'blur' }],
        oil_tank_type: [{ required: true, message: '此处不能为空!', trigger: 'change' }],
        oil_brand: [{ required: true, message: '此处不能为空!', trigger: 'change' }]
      },
      oilTankColumns: [
        {
          type: 'index',
          title: '序号',
          width: 70,
          align: 'center'
        },
        {
          title: '船舶名称',
          key: 'oil_name',
          align: 'center',
          filters: [
            {
              label: '兴通16',
              value: '兴通16'
            },
            {
              label: '兴通789',
              value: '兴通789'
            }
          ],
          filterMethod (value, row) {
            return row.level.indexOf(value) > -1
          }
        },
        {
          title: '油舱名称',
          key: '',
          align: 'center'
        },
        {
          title: '油舱类型',
          key: '',
          align: 'center',
          filters: [
            {
              label: '存油',
              value: '存油'
            },
            {
              label: '日常使用',
              value: '日常使用'
            }
          ],
          filterMethod (value, row) {
            return row.level.indexOf(value) > -1
          }
        },
        {
          title: '最大舱容（m³）',
          key: '',
          align: 'center'
        },
        {
          title: '安全舱容（m³）',
          key: '',
          align: 'center'
        },
        {
          title: '总深（m）',
          key: 'date',
          align: 'center'
        },
        {
          title: '储存油品及牌号',
          key: 'level',
          align: 'center'
        },
        {
          title: '操作',
          key: '',
          width: 130,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                style: {
                  margin: '0 5px'
                },
                props: {
                  icon: 'md-brush',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleEdit(params.row, 'oilTank')
                  }
                }
              }),
              h('Button', {
                style: {
                  margin: '0 5px'
                },
                props: {
                  icon: 'md-trash',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleDelete(params.row.id, 'oilTank')
                  }
                }
              })
            ])
          }
        }
      ],
      oilTankList: [{
        oil_name: '兴通16'
      }]
    }
  },
  created () {
    this.getOilList()
  },
  methods: {
    // tab切换
    tabClick (name) {
      this.tabType = name
      if (name === 'oil') {
        this.getOilList()
      } else {
        this.getOilTankList()
      }
    },
    // 获取油品列表
    getOilList () {
      // this.loading = true
      // API().then(res => {
      //   if (res.data.Code === 10000) {
      //     this.loading = false
      //     this.oilList = res.data.Result
      //   } else {
      //     this.$Message.error(res.data.Message)
      //   }
      // })
    },
    // 获取油舱列表
    getOilTankList () {
      // this.loading = true
      // API().then(res => {
      //   if (res.data.Code === 10000) {
      //     this.loading = false
      //     this.oilList = res.data.Result
      //   } else {
      //     this.$Message.error(res.data.Message)
      //   }
      // })
    },
    // 新增
    handleCreate (type) {
      if (type === 'oil') {
        this.oilTitle = '新增油品信息'
        this.oilModal = true
      } else {
        this.oilTankTitle = '新增油舱信息'
        this.oilTankModal = true
      }
    },
    // 编辑
    handleEdit (row, type) {
      if (type === 'oil') {
        this.oilModal = true
        this.oilTitle = '编辑油品信息'
        this.formInline = row
        // this.formInline.unit = '吨'
      } else {
        this.oilTankModal = true
        this.oilTankTitle = '编辑油舱信息'
      }
    },
    // 保存
    handleSubmit (name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.$Modal.confirm({
            title: '提示',
            content: '<p>确认保存油品信息？</p>',
            loading: true,
            onOk: () => {
              API(this.formInline).then(res => {
                if (res.data.Code === 10000) {
                  this.loading = false
                  this.$Modal.remove()
                  this.$Message.success(res.data.Message)
                  this.$refs['name'].resetFields()
                  this.getOilList(this.equipTree[0].id)
                } else {
                  this.loading = false
                  this.$Modal.remove()
                  this.$Message.error(res.data.Message)
                }
              })
            }
          })
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 关闭弹窗
    handleCancel () {
      this.$refs['formInline'].resetFields()
      this.formInline.oil_type = ''
      this.formInline.precise_digits = ''
      this.formInline.record_rate = ''
    },
    // 关闭油舱弹窗
    handleCancelTank () {
      this.$refs['formValidate'].resetFields()
      this.formValidate.ship_name = ''
      this.formValidate.oil_tank_type = ''
      this.formValidate.oil_brand = ''
    },
    // 删除列表
    handleDelete (d, type) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>删除后无法恢复，是否确认删除？</p>',
        loading: true,
        onOk: () => {
          if (type === 'oil') {
            // API.({ id: d }).then(res => {
            //   if (res.data.Code === 10000) {
            //     this.loading = false
            //     this.$Modal.remove()
            //     this.$Message.success(res.data.Message)
            //     this.getOilList()
            //   } else {
            //     this.loading = false
            //     this.$Modal.remove()
            //     this.$Message.error(res.data.Message)
            //   }
            // })
          } else {
            // API.({ id: d }).then(res => {
            //   if (res.data.Code === 10000) {
            //     this.loading = false
            //     this.$Modal.remove()
            //     this.$Message.success(res.data.Message)
            //     this.getOilTankList()
            //   } else {
            //     this.loading = false
            //     this.$Modal.remove()
            //     this.$Message.error(res.data.Message)
            //   }
            // })
          }
        }
      })
    },
    // 查询
    searchResults (e) {
      this.listCurrent = 1
      this.listQuery.pageIndex = 1
      this.getOilList()
    },
    // 重置
    resetResults () {
      this.listCurrent = 1
      this.listQuery = {
        pageSize: 10,
        pageIndex: 1
      }
      this.getOilList()
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getOilList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getOilList()
    },
    // 拖拽排序
    onDragDrop(first, end) {
      //转成int型，方便后续使用
      first = parseInt(first)
      end = parseInt(end)
      let tmp = this.classiList[first]
      if(first < end) {
        for(var i=first+1; i<=end; i++) {
          this.classiList.splice(i-1, 1, this.classiList[i])
        }
        this.classiList.splice(end, 1, tmp)
      }
      if(first > end) {
        for(var i=first; i>end; i--) {
          this.classiList.splice(i, 1, this.classiList[i-1])
        }
        this.classiList.splice(end, 1, tmp)
      }
      // let idx = this.classiList.findIndex(e => {return e.id === ''})
      // if (idx > -1) {
      //   this.classiList.splice(this.classiList[idx], 1)
      // }
      if (this.classiList[0].id === '') {
        this.classiList.splice(this.classiList[0], 1)
      }
      this.getClassiList()
    }
  }
}
</script>
<style lang="less" scoped>
.col-text {
  line-height: 40px;
  border-bottom: 1px solid #ccc;
  font-weight: 700;
  font-size: 16px;
  color: #000;
  text-align: center;
}
.search-vie {
  float: right;
  margin-top: -5px;
  margin-left: 10px;
}
.tr-name {
  font-size: 16px;
  color: #333;
  font-weight: 700;
  margin: 15px 0;
  &&::before {
    content: "";
    font-size: 16px;
    padding-right: 10px;
    border-left: 4px solid #1d6ced;
  }
}
.form-class-div {
  border-top: 1px solid #ccc;
  border-left: 1px solid #ccc;
}
.all_num {
  margin: 5px 0 10px;
  border: 1px solid #ccc;
  && > div:nth-child(even) label {
    border-left: 1px solid #ccc;
  }
  label {
    width: 150px;
    text-align: right;
  }
  span {
    display: inline-block;
    padding-left: 10px;
  }
}
</style>
<style>
.ivu-form .form-class-div .ivu-col.ivu-col-span-12, .ivu-form .form-class-div > .ivu-row > .ivu-form-item {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-bottom: 1px solid #ccc;
  border-right: 1px solid #ccc;
}
.ivu-form .form-class-div .ivu-form-item-label, .all_num label {
  background-color: #eee;
  padding: 0 8px;
  font-size: 12px;
  font-weight: 700;
  color: #333;
  display: inline-block;
  line-height: 40px;
  border-right: 1px solid #ccc;
}
.form-class-div .ivu-form-item {
  margin: 0;
  line-height: 40px;
  width: 100%;
}
.ivu-form .form-class-div .ivu-form-item-content {
  padding: 0 10px;
  margin-top: 3px;
}
</style>

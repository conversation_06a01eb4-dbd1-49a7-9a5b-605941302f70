<template>
  <Drawer :title="modalData.title" v-model="modalData.modal" :data="modalData.data" @on-visible-change="modalShowHide" width="600">
    <div class="title_area">
      <span class="list_span list_title" style="border-bottom: none; border-right: none;">项目</span>
      <span class="list_span list_title" style="border-bottom: none; border-right: none;">合同/预算标准(月)</span>
      <span class="list_span list_title" style="border-bottom: none;">当月计提数</span>
    </div>
    <h4 class="title_font">合同约定项目</h4>
    <div class="list_area" v-for="(item, idx) in contractList">
      <span class="list_span" style="border-bottom: none; border-right: none;">{{ item.material_name }}</span>
      <span class="list_span" style="border-bottom: none; border-right: none;">{{ item.xtgf_cgapplyqty ? `$${item.xtgf_cgapplyqty}` : '' }}</span>
      <span class="list_span" style="border-bottom: none;">${{ item.applyqty }}</span>
    </div>
    <div>
      <span class="list_span" style="border-bottom: none; border-right: none; font-weight: bold;">单项合计</span>
      <span class="list_span" style="border-bottom: none; border-right: none;">
        {{ contract_cgapplyqty? `$${contract_cgapplyqty.toFixed(2)}` : 0 }}
      </span>
      <span class="list_span" style="border-bottom: none;">
        {{ contract_apply? `$${contract_apply.toFixed(2)}` : 0 }}
      </span>
    </div>
    <h4 class="title_font">接船费用</h4>
    <div class="list_area" v-for="(item, idx) in shipList">
      <span class="list_span" style="border-bottom: none; border-right: none;">{{ item.material_name }}</span>
      <span class="list_span" style="border-bottom: none; border-right: none;">{{ item.xtgf_cgapplyqty ? `$${item.xtgf_cgapplyqty}` : '' }}</span>
      <span class="list_span" style="border-bottom: none; ">{{ item.applyqty }}</span>
    </div>
    <div>
      <span class="list_span" style="border-bottom: none; border-right: none; font-weight: bold;">单项合计</span>
      <span class="list_span" style="border-bottom: none; border-right: none;">{{ ship_cgapplyqty? `$${ship_cgapplyqty.toFixed(2)}` : 0 }}</span>
      <span class="list_span" style="border-bottom: none;">
        {{ ship_apply? `$${ship_apply.toFixed(2)}` : 0 }}
      </span>
    </div>
    <h4 class="title_font" style="border-bottom: 1px solid #ccc;">预算外费用</h4>
    <div class="list_area" v-for="(item, idx) in otherList">
      <span class="list_span" style="border-top: none; border-right: none;">{{ item.material_name }}</span>
      <span class="list_span" style="border-top: none; border-right: none;">{{ item.xtgf_cgapplyqty ? `$${item.xtgf_cgapplyqty}` : '' }}</span>
      <span class="list_span" style="border-top: none; ">${{ item.applyqty }}</span>
    </div>
    <div>
      <span class="list_span" style="border-top: none;border-right: none; font-weight: bold;">单项合计</span>
      <span class="list_span" style="border-top: none;border-right: none;">
        {{ other_cgapplyqty? `$${other_cgapplyqty.toFixed(2)}` : 0 }}
      </span>
      <span class="list_span" style="border-top: none;">
        {{ other_apply? `$${other_apply.toFixed(2)}` : 0 }}
      </span>
    </div>
    <div class="list_area">
      <span class="list_span" style="border-top: none; border-right: none; font-weight: bold;">合计</span>
      <span class="list_span" style="border-top: none; border-right: none; font-weight: bold;">{{ total_cgapplyqty ? `$${total_cgapplyqty.toFixed(2)}` : 0 }}</span>
      <span class="list_span" style="border-top: none; font-weight: bold;">{{ total_apply ? `$${total_apply.toFixed(2)}` : 0 }}</span>
    </div>
  </Drawer>
</template>
<script>

export default ({
  props: {
    modalData: Object
  },
  data () {
    return {
      total_cgapplyqty: 0, // 预算合计
      total_apply: 0, // 实际合计
      contract_cgapplyqty: 0, // 合同合计
      contract_apply: 0, // 合同合计
      ship_cgapplyqty: 0, // 接船合计
      ship_apply: 0, // 接船合计
      other_cgapplyqty: 0, // 预算外合计
      other_apply: 0, // 预算外合计
      contractList: [], // 合同约定列表
      shipList: [], // 接船列表
      otherList: [] // 预算外费用
    }
  },
  methods: {
    totalPay (str) {
      if (!str || str === '') return ''
      const totalPay = parseFloat(str) * parseFloat(this.modalData.data.xtgf_datanum)
      return totalPay.toFixed(2)
    },
    modalShowHide (val) {
      if (this.modalData.data.billentry.length > 0) {
        this.total_apply = 0
        this.total_cgapplyqty = 0
        this.contract_cgapplyqty = 0
        this.contract_apply = 0
        this.ship_cgapplyqty = 0
        this.ship_apply = 0
        this.other_cgapplyqty = 0
        this.other_apply = 0
        this.contractList = []
        this.shipList = []
        this.otherList = []
        this.modalData.data.billentry.map(item => {
          const cgapplyqty_value = typeof item.xtgf_cgapplyqty !== '' && !isNaN(item.xtgf_cgapplyqty) ? parseFloat(item.xtgf_cgapplyqty) : 0
          const apply_value = typeof item.applyqty !== '' && !isNaN(item.applyqty) ? parseFloat(item.applyqty) : 0
          this.total_apply += apply_value
          this.total_cgapplyqty += cgapplyqty_value
          if (item.material_masterid_modelnum === '合同约定项目') {
            this.contractList.push(item)
            this.contract_cgapplyqty += typeof item.xtgf_cgapplyqty !== '' && !isNaN(item.xtgf_cgapplyqty) ? parseFloat(item.xtgf_cgapplyqty) : 0
            this.contract_apply += typeof item.applyqty!== '' &&!isNaN(item.applyqty)? parseFloat(item.applyqty) : 0
          }
          if (item.material_masterid_modelnum === '接船费用') {
            this.shipList.push(item)
            this.ship_cgapplyqty += typeof item.xtgf_cgapplyqty!== '' &&!isNaN(item.xtgf_cgapplyqty)? parseFloat(item.xtgf_cgapplyqty) : 0
            this.ship_apply += typeof item.applyqty!== '' &&!isNaN(item.applyqty)? parseFloat(item.applyqty) : 0
          }
          if (item.material_masterid_modelnum === '预算外费用') {
            this.otherList.push(item)
            this.other_cgapplyqty += typeof item.xtgf_cgapplyqty!== '' &&!isNaN(item.xtgf_cgapplyqty)? parseFloat(item.xtgf_cgapplyqty) : 0
            this.other_apply += typeof item.applyqty!== '' &&!isNaN(item.applyqty)? parseFloat(item.applyqty) : 0
          }
        })
      }
    }
  }
})
</script>
<style scoped>
  .title_area {
    position: sticky;
    top: -16px;
  }
  .title_font {
    width: 540px;
    padding: 5px;
    border: 1px solid #ccc;
    border-bottom: none;
  }
  .title_font::before {
    content: "";
    margin-right: 5px;
    border-left: 5px solid #195bdd;
  }
  .list_title {
    background: #195bdd;
    color: #fff;
    font-size: 14px;
    font-weight: bold;
  }
  .list_span {
    display: inline-block;
    width: 180px;
    border: 1px solid #ccc;
    padding: 5px;
  }
  .list_span:empty::after {
    content: '\00a0';
    display: inline-block;
  }
</style>

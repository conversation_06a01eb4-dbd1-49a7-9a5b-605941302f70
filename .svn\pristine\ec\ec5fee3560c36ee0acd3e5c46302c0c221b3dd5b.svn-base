<template>
  <div>
    <Upload
      multiple
      action=""
      :show-upload-list="false"
      :before-upload="handleFileUpload"
      style="margin-bottom:5px;">
      <Button icon="ios-cloud-upload-outline">上传文件</Button>
    </Upload>
    <ul class="file-list">
      <li v-for="(item, index) in fileDataList" :key="index">
        <a :href="item.download_url" target="_blank">{{ item.name }}</a>
        <Icon size="16" type="ios-trash-outline" @click="handleDelete(item.theme_attachment_id, index)" />
        <Icon type="ios-eye-outline" size="16" @click="handleEye(item.download_url)" />
      </li>
    </ul>
  </div>
</template>

<script>
import { fileUpload, deleteAttachment } from '@/api/examSystem/trainingModule/trainingTopics'
export default {
  props: {
    themeId: String,
    fileDataList: Array
  },
  data () {
    return {
      formDataList: []
    }
  },
  methods: {
    // 删除附件
    handleDelete (d, idx) {
      deleteAttachment({ theme_attachment_id: d }).then(e => {
        if (e.data.Code === 10000) {
          this.$Message.success(e.data.Message)
        }
      })
      this.fileDataList.splice(idx, 1)
      this.formDataList.splice(idx, 1)
    },
    // 预览
    handleEye (url) {
      window.open(url, '_blank')
    },
    handleFileUpload (file) {
      this.fileDataList.push({
        name: file.name,
        download_url: URL.createObjectURL(file)
      })
      this.formDataList.push(file)
      let formData = new FormData()
      let through = false // 判断是否需要上传文件
      this.formDataList = this.formDataList.filter(item => {
        if (item.type !== undefined) {
          through = true
          formData.append('theme_id', this.themeId)
          formData.append('file', item)
        }
        return item.type === undefined
      })
      if (through) {
        fileUpload(formData).then(e => {
          if (e.data.Code === 10000) {
            this.fileDataList[this.fileDataList.length - 1].theme_attachment_id = e.data.theme_attachment_id
            // this.formDataList = e.data.download_url
            this.$Message.success(e.data.Message)
          }
        })
      }
      return false
    }
  }
}
</script>

<style lang="less" scoped>
.file-list{
  li{
    list-style-type: none;
    padding: 5px 28px 5px 5px;
    position: relative;
    span{
      color: #2D8cF0;
      cursor: pointer;
    }
    i{
      margin-right: 4px;
    }
    .ivu-icon-ios-close{
      cursor: pointer;
      display: none;
      font-size: 20px;
      position: absolute;
      right: 5px;
      top: 4px;
      margin: 0;
    }
    &:hover{
      background: #f0f0f0;
      .ivu-icon-ios-close{
        display: inline-block;
      }
    }
  }
}
.imgShowClose{
  font-size: 30px;
  position: absolute;
  right: -15px;
  top: -15px;
  cursor: pointer;
}
</style>

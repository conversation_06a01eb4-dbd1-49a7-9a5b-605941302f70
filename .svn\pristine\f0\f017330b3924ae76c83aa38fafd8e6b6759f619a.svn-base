.side-menu-wrapper{
  user-select: none;
  .menu-collapsed{
    padding-top: 10px;

    .ivu-dropdown{
      width: 100%;
      .ivu-dropdown-rel a{
        width: 100%;
      }
    }
    .ivu-tooltip{
      width: 100%;
      .ivu-tooltip-rel{
        width: 100%;
      }
      .ivu-tooltip-popper .ivu-tooltip-content{
        .ivu-tooltip-arrow{
          border-right-color: #fff;
        }
        .ivu-tooltip-inner{
          background: #fff;
          color: #495060;
        }
      }
    }
  }
  a.drop-menu-a{
    display: inline-block;
    padding: 6px 15px;
    width: 100%;
    text-align: center;
    color: #495060;
  }
  .ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu .ivu-menu-item-active,
  .ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu .ivu-menu-item-active:hover {
    background: rgba(255,255,255,0.3) !important;
  }
 .ivu-menu-dark.ivu-menu-vertical.erpSys-active .ivu-menu-submenu .ivu-menu-item-active {
    // background: #1d6ced!important;
    background: none;
  }
  .ivu-menu-dark.ivu-menu-vertical.erpSys-active .ivu-menu-submenu .ivu-menu-item-active::before {
    display: none;
  }
  .ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu .ivu-menu-item-active::before {
    content: '';
    display: block;
    width: 5px;
    height: 100%;
    left: 0;
    top: 0;
    position: absolute;
    background-color: #fff;
    border-radius:0px 100px 100px 0px;
  }
}
.ivu-menu-dark.ivu-menu-vertical .ivu-menu-item-active:not(.ivu-menu-submenu), .ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu-title-active:not(.ivu-menu-submenu) {
  background: rgba(255,255,255,0.3) !important;
  color: #fff !important;
  &::before {
    content: '';
    display: block;
    width: 5px;
    height: 100%;
    left: 0;
    top: 0;
    position: absolute;
    background-color: #fff;
    border-radius:0px 100px 100px 0px;
  }
}
.menu-title{
  padding-left: 6px;
}

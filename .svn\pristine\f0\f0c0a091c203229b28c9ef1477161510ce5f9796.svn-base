<template>
  <div ref="dom" class="charts chart-pie"></div>
</template>

<script>
import echarts from 'echarts'
import tdTheme from './theme.json'
import { on, off } from '@/libs/tools'
echarts.registerTheme('tdTheme', tdTheme)
export default {
  name: 'Chart<PERSON>ie',
  props: {
    value: Array,
    text: String,
    subtext: String,
    legendShow: {
      type: Boolean,
      default: true
    },
    radius: {
      type: Number,
      default: 60
    },
    center: {
      type: Array,
      default: () => {
        return ['50%', '60%']
      }
    },
    color: {
      type: Array,
      default: () => {
        return ['#ff7f7f', '#ff7fbf', '#ff7fff', '#bf7fff', '#7f7fff', '#7fbfff', '#7fffff', '#7fffbf', '#7fff7f', '#bfff7f', '#DFDFDF']
      }
    },
    legend: {
      type: Boolean,
      default: true
    },
    legendPosition: {
      type: String,
      default: 'left'
    },
    legendType: {
      type: String,
      default: 'plain'
    }
  },
  data () {
    return {
      dom: null
    }
  },
  methods: {
    chartInit () {
      this.$nextTick(() => {
        let legend = this.value.map(_ => _.name)
        // let legend = this.value.map((item) => {
        //   let _textStyle = {
        //     width: 10,
        //     color: 'red',
        //     borderColor: '#000',
        //     borderWidth: 1,
        //     fontWeight: 'bold',
        //     overflow: 'break'
        //   }
        //   Object.assign(item, {
        //     textStyle: _textStyle
        //   })
        //   delete item.value
        //   return item
        // })
        let option = {
          title: {
            text: this.text,
            subtext: this.subtext,
            x: 'center'
          },
          tooltip: {
            show: this.legendShow,
            trigger: 'item',
            formatter: this.legend ? '{b} : {c}元 ({d}%)' : '{b}{d}%'
          },
          legend: {
            show: this.legend, // this.legendShow,
            orient: 'vertical',
            left: this.legendPosition, // 'left'
            data: legend,
            type: this.legendType
          },
          color: this.color,
          series: [
            {
              type: 'pie',
              radius: this.radius + '%',
              center: this.center, // ['50%', '60%'],
              data: this.value,
              itemStyle: {
                emphasis: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              },
              label: {
                align: 'left',
                normal: {
                  formatter (v) {
                    let text = v.name
                    if (text.length > 8 && text.length <= 16) {
                      text = `${text.slice(0, 8)}\n${text.slice(8)}`
                    } else if (text.length > 16 && text.length <= 24) {
                      text = `${text.slice(0, 8)}\n${text.slice(8, 16)}\n${text.slice(16)}`
                    } else if (text.length > 24 && text.length <= 30) {
                      text = `${text.slice(0, 8)}\n${text.slice(8, 16)}\n${text.slice(16, 24)}\n${text.slice(24)}`
                    } else if (text.length > 30) {
                      text = `${text.slice(0, 8)}\n${text.slice(8, 16)}\n${text.slice(16, 24)}\n${text.slice(24, 30)}\n${text.slice(30)}`
                    }
                    return text
                  }
                }
              }
            }
          ]
        }
        this.dom = echarts.init(this.$refs.dom, 'tdTheme')
        this.dom.setOption(option)
        on(window, 'resize', this.resize)
      })
    },
    resize () {
      this.dom.resize()
    }
  },
  mounted () {
    this.chartInit()
  },
  beforeDestroy () {
    off(window, 'resize', this.resize)
  },
  watch: {
    value: {
      handler (n, o) {
        this.chartInit()
      },
      deep: true,
      immediate: true
    }
  }
}
</script>

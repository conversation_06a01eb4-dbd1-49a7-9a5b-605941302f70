<template>
  <Drawer :title="modalData.title" :mask-closable="false" v-model="modalData.modal" :data="modalData.data" @on-visible-change="modalShow" width="950">
    <Form ref="formDynamic" :model="formDynamic" :label-width="80">
      <div v-for="(item, index) in formDynamic.attributArray" :key="index">
        <Row>
          <Col span="6">
            <FormItem label="题目分类" :prop="'attributArray.' + index + '.question_type_id'" :rules="{required: true, message: '此处不能为空', trigger: 'change'}">
              <Select v-model="item.question_type_id" filterable @on-change="selectedType(item.question_type_id, index)" @on-open-change="changeTypeOption(index)">
                <Option v-for="(item1, idx) in classifyList" :key="idx" :value="item1.question_type_id" v-show="!selectedTypeList.includes(item1.question_type_id)">{{ item1.type_name }}</Option>
              </Select>
            </FormItem>
          </Col>
          <Col span="6">
            <FormItem label="题目章节" :prop="'attributArray.' + index + '.question_section_id'" :rules="{required: true, message: '此处不能为空', trigger: 'change'}">
              <Select v-model="item.question_section_id" filterable :disabled="item.question_type_id === ''">
                <Option v-for="(item1, idx) in chapterList[index]" :key="idx" :value="item1.question_section_id">{{ item1.section_name }}</Option>
              </Select>
            </FormItem>
          </Col>
          <Col span="10">
            <FormItem label="题目归属" :prop="'attributArray.' + index + '.post_arr'" :rules="{required: true, message: '此处不能为空', trigger: 'change', type: 'array'}">
              <Select v-model="item.post_arr" multiple filterable>
                <Option v-for="(item1, idx) in ascriptionList" :key="idx" :value="item1.question_post_id">{{ item1.post_name }}</Option>
              </Select>
            </FormItem>
          </Col>
          <Col span="1">
            <Button v-if="index === (formDynamic.attributArray.length - 1)" type="text" @click="handleAdd('classify')" icon="md-add-circle" size="large"></Button>
          </Col>
          <Col span="1">
            <Button v-if="showAttributRemove" type="text" @click="handleRemove(index, 'classify')" icon="md-remove-circle" size="large"></Button>
          </Col>
        </Row>
      </div>
      <Row>
        <Col>
          <FormItem label="题目" prop="question_topic" :rules="{required: true, message: '此处不能为空', trigger: 'blur'}">
            <Input type='textarea' v-model='formDynamic.question_topic'></Input>
          </FormItem>
        </Col>
      </Row>
      <Row>
        <label class="label_div">选项</label>
        <div v-for="(itm, indexs) in formDynamic.optionArray" :key="indexs">
          <Col span="13">
            <FormItem :label="idxArr[indexs]" :prop="'optionArray.' + indexs + '.option_content'" :rules="{required: true, message: '此处不能为空', trigger: 'blur'}">
              <Input v-model='itm.option_content'></Input>
            </FormItem>
          </Col>
          <Col span="1">
            <Button v-if="indexs === (formDynamic.optionArray.length - 1) && formDynamic.optionArray.length < 6" type="text" @click="handleAdd('option')" icon="md-add-circle" size="large"></Button>
          </Col>
          <Col span="1">
            <Button v-if="showOptionRemove" type="text" @click="handleRemove(indexs, 'option')" icon="md-remove-circle" size="large"></Button>
          </Col>
        </div>
      </Row>
      <Row>
        <Col span="4">
          <FormItem label="答案" prop="question_answer" :rules="{required: true, type: 'array', message: '此处不能为空', trigger: 'blur'}">
            <Select v-model="formDynamic.question_answer" multiple filterable style="width: 200px;">
              <Option v-for="(item1, idx) in answerList" :key="idx" :value="item1">{{ item1 }}</Option>
            </Select>
          </FormItem>
        </Col>
      </Row>
    </Form>
    <div class="demo-drawer-footer">
      <Button style="margin-right: 8px" @click="modalData.modal = false">取消</Button>
      <Button type="primary" v-if="modalData.type === 'create'" @click="createData">保存</Button>
      <Button type="primary" v-else @click="updateData">保存</Button>
    </div>
  </Drawer>
</template>
<script>
import API from '@/api/examSystem/examModule/questionBankList'
import { queryQuestionSectionList } from '@/api/examSystem/examModule/questionBankClassify'
import { queryExamPostList } from '@/api/examSystem/examModule/jobCategory'
export default {
  props: {
    modalData: Object
  },
  data () {
    return {
      idxArr: ['A', 'B', 'C', 'D', 'E', 'F'],
      classifyList: [], // 题目分类
      chapterList: [], // 章节
      ascriptionList: [], // 归属
      answerList: ['A', 'B'], // 答案下拉
      selectedTypeList: [],
      showAttributRemove: false,
      showOptionRemove: false,
      curQueTypeId: '',
      formDynamic: {
        question_id: '',
        question_topic: '',
        question_answer: '',
        attributArray: [{
          question_type_id: '',
          question_section_id: '',
          postArray: [],
          post_arr: []
        }],
        optionArray: [{
          option_code: '',
          option_content: ''
        }, {
          option_code: '',
          option_content: ''
        }]
      }
    }
  },
  methods: {
    // 新增保存
    createData () {
      this.$refs['formDynamic'].validate((valid) => {
        if (valid) {
          this.$Modal.confirm({
            title: '提示',
            content: '<p>确认新增题目？</p>',
            loading: true,
            onOk: () => {
              let newAttributArr = JSON.parse(JSON.stringify(this.formDynamic.attributArray))
              let arr = newAttributArr.map(e => e.post_arr.map(c => ({ question_post_id: c })))
              newAttributArr.map((item, idx) => {
                Object.assign(item, { postArray: arr[idx] })
                delete item.post_arr
              })
              this.formDynamic.optionArray.map((item, idx) => { item.option_code = this.idxArr[idx] })
              let data = {
                question_topic: this.formDynamic.question_topic,
                question_answer: this.formDynamic.question_answer.sort().join(),
                attributArray: JSON.stringify(newAttributArr),
                optionArray: JSON.stringify(this.formDynamic.optionArray)
              }
              API.addExamQuestion(data).then(res => {
                if (res.data.Code === 10000) {
                  this.loading = false
                  this.$Modal.remove()
                  this.modalData.modal = false
                  this.$Message.success(res.data.Message)
                  this.$emit('callback')
                } else {
                  this.loading = false
                  this.$Modal.remove()
                  this.$Message.error(res.data.Message)
                }
              })
            }
          })
        }
      })
    },
    // 编辑保存
    updateData () {
      this.$refs['formDynamic'].validate((valid) => {
        if (valid) {
          this.$Modal.confirm({
            title: '提示',
            content: '<p>确认修改题目？</p>',
            loading: true,
            onOk: () => {
              let newAttributArr = JSON.parse(JSON.stringify(this.formDynamic.attributArray))
              let arr = newAttributArr.map(e => e.post_arr.map(c => ({ question_post_id: c })))
              newAttributArr.map((item, idx) => {
                Object.assign(item, { postArray: arr[idx] })
                delete item.post_arr
              })
              this.formDynamic.optionArray.map((item, idx) => { item.option_code = this.idxArr[idx] })
              let data = {
                question_id: this.formDynamic.question_id,
                question_topic: this.formDynamic.question_topic,
                question_answer: this.formDynamic.question_answer.sort().join(),
                attributArray: JSON.stringify(newAttributArr),
                optionArray: JSON.stringify(this.formDynamic.optionArray)
              }
              API.updateExamQuestion(data).then(res => {
                if (res.data.Code === 10000) {
                  this.loading = false
                  this.$Modal.remove()
                  this.modalData.modal = false
                  this.$Message.success(res.data.Message)
                  this.$emit('callback')
                } else {
                  this.loading = false
                  this.$Modal.remove()
                  this.$Message.error(res.data.Message)
                }
              })
            }
          })
        }
      })
    },
    // 增加内容信息
    handleAdd (type) {
      if (type === 'classify') {
        this.showAttributRemove = this.formDynamic.attributArray.length > 0
        this.formDynamic.attributArray.push({
          question_type_id: '',
          question_section_id: '',
          postArray: [],
          post_arr: []
        })
      } else {
        this.formDynamic.optionArray.push({
          option_code: '',
          option_content: ''
        })
        this.showOptionRemove = this.formDynamic.optionArray.length > 0
        this.answerList = [...this.answerList, this.idxArr[this.formDynamic.optionArray.length - 1]]
      }
    },
    // 移除内容信息
    handleRemove (idx, type) {
      if (type === 'classify') {
        this.showAttributRemove = this.formDynamic.attributArray.length > 2
        if (this.formDynamic.attributArray.length > 1) {
          this.formDynamic.attributArray.splice(idx, 1)
          this.selectedTypeList.splice(idx, 1)
          this.chapterList.splice(idx, 1)
        }
      } else {
        this.answerList.pop()
        this.showOptionRemove = this.formDynamic.optionArray.length > 3
        if (this.formDynamic.optionArray.length > 1) {
          this.formDynamic.optionArray.splice(idx, 1)
        }
      }
    },
    // 模态框展示
    modalShow (val) {
      if (val) {
        this.modalData.questionTypeData.map(e => { // 题目分类下拉
          this.classifyList.push({
            question_type_id: e.value,
            type_name: e.label
          })
        })
        queryExamPostList().then(res => { // 获取职务下拉
          if (res.data.Code === 10000) {
            res.data.Result.map(e => {
              this.ascriptionList.push({
                post_name: e.post_name,
                question_post_id: e.question_post_id
              })
            })
          }
        })
        if (this.modalData.type === 'update') {
          this.formDynamic = { ...this.formDynamic, ...this.modalData.data }
          this.formDynamic.question_answer = this.formDynamic.question_answer.split(',')
          this.formDynamic.attributArray.forEach((item, idx) => {
            let curArr = []
            this.selectedType(item.question_type_id, idx)
            item.postArray.forEach(e => {
              curArr.push(e.question_post_id)
            })
            item.post_arr = curArr
          })
          this.showAttributRemove = this.formDynamic.attributArray.length > 1
          this.showOptionRemove = this.formDynamic.optionArray.length > 2
          this.answerList = this.idxArr.slice(0, this.formDynamic.optionArray.length)
        } else {
          this.showAttributRemove = false
          this.showOptionRemove = false
          this.$refs['formDynamic'].resetFields()
        }
      } else {
        this.classifyList = []
        this.chapterList = []
        this.ascriptionList = []
        this.selectedTypeList = []
        this.answerList = ['A', 'B']
        this.$nextTick(() => {
          this.formDynamic = {
            question_id: '',
            question_topic: '',
            question_answer: '',
            attributArray: [{
              question_type_id: '',
              question_section_id: '',
              postArray: [],
              post_arr: []
            }],
            optionArray: [{
              option_code: '',
              option_content: ''
            }, {
              option_code: '',
              option_content: ''
            }]
          }
        })
      }
    },
    // 选择题目分类触发
    selectedType (val, idx) {
      if (val) {
        this.chapterList[idx] = []
        if (this.selectedTypeList.includes(this.curQueTypeId)) {
          let idIndex = this.selectedTypeList.indexOf(this.curQueTypeId)
          this.selectedTypeList.splice(idIndex, 1)
        }
        this.selectedTypeList.push(val)
        queryQuestionSectionList({ question_type_id: val }).then(res => { // 获取章节下拉
          if (res.data.Code === 10000) {
            res.data.Result.map(e => {
              this.chapterList[idx].push({
                section_name: e.section_name,
                question_section_id: e.question_section_id
              })
            })
            this.$forceUpdate()
          }
        })
      }
    },
    // 置灰已选题目分类下拉项
    changeTypeOption (index) {
      this.curQueTypeId = this.formDynamic.attributArray[index].question_type_id
    }
  }
}
</script>
<style lang="less" scoped>
.demo-drawer-footer {
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  background: #fff;
}
.label_div {
  position: absolute;
  left: 10px;
  top: 7px;
  color: #515a6e;
}
</style>

<template>
  <!-- 进行中、已完成 统计 -->
  <Drawer :title="loadingStatisticsModalData.title"
    v-model="loadingStatisticsModalData.modal"
    :data="loadingStatisticsModalData.data"
    @on-visible-change="modalShow"
    width="850">
    <div class="tab_btn">
      <Button :type="curTab === 'noSignIn' ? 'primary' : 'default'" size="large" @click="changeTab('noSignIn')">未签到人员</Button>
      <Button :type="curTab === 'noExam' ? 'primary' : 'default'" size="large" @click="changeTab('noExam')">未考试人员</Button>
      <Button :type="curTab === 'noEvaluated' ? 'primary' : 'default'" size="large" @click="changeTab('noEvaluated')">未评价人员</Button>
      <Button :type="curTab === 'all' ? 'primary' : 'default'" size="large" @click="changeTab('all')">全部</Button>
      <Button style="float: right;" @click="exportData">导出</Button>
    </div>
    <Table border :loading="listLoading" ref="selection" :columns="columns" :data="list"></Table>
    <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
      :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
    <!-- <Button type="primary" style="margin: 20px auto 0; display: block;">发送消息至未签到人员</Button> -->
    <div class="demo-drawer-footer">
      <Button type="primary" @click="loadingStatisticsModalData.modal = false">返回</Button>
    </div>
    <!-- 考题记录 -->
    <Modal v-model="examModal" title="考题记录" width="680" class="modal_height">
      <div v-for="(item, index) in curMemberExamArr" :key="index" style="margin-bottom: 15px;">
        <!-- 选择题 theme_exam_title_type: 1单选；2多选；3问答 -->
        <div v-if="item.theme_exam_title_type !== '3'">
          <div>{{ index+1 }}. {{ item.theme_exam_title }}（）。 <span style="color: red;margin-right: 8px;">{{ item.theme_exam_title_type === '1' ? '单选' : '多选' }}</span> {{ item.member_exam_score }}分</div>
          <div v-for="(itm, idx) in item.options" :key="idx" class="option_class" :class="item.member_exam_answer.includes(itm.exam_option_code) ? 'red' : ''">
            <em></em>
            <span>{{ itm.exam_option_code + '.' + itm.exam_option_content }}</span>
          </div>
          <div>正确答案： <span>{{item.theme_exam_answer}}</span></div>
        </div>
        <!-- 问答题 -->
        <div v-else>
          <span>{{ index+1 }}. {{ item.theme_exam_title }}： <Input v-model="item.member_exam_score" :placeholder="item.member_exam_score" :disabled="item.isScore" class="score_input" /> 分</span>
          <Button style="color: #169bd5;" v-if="item.isScore" type="text" @click="changeScore(index)">编辑</Button>
          <Button style="color: #169bd5;" v-else type="text" @click="undateScore(index)">保存</Button>
          <Input v-model="item.member_exam_analysis" type="textarea" style="width: 100%;margin-top: 5px;" disabled />
        </div>
      </div>
      <div slot="footer">
        <Button type="primary" @click="examModal = false">返回</Button>
      </div>
    </Modal>
    <!-- 评价记录 -->
    <Modal v-model="evaluateModal" title="评价记录" width="680" class="modal_height">
      <div v-for="(item, index) in curEvaluateArr" :key="index" style="margin-bottom: 10px;">
        <div v-if="item.theme_evaluate_title_type !== '2'">
          <div>{{ index+1 }}. {{ item.theme_evaluate_title }}（）。</div>
          <Rate disabled :value="parseInt(item.member_evaluate_score)" :count='10' />
        </div>
        <div v-else>
          {{ index+1 }}. {{ item.theme_evaluate_title }}:
          <Input v-model="item.member_evaluate_bak"  type="textarea" readonly style="width: 100%;margin-top: 5px;" /></div>
      </div>
      <div slot="footer">
        <Button type="primary" @click="evaluateModal = false">返回</Button>
      </div>
    </Modal>
  </Drawer>
</template>
<script>
import { queryTrainMemberThemePage, updateScore, exportThemeStatusTemplate } from '@/api/examSystem/trainingModule/memberManagement'
export default {
  props: {
    loadingStatisticsModalData: Object
  },
  data () {
    return {
      curMemberExamArr: [],
      curEvaluateArr: [],
      examModal: false,
      evaluateModal: false,
      curTab: 'noSignIn',
      listLoading: false,
      list: [],
      total: null,
      listCurrent: 1,
      listQuery: {
        member_id: '',
        train_member_id: '',
        theme_id: '',
        member_apply_state: '',
        member_sign_state: '0',
        member_dine_state: '',
        member_evaluate_state: '',
        member_exam_state: '',
        pageSize: 10,
        pageIndex: 1
      },
      columns: [
        {
          title: '参会人员',
          key: 'member_name',
          align: 'center'
        },
        {
          title: '电话',
          key: 'member_mobile',
          align: 'center'
        },
        {
          title: '报名状态',
          align: 'center',
          render: (h, params) => {
            let curVal = params.row.member_apply_state === '1' ? '√' : '×'
            return h('div', {}, curVal)
          }
        },
        {
          title: '签到状态',
          align: 'center',
          render: (h, params) => {
            let curVal = params.row.member_sign_state === '1' ? '√' : '×'
            return h('div', {}, curVal)
          }
        },
        {
          title: '考题',
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: {
                  icon: 'md-paper',
                  size: 'small',
                  disabled: params.row.member_exam_state === '0'
                },
                on: {
                  click: () => {
                    this.handleDetailModal(params.row.memberExamArr, 'exam')
                  }
                }
              }, '详情')
            ])
          }
        },
        {
          title: '评价',
          key: 'paper_score',
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: {
                  icon: 'md-paper',
                  size: 'small',
                  disabled: params.row.member_evaluate_state === '0'
                },
                on: {
                  click: () => {
                    this.handleDetailModal(params.row.evaluateArr, 'comment')
                  }
                }
              }, '详情')
            ])
          }
        }
      ]
    }
  },
  methods: {
    getList () {
      this.loading = true
      this.listQuery.theme_id = this.loadingStatisticsModalData.data.theme_id
      queryTrainMemberThemePage(this.listQuery).then(res => {
        if (res.data.Code === 10000) {
          this.loading = false
          this.list = res.data.Result
          this.total = res.data.Total
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 详情
    handleDetailModal (row, type) {
      if (type === 'exam') {
        this.examModal = true
        this.curMemberExamArr = row
        this.curMemberExamArr.map(item => {
          Object.assign(item, { isScore: true })
        })
      } else {
        this.evaluateModal = true
        this.curEvaluateArr = row
      }
    },
    // 编辑问答题评分
    changeScore (idx) {
      this.curMemberExamArr[idx].isScore = false
      this.$forceUpdate()
    },
    // 保存问答题评分
    undateScore (idx) {
      let data = {
        member_exam_id: this.curMemberExamArr[idx].member_exam_id,
        member_exam_score: this.curMemberExamArr[idx].member_exam_score
      }
      updateScore(data).then(res => {
        if (res.data.Code === 10000) {
          this.$Message.success('修改成员分数成功！')
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 导出
    exportData () {
    let data = {
      member_id: this.listQuery.member_id,
      train_member_id: this.listQuery.train_member_id,
      theme_id: this.listQuery.theme_id,
      member_apply_state: this.listQuery.member_apply_state,
      member_sign_state: this.listQuery.member_sign_state,
      member_dine_state: this.listQuery.member_dine_state,
      member_evaluate_state: this.listQuery.member_evaluate_state,
      member_exam_state: this.listQuery.member_exam_state
    }
      exportThemeStatusTemplate(data).then(res => {
        if (res.data.Code === 10000) {
          window.open(res.data.fileUrl, '_blank')
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // tab切换
    changeTab (type) {
      if (type === 'noSignIn') {
        this.curTab = 'noSignIn'
        this.listQuery.member_sign_state = '0'
        this.listQuery.member_exam_state = ''
        this.listQuery.member_evaluate_state = ''
      } else if (type === 'noExam') {
        this.curTab = 'noExam'
        this.listQuery.member_exam_state = '0'
        this.listQuery.member_evaluate_state = ''
        this.listQuery.member_sign_state = ''
      } else if (type === 'noEvaluated') {
        this.curTab = 'noEvaluated'
        this.listQuery.member_evaluate_state = '0'
        this.listQuery.member_sign_state = ''
        this.listQuery.member_exam_state = ''
      } else if (type === 'all') {
        this.curTab = 'all'
        this.listQuery.member_evaluate_state = ''
        this.listQuery.member_sign_state = ''
        this.listQuery.member_exam_state = ''
      }
      this.getList()
    },
    modalShow (val) {
      if (val) {
        this.getList()
      } else {
        this.curTab = 'noSignIn'
        this.listQuery = {
          member_id: '',
          train_member_id: '',
          theme_id: '',
          member_apply_state: '',
          member_sign_state: '0',
          member_dine_state: '',
          member_evaluate_state: '',
          member_exam_state: '',
          pageSize: 10,
          pageIndex: 1
        }
      }
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    }
  }
}
</script>
<style lang="less" scoped>
.demo-drawer-footer {
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  background: #fff;
}
.tab_btn {
  margin-bottom: 10px;
  button {
    margin-right: 10px;
  }
}
.score_input {
  width: 55px;
}
.modal_height {
  .option_class {
    line-height: 26px;
    position: relative;
  }
  em {
    border-radius: 50%;
    width: 14px;
    height: 14px;
    margin-right: 3px;
    border: 1px solid #333;
    display: inline-block;
    vertical-align: middle;
  }
  .red {
    color: #d9001b;
    &::before {
      content: '';
      display: inline-block;
      position: absolute;
      top: 10px;
      left: 3px;
      border-radius: 50%;
      border: 4px solid #d9001b;
    }
    em {
      border-color: #d9001b;
    }
  }
}
</style>
<style lang="less">
.score_input {
  .ivu-input {
    height: 28px;
    line-height: 28px;
    padding: 0 5px;
  }
}
.modal_height {
  .ivu-modal-body {
    max-height: 600px;
    overflow-y: scroll;
  }
}
</style>

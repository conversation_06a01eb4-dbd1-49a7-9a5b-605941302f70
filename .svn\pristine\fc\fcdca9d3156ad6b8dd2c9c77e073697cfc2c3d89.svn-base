import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 单种价格走势-统计图数据
export function queryMaterialList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/nuified/material/queryUnifiedMaterialAndDetailList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 单种价格走势-数据分页报表
export function queryMaterialPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/nuified/material/queryUnifiedMaterialAndDetailPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 数据导出
export function exportMaterial (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/materal/template/materialDetailsTemplate',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

<template>
  <div class="iframe_area" :style="collapsed ? 'width: calc(100% - 80px);' : 'width: calc(100% - 220px);'">
    <iframe
      :src="src"
      width="100%"
      height="100%"
      frameborder="0"
    ></iframe>
  </div>
</template>

<script>
export default {
  data () {
    return {
      src: ''
    }
  },
  computed: {
    collapsed () {
      return this.$store.state.app.collapsed
    }
  },
  // 组件的其他选项和逻辑
  created () {
    this.src = this.$route.query.url
  },
  watch: {
    '$route.query.url': function (newUrl, oldUrl) {
      this.src = this.$route.query.url
    }
  }
}
</script>
<style>
  .iframe_area {
    position: absolute;
    width: calc(100% - 220px);
    height: calc(100% - 70px);
  }
</style>

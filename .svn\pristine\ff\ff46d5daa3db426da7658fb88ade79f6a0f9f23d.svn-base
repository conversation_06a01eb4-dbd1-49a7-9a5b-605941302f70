<template>
  <div>
    <div class="statistic_area">
      <div>
        <Button v-if="isWarnShow" type="primary" size="small" icon="ios-undo" @click="backToBtnList">返回</Button>
        <Icon style="margin-left: 12px;" type="md-volume-down" size="16" />
        <span>
          超期预警：
          <span style="color: #e74c3c; font-weight: bold; cursor: pointer;" @click="showWarnMess('overdue')">{{ overdueNum }}</span>
          条
        </span>
        <span style="margin-left: 15px;">
          预到期预警：
          <span style="color: #ffa502; font-weight: bold; cursor: pointer;" @click="showWarnMess('warn')">{{ waringNum }}</span>
          条
        </span>
      </div>
    </div>
    <Spin size="large" fix v-if="spinShow">
      <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
      <div>{{ spinTxt }}</div>
    </Spin>
    <div>
      <Card>
        <Table class="table-box" ref="tableSelection" border row-key="id" :loading="isLoading" :columns="columnsList" :data="tableList" @on-row-dblclick="rowDbClick"></Table>
        <Page show-sizer :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="listQuery.pageSize" :current.sync="listQuery.pageIndex"
          :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
      </Card>
    </div>
    <BreakModal :modalData="breakModal" @breakBack="breakBack"></BreakModal>
  </div>
</template>

<script>
import API from '@/api/shipperOwner/contract'
import { getToken } from '@/libs/util'
import CryptoJS from 'crypto-js'
import BreakModal from '../breakModal'

export default ({
  components: {
    BreakModal
  },
  data () {
    return {
      waringNum: 0, // 预到期数量
      overdueNum: 0, // 到期数量
      isWarnShow: false, // 是否显示预警内容
      secretKey: 'UNIFIED_Xt603209',
      spinShow: false,
      spinTxt: '加载中',
      isLoading: false,
      breakModal: {
        modal: false,
        title: '合同解约',
        data: {}
      },
      listQuery: {
        pageSize: 10,
        pageIndex: 1,
        audit_status: 3
      },
      total: 0,
      columnsList: [
        {
          title: '姓名',
          key: 'seafarer_name',
          align: 'center'
        },
        {
          title: '合同职务',
          key: 'crt_duty_name',
          align: 'center'
        },
        {
          title: '合同编号',
          key: 'contract_no',
          align: 'center',
          render: (h, params) => {
            if (parseFloat(params.row.audit_status) === 4 && params.row.termination_reason !== '') {
              return h('Tooltip', {
                props: {
                  maxWidth: 300,
                  transfer: true
                }
              }, [
                h('div', {
                  slot: 'content'
                }, params.row.termination_reason),
                h('div', {
                  style: {
                    display: 'inline-flex',
                    alignItems: 'center',
                    marginTop: '5px'
                  }
                }, params.row.contract_no)
              ])
            } else {
              return h('div', {
                style: {
                  display: 'inline-flex',
                  alignItems: 'center',
                  marginTop: '5px'
                }
              }, params.row.contract_no)
            }
          }
        },
        {
          title: '合同类型',
          key: 'contract_type',
          align: 'center',
          render: (h, params) => {
            let typeStr = params.row.offline_flag === '1' ? '线下合同' : '云签合同'
            return h('div', {}, typeStr)
          }
        },
        {
          title: '身份证',
          key: 'seafarer_id_no',
          align: 'center',
          ellipsis: true,
          tooltip: true,
          minWidth: 80
        },
        {
          title: '开始时间',
          key: 'contract_period_from',
          align: 'center'
        },
        {
          title: '结束时间',
          key: 'contract_period_to',
          align: 'center'
        },
        {
          title: '预警状态',
          key: 'days_overdue',
          align: 'center',
          render: (h, params) => {
            let alarmStr = this.getStatusStr(params.row.days_overdue)
            return h('span', {
              style: {
                padding: '1px 10px',
                borderRadius: '15px',
                color: '#fff',
                background: this.getStatusColor(params.row.days_overdue)
              }
            }, alarmStr || '--')
          }
        },
        {
          title: '操作',
          key: 'seafarer_name',
          align: 'center',
          render: (h, params) => {
            return  h('Button', {
              props: {
                size: 'small',
                type: 'warning',
                icon: 'md-bookmark'
              },
              on: {
                click: () => {
                  this.handleBreak(params.row)
                }
              }
            }, '解除合约')
          }
        }
      ],
      tableList: []
    }
  },
  methods: {
    getList () {
      this.isLoading = true
      API.querySeafarerContractPage(this.listQuery).then(res => {
        this.isLoading = false
        if (res.data.Code === 10000) {
          this.tableList = res.data.Result
          this.total = res.data.Total
        }
      })
    },
    backToBtnList () { // 返回列表
      this.isWarnShow = false
      this.tableList = []
      this.listQuery.days_overdue_st = ''
      this.listQuery.days_overdue_ed = ''
      this.getList()
    },
    showWarnMess (str) { // 预警数量点击事件
      this.isWarnShow = true
      this.tableList = []
      this.listQuery.days_overdue_st = ''
      this.listQuery.days_overdue_ed = ''
      if (str === 'overdue') { // 超期预警点击
        Object.assign(this.listQuery, {
          days_overdue_st: 0
        })
        this.getList()
      }
      if (str === 'warn') { // 预到期预警点击
        Object.assign(this.listQuery, {
          days_overdue_st: -30,
          days_overdue_ed: 0
        })
        this.getList()
      }
    },
    handleBreak (row) { // 解约
      this.breakModal.modal = true
      this.breakModal.data = row
    },
    breakBack () { // 解约回调
      this.getList()
    },
    rowDbClick (row) { // 表格双击预览
      if (!row.wpsUrl) {
        this.$Message.warning('文件还未生成')
        return
      }
      try {
        const encryptedData = CryptoJS.enc.Base64.parse(row.wpsUrl)
        const key = CryptoJS.enc.Utf8.parse(this.secretKey)

        const decrypted = CryptoJS.AES.decrypt(
          { ciphertext: encryptedData },
          key,
          { mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7 }  // ECB 模式，不需要 IV
        )
        const decryptedText = decrypted.toString(CryptoJS.enc.Utf8)
        sessionStorage.setItem('wpsUrl', decryptedText)
        sessionStorage.setItem('token', getToken())
        const jump = this.$router.resolve({ name: 'viewFile' })
        window.open(jump.href, '_blank')
      } catch (err) {
        console.log('出错了： ' + err)
      }
    },
    getStatusStr (days_overdue) { // 返回预警文案
      let alarmStr = ''
      if (days_overdue !== '' && parseFloat(days_overdue) < -30) { // 未超期
        alarmStr = '未到期'
      }
      if (days_overdue !== '' && parseFloat(days_overdue) >= -30 && parseFloat(days_overdue) < 0) { // 30天内预警
        alarmStr = '还剩' + Math.abs(days_overdue) + '天到期'
      }
      if (days_overdue !== '' && parseFloat(days_overdue) >= 0) { // 超期预警
        alarmStr = '已超期' + days_overdue + '天'
      }
      return alarmStr
    },
    getStatusColor (days_overdue) { // 返回预警颜色
      let colorStr = ''
      if (days_overdue !== '' && parseFloat(days_overdue) < -30) { // 未超期
        colorStr = '#19be6b'
      }
      if (days_overdue !== '' && parseFloat(days_overdue) >= -30 && parseFloat(days_overdue) < 0) { // 30天内预警
        colorStr = '#ffa502'
      }
      if (days_overdue !== '' && parseFloat(days_overdue) >= 0) { // 超期预警
        colorStr = '#e74c3c'
      }
      return colorStr
    },
    getWarnNum () { // 获取预警数量
      API.queryFileDirectoryOverdueCount().then(res => {
        if (res.data.Code === 10000) {
          this.waringNum = res.data.waringNum
          this.overdueNum = res.data.overdueNum
        }
      })
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    }
  },
  created () {
    // this.$store.commit('setCollapsed', true)
    this.getWarnNum()
    this.getList()
  },
  beforeDestroy () {
    // this.$store.commit('setCollapsed', false)
  }
})
</script>
<style>
  .statistic_area {
    position: absolute;
    top: 10px;
    right: 300px;
    z-index: 999;
  }
  .table-box .ivu-table-cell {
    padding-left: 8px !important;
    padding-right: 5px !important;
  }
  .ship_btn_area {
    cursor: pointer;
    background: #f7f7f7;
    padding: 10px 8px;
    margin-bottom: 10px;
    border-radius: 5px;
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-all;
    white-space: nowrap;
  }
  .ship_btn_close {
    cursor: pointer;
    float: right;
  }
  .ship_btn_close:hover {
    color: #2d8cf0;
  }
  .left_show_hide {
    /* width: 20px; */
    display: inline-flex;
    height: 64px;
    text-align: center;
    padding: 2px 0;
    background: #fff;
    border-radius: 15px;
    border: 1px solid #d0cdcd;
    align-items: center;
    position: absolute;
    top: calc(50% - 32px);
    right: -8px;
    cursor: pointer;
  }
  .left_show_hide:hover {
    background: #2d8cf0;
    color: #fff;
  }
  .add_ship_btn {
    margin-top: 10px;
  }
  .btn_area {
    text-align: right;
    margin-bottom: 15px;
  }
  /* .back_btn {
    display: inline-flex;
    align-items: center;
    position: absolute;
    left: 10px;
  } */
  .btn_area button {
    margin-left: 10px;
  }
  .bread-span {
    display: inline-flex;
    align-items: center;
  }
  .demo-spin-icon-load{
    animation: ani-demo-spin 1s linear infinite;
  }
  @keyframes ani-demo-spin {
    from { transform: rotate(0deg);}
    50%  { transform: rotate(180deg);}
    to   { transform: rotate(360deg);}
  }
</style>
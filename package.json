{"name": "voyage-admin", "version": "2.0.0", "author": "henry_lin<<EMAIL>>", "private": false, "scripts": {"start": "npm run dev", "dev": "vue-cli-service serve --open", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "test:unit": "vue-cli-service test:unit", "test:e2e": "vue-cli-service test:e2e"}, "dependencies": {"@pdf-lib/fontkit": "^1.1.1", "axios": "^1.7.7", "babel-polyfill": "^6.26.0", "blueimp-md5": "^2.12.0", "clipboard": "^2.0.6", "codemirror": "^5.38.0", "countup": "^1.8.2", "cropperjs": "^1.2.2", "crypto-js": "^4.2.0", "dayjs": "^1.7.7", "docxtemplater": "^3.50.0", "docxtemplater-image-module": "^3.1.0", "echarts": "^4.9.0", "es6-promise": "^4.2.8", "file-saver": "^2.0.5", "html2canvas": "^1.0.0-alpha.12", "iview-area": "^1.6.0", "iview-select-tree": "^1.0.13", "js-cookie": "^2.2.0", "js-md5": "^0.7.3", "jshint": "^2.12.0", "jszip-utils": "^0.1.0", "os": "^0.1.2", "pdf-lib": "^1.17.1", "pdfjs-dist": "^2.5.207", "pizzip": "^3.1.7", "qrcodejs2": "0.0.2", "qs": "^6.9.0", "simplemde": "^1.11.2", "socket.io-client": "^4.8.1", "sortablejs": "^1.7.0", "tree-table-vue": "^1.1.0", "v-org-tree": "^1.0.6", "view-design": "^4.6.0", "vue": "^2.6.10", "vue-fullscreen": "^2.1.6", "vue-pdf": "^4.2.0", "vue-router": "^3.1.3", "vue-signature-pad": "^1.1.18", "vuedraggable": "^2.16.0", "vuex": "^3.1.1", "wangeditor": "^3.1.1", "xlsx": "^0.13.3"}, "devDependencies": {"@vue/cli-plugin-babel": "^3.0.1", "@vue/cli-plugin-eslint": "^3.0.1", "@vue/cli-plugin-unit-mocha": "^3.0.1", "@vue/cli-service": "^3.0.1", "@vue/eslint-config-standard": "^3.0.0-beta.10", "@vue/test-utils": "^1.0.0-beta.10", "chai": "^4.1.2", "eslint-plugin-cypress": "^2.0.1", "less": "^2.7.3", "less-loader": "^4.0.5", "lint-staged": "^6.0.0", "mockjs": "^1.0.1-beta3", "vue-template-compiler": "^2.5.13"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.js": ["vue-cli-service lint", "git add"], "*.vue": ["vue-cli-service lint", "git add"]}}
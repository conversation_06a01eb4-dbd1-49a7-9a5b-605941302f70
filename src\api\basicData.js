import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 查询船舶列表(包含基础数据)
export function queryBasicShipList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/vmp/common/query/queryBasicShipList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取一体化用户列表
export function queryUnifiedAccountList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/vmp/common/query/queryUnifiedAccountList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询港口列表
export function queryBasicPortList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/vmp/common/query/queryBasicPortList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询码头列表
export function queryBasicWharfList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/vmp/common/query/queryBasicWharfList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询货品 列表
export function queryVmpBasicCargoList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/vmp/common/query/queryBasicCargoList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询货品 列表
export function queryBasicCargoList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/vmp/common/query/queryBasicCargoList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询公司 列表 类型（1船东；2货主；3代理） - 主要获取货主列表
export function queryUsCompanyList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/vmp/common/query/queryUsCompanyList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询公司船舶信息
export function queryCompanyShipList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/sys/ship/queryCompanyShipList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取用途下拉数据
export function materialUsefor (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/nuified/material/common/queryUnifiedMaterialDetailUsefor',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取名称下拉数据
export function materialInventoryname (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/nuified/material/common/queryUnifiedMaterialDetailInventoryname',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取型号下拉数据
export function materialInventorystd (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/nuified/material/common/queryUnifiedMaterialDetailInventorystd',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取船舶下拉数据
export function materialWarehousename (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/nuified/material/common/queryUnifiedMaterialWarehousename',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取供应商下拉数据
export function materialVendorname (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/nuified/material/common/queryUnifiedMaterialVendorname',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 系统时间
export function getSysTime (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/nuified/material/common/getSysTime',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 数据同步
export function dataToSync (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/material/data/resource/addKDMaterialSourceDataToDataBaseImmediately',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 添加/修改备注，油料价格-日期分析区域修改
export function addOrUpdateAffiliate (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/nuified/material/affiliate/addOrUpdateUnifiedMaterialAffiliate',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询系统字典-无分页
export function queryDictList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/dict/queryDictList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询系统字典-分页
export function queryDictPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/dict/queryDictPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询缓存中的字典条目-无分页
export function queryDictCacheList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/dict/queryDictCacheList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询字典条目 分页
export function queryDictEntryPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/dict/entry/queryDictEntryPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 查询字典条目 无分页
export function queryDictEntryList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/dict/entry/queryDictEntryList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 添加 字典条目
export function addDictEntry (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/dict/entry/addDictEntry',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 添加 字典条目
export function addDictEntryShort (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/dict/entry/addDictEntryShort',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 修改 字典条目
export function updateDictEntry (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/dict/entry/updateDictEntry',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 修改 字典条目
export function updateDictEntryShort (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/dict/entry/updateDictEntryShort',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 删除 字典条目
export function delDictEntry (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/dict/entry/delDictEntry',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 上传附件
export function fileUpload (data) {
  return axios.request({
    url: '/basic/attachment/upload',
    method: 'post',
    headers: config.ajaxHeader,
    data
  })
}

// 删除附件-单个
export function deleteFile (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/basic/attachment/deleteWps',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  queryBasicShipList,
  queryUnifiedAccountList,
  queryBasicPortList,
  queryBasicWharfList,
  queryVmpBasicCargoList,
  queryUsCompanyList,
  queryCompanyShipList,
  materialUsefor,
  materialInventoryname,
  materialInventorystd,
  materialWarehousename,
  materialVendorname,
  getSysTime,
  dataToSync,
  addOrUpdateAffiliate,
  queryDictList,
  queryDictPage,
  queryDictCacheList,
  queryDictEntryPage,
  queryDictEntryList,
  addDictEntry,
  addDictEntryShort,
  updateDictEntry,
  updateDictEntryShort,
  delDictEntry,
  fileUpload,
  deleteFile
}

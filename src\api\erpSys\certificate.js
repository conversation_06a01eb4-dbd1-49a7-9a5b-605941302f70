import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 目录文件列表信息 分页
export function queryFileDirectoryPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/file/directory/queryFileDirectoryPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 目录文件列表信息 不分页
export function queryFileDirectoryList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/file/directory/queryFileDirectoryList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 上传附件
export function fileUpload (data, uploadProgress) {
  return axios.request({
    url: '/file/directory/addFileDirectory',
    method: 'post',
    headers: config.ajaxHeader,
    data,
    onUploadProgress: uploadProgress
  })
}

// 新增目录文件
export function addFileDirectory (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/file/directory/addFileDirectory',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 批量修改目录/附件基础信息
export function updateFileDirectoryBatch (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/file/directory/updateFileDirectoryBatch',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 修改目录文件
export function updateFileDirectory (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/file/directory/updateFileDirectory',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 替换附件 单个
export function replaceSingleFile (data) {
  return axios.request({
    url: '/file/directory/replaceSingleFile',
    method: 'post',
    headers: config.ajaxHeader,
    data
  })
}

// 删除目录文件
export function delFileDirectory (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/file/directory/delFileDirectory',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 批量删除 文件或附件
export function deleteFileDirectories (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/file/directory/deleteFileDirectories',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 批下载并压缩
export function downloadFileDirectory (data) {
  // let qsData = Qs.stringify(data)
  let header = {...config.ajaxHeader, ...{
    'Content-Type': 'application/json;application/octet-stream'
  }}
  return axios.request({
    url: '/file/directory/downloadFileDirectory',
    method: 'get',
    headers: header,
    responseType: 'blob',
    params: data
  })
}

// 查询目录树结构
export function getDirectoryTree (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/file/directory/getDirectoryTree',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 预警文件数量
export function queryFileDirectoryOverdueCount (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/file/directory/queryFileDirectoryOverdueCount',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  queryFileDirectoryPage,
  queryFileDirectoryList,
  fileUpload,
  addFileDirectory,
  updateFileDirectory,
  updateFileDirectoryBatch,
  replaceSingleFile,
  delFileDirectory,
  deleteFileDirectories,
  downloadFileDirectory,
  getDirectoryTree,
  queryFileDirectoryOverdueCount
}

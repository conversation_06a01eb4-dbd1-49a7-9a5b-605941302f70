import ierp_axios from '@/libs/ierp.api.request'
import Qs from 'qs'
import config from '@/config'

// 公司物料-列表
export function materialQuery (data) {
  var statusVal = ''
  switch (data.status.length) {
    case 1:
      statusVal = 'status=' + data.status
      break
    case 2:
      statusVal = 'status=' + data.status[0] + '&status=' + data.status[1]
      break
    case 3:
      statusVal = 'status=' + data.status[0] + '&status=' + data.status[1] + '&status=' + data.status[2]
      break
    default:
      break
  }
  return ierp_axios.request({
    url: '/ierp/kapi/v2/xtgf/basedata/bd_material/goodSearch?' + statusVal,
    method: 'get',
    params: data
  })
}

// 公司物料-查询
export function materialLook (data) {
  var statusVal = ''
  switch (data.status.length) {
    case 1:
      statusVal = 'status=' + data.status
      break
    case 2:
      statusVal = 'status=' + data.status[0] + '&status=' + data.status[1]
      break
    case 3:
      statusVal = 'status=' + data.status[0] + '&status=' + data.status[1] + '&status=' + data.status[2]
      break
    default:
      break
  }
  return ierp_axios.request({
    url: '/ierp/kapi/v2/xtgf/basedata/bd_material/materialLook?' + statusVal,
    method: 'get',
    params: data
  })
}

// 公司物料-删除
export function deleteCompanyMaterial (data) {
  let qsData = Qs.stringify(data)
  return ierp_axios.request({
    url: '/ierp/kapi/v2/xtgf/basedata/bd_material/goodDelete',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

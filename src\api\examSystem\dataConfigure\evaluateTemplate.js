import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 评价模板列表-无分页
export function queryEvaluateTemplateList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/train/evaluate/template/queryList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 编辑批量保存
export function evaluateTemplateAddAll (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/train/evaluate/template/addAll',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

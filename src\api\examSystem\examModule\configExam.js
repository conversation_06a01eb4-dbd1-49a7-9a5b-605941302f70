import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 获取考试配置
export function queryExamConfig (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/exam/paper/config/queryExamPaperConfig',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 设置考试配置
export function setExamConfig (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/exam/paper/config/addOrUpdateExamPaperConfig',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  queryExamConfig,
  setExamConfig
}

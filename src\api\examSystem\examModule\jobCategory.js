import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 职务列表-无分页
export function queryExamPostList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/exam/question/post/queryExamQuestionPostList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 增加职务
export function addExamPost (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/exam/question/post/addExamQuestionPost',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 修改职务
export function updateExamPost (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/exam/question/post/updateExamQuestionPost',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  queryExamPostList,
  addExamPost,
  updateExamPost
}
import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 成员列表
export function queryExamMemberPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/exam/member/queryExamMemberPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 成员新增
export function addMember (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/exam/member/addExamMember',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 成员修改
export function updateMember (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/exam/member/updateExamMember',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 成员删除
export function deleteMember (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/exam/member/delExamMember',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 成绩查询-试卷信息列表 分页
export function queryExamPaperPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/exam/paper/queryExamPaperHistoryListPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  queryExamMemberPage,
  addMember,
  updateMember,
  deleteMember,
  queryExamPaperPage
}
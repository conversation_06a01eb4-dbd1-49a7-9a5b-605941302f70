import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 题库类别列表-无分页
export function queryQuestionTypeList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/exam/question/type/queryExamQuestionTypeList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 增加题库类别
export function addQuestionType (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/exam/question/type/addExamQuestionType',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 修改题库类别
export function updateQuestionType (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/exam/question/type/updateExamQuestionType',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 章节列表-无分页
export function queryQuestionSectionList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/exam/question/section/queryExamQuestionSectionList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 新增章节
export function addQuestionSection (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/exam/question/section/addExamQuestionSection',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 修改章节
export function updateQuestionSection (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/exam/question/section/updateExamQuestionSection',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  queryQuestionTypeList,
  addQuestionType,
  updateQuestionType,
  queryQuestionSectionList,
  addQuestionSection,
  updateQuestionSection
}
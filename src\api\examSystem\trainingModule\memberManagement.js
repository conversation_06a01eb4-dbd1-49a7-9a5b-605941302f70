import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 培训成员列表-分页
export function queryTrainMemberPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/train/member/queryMemberAndTrainInfoPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 详情
export function queryTrainMemberThemePage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/train/member/queryMemberThemePage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 成员详情-培训课题-问答题考试评分保存
export function updateScore (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/train/member/exam/update',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 主题培训成员已计划-统计的导出
export function exportThemeTemplate (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/train/theme/queryMemberThemeTemplate',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 主题培训成员已完成-统计的导出
export function exportThemeStatusTemplate (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/train/theme/memberThemeStatusTemplate',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 培训课题列表-分页
export function queryTrainThemePage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/train/theme/queryPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 培训课题-新增
export function trainThemeAdd (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/train/theme/addAll',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 培训课题-编辑保存使用
export function trainThemeUpdateAll (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/train/theme/updateAll',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 培训课题-培训列表修改使用
export function trainThemeUpdateList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/train/theme/update',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 培训课题-删除
export function trainThemedeleteAll (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/train/theme/deleteAll',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 附件列表 无分页
export function queryAttachmentList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/train/theme/attachment/queryList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 上传附件
export function fileUpload (data) {
  return axios.request({
    url: '/train/theme/attachment/upload',
    method: 'post',
    headers: config.ajaxHeader,
    data
  })
}

// 删除附件-单个
export function deleteAttachment (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/train/theme/attachment/delete',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 培训人员及就餐人员数量统计
export function queryTrainMemberSum (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/train/member/queryThemeTrainMemberStateSum',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 已完成主题-汇总-考试统计
export function queryTrainExamInfo (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/train/theme/queryTrainThemeExamStatInfo',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 已完成主题-汇总-评价统计
export function queryTrainEvaluateInfo (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/train/theme/queryTrainThemeEvaluateStatInfo',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  queryTrainThemePage,
  trainThemeAdd,
  trainThemeUpdateAll,
  trainThemeUpdateList,
  trainThemedeleteAll,
  queryAttachmentList,
  fileUpload,
  deleteAttachment,
  queryTrainMemberSum,
  queryTrainExamInfo,
  queryTrainEvaluateInfo
}

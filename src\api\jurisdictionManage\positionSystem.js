import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 审批时间配置获取
export function queryRemindTime (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/perf/remind/time/queryPerfRemindTimeList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 审批时间配置保存
export function saveRemindTime (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/perf/remind/time/addOrUpdatePerfRemindTime',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  queryRemindTime,
  saveRemindTime
}

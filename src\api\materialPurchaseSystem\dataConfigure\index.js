import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 数据配置--用途列表
export function queryUseforConfigAll (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/nuified/material/usefor/queryUnifiedMaterialUseforConfigAll',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 配置保存
export function configUseforAllSave (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/nuified/material/usefor/configUnifiedMaterialUseforAll',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取组织仓库列表
export function queryOrgConfigList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/nuified/material/organization/queryUnifiedMaterialOrganizationConfigAll',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 保存组织仓库列表
export function saveOrgConfigList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/nuified/material/organization/configUnifiedMaterialOrganizationAll',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取组织仓库名称
export function queryOrgConfigName (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/nuified/material/common/queryUMOrganizationConfigBox',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

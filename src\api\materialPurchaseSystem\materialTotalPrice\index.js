import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 用途分析
export function queryUseforAnalyse (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/nuified/material/getUnifiedMaterialCostSumGroupByUsefor',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 总金额分析—用途分析—全部物料金额数据表 主表
// 总金额分析—船舶分析—各船物料金额数据表 附表
export function exportUsefor (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/materal/template/getUnifiedMaterialCostSumGroupByUseforTemplate',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 总金额分析—船舶分析—各船物料金额数据表 主表
// 总金额分析—用途分析—全部物料金额数据表 从表
export function exportShipfor (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/materal/template/getUnifiedMaterialCostSumGroupByWarehousenameTemplate',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 船舶分析
export function queryWarehousenameAnalyse (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/nuified/material/getUnifiedMaterialCostSumGroupByWarehousename',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 船舶分析数据导出
export function exportWarehousename (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/materal/template/materialCostSumGroupByWarehousename',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 时间分析
export function queryDateAnalyse (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/nuified/material/getUnifiedMaterialCostSumGroupByMonth',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 总金额分析—时间分析—全部物料金额数据表 主表
export function exportMonth (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/materal/template/getUnifiedMaterialCostSumGroupByMonthTemplate',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 总金额分析—时间分析—全部物料金额数据表 附表
export function exportMonthSub (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/materal/template/getUnifiedMaterialCostSumGroupByUseforForMonthTemplate',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 油料价格-月份分析
export function queryOilList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/nuified/material/queryUnifiedMaterialAndDetailOilList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 数据导出
export function exportOilList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/materal/template/materialAndDetailOilList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 数据导出(季度)
export function exportOilListByQuarter (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/materal/template/materialAndDetailOilListByQuarter',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 油料价格-季度分析 含第三方
export function queryOilListByQuarter (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/nuified/material/getUnifiedMaterialOilInfoGroupByQuarter',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 油料价格-月份分析 含第三方
export function queryOilListByMonth (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/nuified/material/queryUnifiedMaterialOilListByMonth',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 油料价格-月份分析 数据导出 含第三方
export function exportOilListByMonth (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/materal/template/materialAndDetailOilListByMonth',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 油料价格-日期分析 分页 含第三方
export function queryOilListByDayPage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/nuified/material/queryUnifiedMaterialOilListByDay',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 油料价格-日期分析 无分页 含第三方
export function queryOilListByDayList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/nuified/material/queryUnifiedMaterialOilListByDayList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 油料价格-日期分析 数据导出 含第三方
export function exportOilListByDay (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/materal/template/materialAndDetailOilListByDay',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 油料价格-区域分析
export function queryOilAreaList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/nuified/material/getUnifiedMaterialOilArea',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 油料价格-区域分析 数据导出
export function exportOilAreaList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/materal/template/getUnifiedMaterialOilArea',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

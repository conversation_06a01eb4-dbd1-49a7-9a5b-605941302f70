import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 三方价格列表 分页
export function queryOilPricePage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/nuified/oil/price/queryUnifiedOilPricePage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 三方价格列表 无分页
export function queryOilPriceList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/nuified/oil/price/queryUnifiedOilPriceList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 三方价格批量新增
export function addOilPrice (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/nuified/oil/price/addBatchUnifiedOilPrice',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 三方价格编辑
export function updateOilPrice (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/nuified/oil/price/updateUnifiedOilPrice',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 三方价格删除
export function delOilPrice (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/nuified/oil/price/delUnifiedOilPrice',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 卓创价格折线图
export function oilPriceListForType (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/nuified/oil/price/queryUnifiedOilPriceListForType',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 卓创价格导出
export function exportOilPriceList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/report/materal/template/queryUnifiedOilPriceTemplate',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

import axios from '@/libs/api.request'
import Qs from 'qs'
import config from '@/config'

// 保存/修改 绩效表单
export function addOrUpdatePerfForm (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/perf/form/addOrUpdatePerfForm',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 审批列表获取
export function queryPerformancePage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/perf/flow/history/queryPerfFlowWorksheetPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 审批档案列表获取
export function querySysPerformancePage (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/perf/form/queryPerfFormPage',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 提交流程
export function submitPerfForm (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/perf/form/submitPerfForm',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取表单详情
export function getPerfFormInfo (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/perf/form/getPerfFormInfo',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 获取表单流转历史
export function queryPerfFlowHistoryList (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/perf/flow/history/queryPerfFlowHistoryList',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

// 上传附件
export function fileUploadForPerf (data) {
  return axios.request({
    url: '/basic/attachment/uploadForPerf',
    method: 'post',
    headers: config.ajaxHeader,
    data
  })
}

// 查询加减分说明
export function queryPerfAddReduceRemark (data) {
  let qsData = Qs.stringify(data)
  return axios.request({
    url: '/perf/dept/flow/queryPerfDeptFlowRemark',
    method: 'post',
    headers: config.ajaxHeader,
    data: qsData
  })
}

export default {
  addOrUpdatePerfForm,
  queryPerformancePage,
  querySysPerformancePage,
  submitPerfForm,
  getPerfFormInfo,
  queryPerfFlowHistoryList,
  fileUploadForPerf,
  queryPerfAddReduceRemark
}

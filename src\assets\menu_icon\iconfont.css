@font-face {
  font-family: "iconfont"; /* Project id  */
  src: url('iconfont.ttf?t=1637653770506') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-menu_icon1:before {
  content: "\e60c";
}

.icon-menu_icon5:before {
  content: "\e60d";
}

.icon-menu_icon2:before {
  content: "\e60e";
}

.icon-menu_icon6:before {
  content: "\e60f";
}

.icon-menu_icon7:before {
  content: "\e610";
}

.icon-menu_icon3:before {
  content: "\e611";
}

.icon-menu_icon4:before {
  content: "\e612";
}

.icon-menu_icon8:before {
  content: "\e613";
}

.icon-menu_icon9:before {
  content: "\e614";
}


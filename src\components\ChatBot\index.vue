<template>
  <div class="chat-container" :class="{ 'chat-expanded': isExpanded, 'chat-fullscreen': isFullscreen }">
    <!-- 聊天图标按钮 -->
    <div class="chat-icon" @click="toggleChat" v-if="!isExpanded">
      <Icon type="ios-chatbubbles" size="28" />
      <Badge :count="unreadCount" class="unread-badge" v-if="unreadCount > 0" />
    </div>

    <!-- 聊天窗口 -->
    <div class="chat-window" v-if="isExpanded">
      <div class="chat-header">
        <div class="chat-title">
          <Icon type="ios-headset" size="18" /> 智能助手
        </div>
        <div class="chat-actions">
          <Button type="primary" size="small" class="action-btn clear-btn" @click="clearChat" title="清除聊天记录">
            <Icon type="ios-trash" /> 清除
          </Button>
          <Button type="primary" size="small" class="action-btn minimize-btn" @click="toggleChat" title="最小化">
            <Icon type="md-remove" />
          </Button>
          <Button type="primary" size="small" class="action-btn maximize-btn" @click="toggleFullscreen"
            :title="isFullscreen ? '退出全屏' : '全屏显示'">
            <Icon :type="isFullscreen ? 'ios-browsers-outline' : 'ios-browsers'" />
          </Button>
          <Button type="error" size="small" class="action-btn close-btn" @click="closeChat" title="关闭">
            <Icon type="md-close" />
          </Button>
        </div>
      </div>
      <div class="chat-messages" ref="messageContainer" @scroll="handleScroll">
        <div v-for="(message, index) in messages" :key="index"
          :class="['message', message.isUser ? 'user-message' : 'system-message']">
          <Avatar v-if="!message.isUser" icon="ios-contact" class="message-avatar" />
          <Avatar v-if="message.isUser" icon="ios-person" class="message-avatar" />
          <div class="message-content">
            <!-- 上传的文件 -->
            <div class="file-list" v-if="message.files && message.files.length > 0">
              <div v-for="(file, index) in message.files" :key="index" class="file-item">
                <div class="file-name">{{ file.name }}</div>
                <div>
                  <Icon :type="'md-' + iconType(file.extension)" size="16" />
                  {{ file.extension }} - {{ getFileSize(file.size) }}
                </div>
              </div>
            </div>
            <div class="message-text">
              <div class="markdown-body" :class="[{ 'bot-body': !message.isUser }]"
                v-html="formatMessage(message.content)"></div>
            </div>
            <div class="thinking-section" v-if="message.thinking" @click="toggleThinking(index)">
              <div class="thinking-header">
                <span>
                  <Icon type="ios-bulb" size="14" /> 思考过程
                </span>
                <Icon :type="message.showThinking ? 'ios-arrow-up' : 'ios-arrow-down'" size="14" />
              </div>
              <div class="thinking-content" v-if="message.showThinking" v-html="message.thinking"></div>
            </div>
            <!-- <div class="message-time">
              <Icon type="ios-time-outline" size="12" /> {{ formatTime(message.timestamp) }}
            </div> -->
          </div>

        </div>
        <div class="typing-indicator" v-if="isTyping">
          <Icon type="ios-loading" size="18" class="spin-icon-loading"></Icon>
          <span>正在输入...</span>
        </div>
      </div>

      <div class="chat-cotainer">

        <div class="file-list" v-if="files.length > 0">
          <div v-for="(file, index) in files" :key="index" class="file-item">
            <div class="file-name">{{ file.name }}</div>
            <div>
              <Icon :type="'md-' + iconType(file.extension)" size="16" />
              {{ file.extension }} - {{ getFileSize(file.size) }}
            </div>
            <div class="file-delete" @click="deleteFile(index)">
              <Icon type="md-close-circle" size="16" />
            </div>
          </div>
        </div>

        <div class="chat-input-container">
          <div class="input-wrapper">
            <Input type="textarea" v-model="newMessage" @on-keydown="handleKeyDown"
              placeholder="请输入消息... (Shift+Enter 发送)" :autosize="{ minRows: 1, maxRows: 4 }" ref="messageInput"
              class="chat-input" />
            <Upload ref="upload" v-if="appParameters.file_upload.enabled" :action="apiUrl + '/files/upload'"
              :show-upload-list="false" class="attachment-btn"
              :class="{ 'disabled': files.length >= appParameters.file_upload.number_limits }"
              :limit="appParameters.file_upload.number_limits" :multiple="appParameters.file_upload.number_limits > 1"
              :accept="getAcceptType()" :data="{ user: userId }"
              :headers="{ 'Authorization': `Bearer ${this.api_key}` }" :on-success="handleUploadSuccess"
              :disabled="files.length >= appParameters.file_upload.number_limits">
              <Icon type="ios-attach" size="20" />
            </Upload>

          </div>
          <Button type="primary" class="send-button" @click="sendMessage" :disabled="sendBtnDisabled">
            <Icon type="ios-send" /> 发送
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import * as echarts from 'echarts'

export default {
  name: 'ChatBot',
  data() {
    return {
      isExpanded: false,
      isFullscreen: false,
      messages: [],
      newMessage: '',
      isTyping: false,
      unreadCount: 0,
      apiUrl: 'http://************/v1',
      api_keys: {
        '39675C79EB054F97A33C6B6DF680A6D9': 'app-4aMjGyH2y0qkx3yMFodsXZv7',
        '船员部ID': 'app-4aMjGyH2y0qkx3yMFodsXZv7',
      },
      userId: this.generateUserId(), // 生成用户ID
      conversation_id: '', // 会话id
      pollingInterval: null, // 轮询间隔引用
      isPollingEnabled: false, // 控制是否启用轮询
      windowPosition: { right: '20px', bottom: '20px' }, // 保存窗口位置
      dept_id: '',
      appParameters: {},
      files: [],
      fileTypes: {
        "document": '.txt, .md, .mdx, .markdown, .pdf, .html, .xlsx, .xls, .docx, .csv, .eml, .msg, .pptx, .ppt, .xml, .epub',
        "image": '.jpg, .jpeg, .png, .gif, .webp, .svg',
        "audio": '.mp3, .m4a, .wav, .webm, .amr, .mpga',
        "video": '.mp4, .mov, .mpeg, .mpga'
      },
      isLoadingMore: false, // 添加加载更多状态
      hasMoreMessage: true,
      echartsInstances: [], // 存储 echarts 实例
    }
  },
  computed: {
    api_key() {
      return this.api_keys[this.dept_id] || 'app-4aMjGyH2y0qkx3yMFodsXZv7';
    },
    sendBtnDisabled() {
      // 如果正在输入中，禁用发送按钮
      if (this.isTyping) {
        return true
      }
      // 其他情况，只判断输入框是否为空
      return !this.newMessage.trim()
    }
  },
  mounted() {
    // 添加Font Awesome
    if (!document.getElementById('font-awesome-css')) {
      const link = document.createElement('link');
      link.id = 'font-awesome-css';
      link.rel = 'stylesheet';
      link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css';
      document.head.appendChild(link);
    }
    this.dept_id = JSON.parse(localStorage.getItem('userData')).dept_id; // 获取部门ID

    this.getAppParameters(); // 获取应用参数

    this.fetchMessages(); // 加载保存的消息

    // 监听窗口大小变化，调整聊天窗口位置
    window.addEventListener('resize', this.handleWindowResize)
  },
  beforeDestroy() {
    // 清除轮询定时器
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval)
    }

    // 移除窗口大小变化监听
    window.removeEventListener('resize', this.handleWindowResize)
  },
  methods: {
    iconType(extension) {
      for (let key in this.fileTypes) {
        if (this.fileTypes[key].indexOf(extension) !== -1) {
          if (key === 'audio') {
            return 'md-musical-note'
          }
          if (key === 'video') {
            return 'md-videocam'
          }
          return key
        }
      }
      return 'md-document'
    },
    // 获取文件大小
    getFileSize(size) {
      if (size < 1024) {
        return size + 'B';
      } else if (size < 1024 * 1024) {
        return (size / 1024).toFixed(2) + 'KB';
      } else if (size < 1024 * 1024 * 1024) {
        return (size / 1024 / 1024).toFixed(2) + 'MB';
      } else {
        return (size / 1024 / 1024 / 1024).toFixed(2) + 'GB';
      }
    },
    handleUploadSuccess(response) {
      this.files.push(response);
    },
    // 获取允许上传的文件类型
    getAcceptType() {
      if (this.appParameters.file_upload.allowed_file_types.length === 1 && this.appParameters.file_upload.allowed_file_types[0] === 'custom') {
        return this.appParameters.file_upload.allowed_file_extensions;
      }
      return this.appParameters.file_upload.allowed_file_types.map(type => this.fileTypes[type]).join(',');
    },
    // 获取应用参数
    async getAppParameters() {
      try {
        // 使用POST请求替代GET请求
        const response = await fetch(this.apiUrl + '/parameters', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.api_key}`
          },

        });
        const data = await response.json();
        this.appParameters = data;

      } catch (error) {
        console.error('获取应用参数失败:', error);
      }
    },
    toggleThinking(index) {
      if (this.messages[index] && this.messages[index].thinking) {
        this.messages[index].showThinking = !this.messages[index].showThinking
        this.$nextTick(() => {
          if (this.messages[index].showThinking) {
            this.scrollToBottom()
          }
        })
      }
    },
    generateUserId() {
      // 从localStorage获取用户ID，如果没有则生成新的
      let userId = localStorage.getItem('chatUserId')
      if (!userId) {
        userId = 'user_' + Math.random().toString(36).substr(2, 9)
        localStorage.setItem('chatUserId', userId)
      }
      return userId
    },
    clearChat() {
      this.$Modal.confirm({
        title: '确认清除',
        content: '确定要清除所有聊天记录吗？',
        onOk: () => {
          this.messages = []
          this.conversation_id = ''
          localStorage.setItem('chatUserId', '')
          this.$Message.success('聊天记录已清除')
        }
      })
    },
    toggleChat() {
      this.isExpanded = !this.isExpanded

      if (this.isExpanded) {
        this.unreadCount = 0
        this.$nextTick(() => {
          this.scrollToBottom()
          if (this.$refs.messageInput) {
            this.$refs.messageInput.focus()
          }
        })
      }
    },
    closeChat() {
      this.isExpanded = false
    },
    toggleFullscreen() {
      this.isFullscreen = !this.isFullscreen

      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },
    handleWindowResize() {
      if (this.isExpanded && !this.isFullscreen) {
        this.applyWindowPosition()
      }
    },
    formatMessage(content) {
      if (!content) return '';

      // 处理 Echarts 图表配置
      content = this.processEchartsConfig(content);

      // 配置 marked 选项
      marked.setOptions({
        pedantic: false,
        gfm: true,
        mangle: false,
        headerIds: false
      });
      setTimeout(() => {
        this.initEchartsInstances()
      }, 300);
      return marked.parse(content);
    },
    extractThinking(text) {
      // 查找思考过程部分
      const thinkingRegex = /<details style="color:gray;background-color: #f8f8f8;padding: 8px;border-radius: 4px;" open>\s*<summary>\s*Thinking...\s*<\/summary>([\s\S]*?)<\/details>/g;

      let thinking = '';
      let cleanedText = text;

      // 提取所有思考过程
      const matches = text.matchAll(thinkingRegex);
      for (const match of matches) {
        if (match[1]) {
          thinking += match[1].trim() + '\n\n';
          // 不立即从文本中移除思考部分，而是标记它
          cleanedText = cleanedText.replace(match[0], '<!-- thinking-placeholder -->');
        }
      }

      return { thinking, cleanedText };
    },
    async fetchMessages() {
      try {
        // 使用POST请求替代GET请求
        const response = await fetch(this.apiUrl + `/conversations?user=${this.userId}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.api_key}`
          }
        })
        const res = await response.json();

        if (res.data.length) {
          this.conversation_id = res.data[0].id

          const historyMessages = await fetch(this.apiUrl + `/messages?user=${this.userId}&conversation_id=${this.conversation_id}`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${this.api_key}`
            }
          });
          const data = await historyMessages.json()
          this.hasMoreMessage = data.has_more
          // 如果无分页对话且有开场白
          if (!data.has_more && this.appParameters.opening_statement) {
            const systemMessage = {
              content: this.appParameters.opening_statement,
              thinking: '',
              showThinking: false,
              timestamp: new Date(),
              isUser: false,
              isComplete: true // 标记消息是否完成
            }
            this.messages.unshift(systemMessage)
          }
          data.data.forEach(msg => {

            // 添加用户消息
            this.messages.push(
              {
                content: msg.query,
                timestamp: dayjs(msg.created_at * 1000),
                isUser: true,
                files: msg.message_files.filter(item => item.belongs_to === 'user').map(file => {
                  return {
                    name: file.filename,
                    extension: file.mime_type.split('/')[1],
                    size: file.size
                  }
                }),
                id: msg.id
              }
            )

            // 添加AI消息
            this.messages.push({
              content: msg.answer,
              thinking: '',
              showThinking: false,
              timestamp: dayjs(msg.created_at * 1000),
              isUser: false,
              isComplete: true, // 标记消息是否完成
              id: msg.id
            })
          });
        } else {
          // 如果无分页对话且有开场白
          if (this.appParameters.opening_statement) {
            const systemMessage = {
              content: this.appParameters.opening_statement,
              thinking: '',
              showThinking: false,
              timestamp: new Date(),
              isUser: false,
              isComplete: true // 标记消息是否完成
            }
            this.messages.unshift(systemMessage)
          }
        }
      } catch (error) {
        console.error('获取消息失败:', error);
      }
    },
    findFileType(extension) {
      for (let key in this.fileTypes) {
        if (this.fileTypes[key].indexOf(extension) !== -1) {
          return key
        }
      }
      return 'document'
    },
    async sendMessage(event) {
      const messageText = this.newMessage.trim()
      if (!messageText) return

      // 添加用户消息到列表
      const userMessage = {
        content: messageText,
        timestamp: new Date(),
        isUser: true,
        files: this.files
      }
      this.messages.push(userMessage)
      this.newMessage = ''
      this.scrollToBottom()

      // 显示"正在输入"状态
      this.isTyping = true

      try {
        // 准备请求参数
        const requestData = {
          inputs: {
            post_name: JSON.parse(localStorage.getItem('userData')).post_name
          },
          query: messageText,
          response_mode: 'streaming',
          conversation_id: this.conversation_id,
          user: this.userId, // 使用用户ID
          files: this.files.map(file => {
            return {
              transfer_method: 'local_file',
              type: this.findFileType(file.extension),
              upload_file_id: file.id,
              url: ''
            }
          })
        }
        this.files = []
        // 发送消息到API并处理流式响应
        const response = await fetch(this.apiUrl + '/chat-messages', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.api_key}`
          },
          body: JSON.stringify(requestData)
        })
        console.log(response.message);

        if (response.status === 400) {
          this.$Message.error('213')
          return
        }

        // 创建一个临时的系统消息对象
        const systemMessage = {
          content: '',
          thinking: '',
          showThinking: false,
          timestamp: new Date(),
          isUser: false,
          isComplete: false // 标记消息是否完成
        }
        this.messages.push(systemMessage)

        // 处理流式响应
        const reader = response.body.getReader()
        const decoder = new TextDecoder()
        let partialLine = ''
        let fullResponse = '' // 用于累积完整响应
        let messageId = null // 用于跟踪消息ID
        let hasThinking = false // 标记是否有思考过程

        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          // 解码二进制数据
          const text = decoder.decode(value)
          const lines = (partialLine + text).split('\n')
          partialLine = lines.pop() || ''

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const jsonStr = line.substring(6) // 移除 "data: " 前缀
                const data = JSON.parse(jsonStr)

                // 新对话时，赋值id
                if (!this.conversation_id) {
                  this.conversation_id = data.conversation_id
                }
                // 检查是否是同一条消息的更新
                if (messageId === null) {
                  messageId = data.message_id
                } else if (data.message_id && data.message_id !== messageId) {
                  console.log('收到新消息ID，忽略:', data.message_id)
                  continue // 忽略不同ID的消息
                }

                // 更新系统消息内容
                if (data.event === 'message' && data.answer) {
                  // 检测是否包含思考过程
                  if (data.answer.includes('<summary>Thinking...</summary>')) {
                    hasThinking = true
                  }

                  // 累积到完整响应中
                  fullResponse += data.answer;

                  // 提取思考过程和正文
                  const { thinking, cleanedText } = this.extractThinking(fullResponse);

                  // 更新当前消息
                  systemMessage.thinking = thinking;

                  // 如果有思考过程，在思考过程完成前显示占位内容
                  if (hasThinking && cleanedText.includes('<!-- thinking-placeholder -->')) {
                    // 思考过程仍在进行，显示打字指示器作为内容
                    systemMessage.content = '正在思考...';
                    systemMessage.isComplete = false;
                  } else {
                    // 思考过程已完成或没有思考过程，显示实际内容
                    systemMessage.content = cleanedText.replace(/<!-- thinking-placeholder -->/g, '');
                    systemMessage.isComplete = true;
                  }

                  this.scrollToBottom();
                }
              } catch (e) {
                console.error('解析流式响应失败:', e)
              }
            }
          }
        }

        // 确保最终消息是完整的
        if (!systemMessage.isComplete) {
          const { thinking, cleanedText } = this.extractThinking(fullResponse);
          systemMessage.thinking = thinking;
          systemMessage.content = cleanedText.replace(/<!-- thinking-placeholder -->/g, '');
          systemMessage.isComplete = true;
        }

      } catch (error) {
        console.error('发送消息失败:', error)
        // 显示错误消息
        this.messages.push({
          content: '消息发送失败，请稍后重试。',
          timestamp: new Date(),
          isUser: false
        })
      } finally {
        this.isTyping = false
        this.scrollToBottom()
        this.initEchartsInstances() // 初始化新消息中的图表
      }
    },
    scrollToBottom() {
      if (this.$refs.messageContainer) {
        this.$refs.messageContainer.scrollTop = this.$refs.messageContainer.scrollHeight
      }
    },
    formatTime(timestamp) {
      const date = new Date(timestamp)
      const hours = date.getHours().toString().padStart(2, '0')
      const minutes = date.getMinutes().toString().padStart(2, '0')
      return `${hours}:${minutes}`
    },
    // 添加手动刷新方法
    refreshMessages() {
      this.fetchMessages()
    },
    handleKeyDown(event) {
      // 如果按下Shift+Enter，发送消息
      if (event.key === 'Enter' && event.shiftKey) {
        event.preventDefault() // 阻止默认行为
        this.sendMessage()
        this.$nextTick(() => {
          this.newMessage = ''
        })
      }
    },
    deleteFile(index) {
      this.$Modal.confirm({
        title: '确认删除',
        content: '确定要删除这个文件吗？',
        onOk: () => {
          this.files.splice(index, 1);
          this.$Message.success('文件已删除');
        }
      });
    },
    handleScroll(e) {
      const container = e.target;

      // 当滚动到顶部时触发
      if (container.scrollTop === 0 && !this.isLoadingMore && this.hasMoreMessage) {
        this.loadMoreMessages(container.scrollHeight);
      }
    },
    async loadMoreMessages(containerHeight) {
      if (this.isLoadingMore) return;

      this.isLoadingMore = true;
      try {
        // 这里可以添加加载更多历史消息的逻辑
        // 例如：调用API获取更早的消息
        console.log(111);
        
        await this.fetchMoreMessages(containerHeight);
      } catch (error) {
        console.error('加载更多消息失败:', error);
        this.$Message.error('加载更多消息失败');
      } finally {
        this.isLoadingMore = false;
      }
    },
    async fetchMoreMessages(containerHeight) {
      // 这里实现获取更多历史消息的具体逻辑
      // 例如：根据当前最早消息的时间戳获取更早的消息
      // 示例代码：
      const earliestMessage = this.messages[0];
      if (!earliestMessage.id) {
        return
      }
      const response = await fetch(`${this.apiUrl}/messages?user=${this.userId}&conversation_id=${this.conversation_id}&first_id=${earliestMessage.id}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.api_key}`
        }
      });
      const data = await response.json();
      let moreMessage = []
      data.data.forEach(msg => {

        // 添加用户消息
        moreMessage.push(
          {
            content: msg.query,
            timestamp: dayjs(msg.created_at * 1000),
            isUser: true,
            files: msg.message_files.filter(item => item.belongs_to === 'user').map(file => {
              return {
                name: file.filename,
                extension: file.mime_type.split('/')[1],
                size: file.size
              }
            }),
            id: msg.id
          }
        )

        // 添加AI消息
        moreMessage.push({
          content: msg.answer,
          thinking: '',
          showThinking: false,
          timestamp: dayjs(msg.created_at * 1000),
          isUser: false,
          isComplete: true, // 标记消息是否完成
          id: msg.id
        })
      });
      this.messages = [...moreMessage, ...this.messages]
      this.hasMoreMessage = data.has_more
      // 如果无分页对话且有开场白
      if (!data.has_more && this.appParameters.opening_statement) {
        const systemMessage = {
          content: this.appParameters.opening_statement,
          thinking: '',
          showThinking: false,
          timestamp: new Date(),
          isUser: false,
          isComplete: true // 标记消息是否完成
        }
        this.messages.unshift(systemMessage)
      }
      this.$nextTick(() => {

        this.$refs.messageContainer.scrollTop = this.$refs.messageContainer.scrollHeight - containerHeight
      })
      // this.messages.unshift(...data.data);
    },
    // 新增处理 Echarts 图表配置的方法
    processEchartsConfig(content) {
      // 匹配 ```echarts ... ``` 格式的代码块
      const echartsRegex = /```echarts\s*([\s\S]*?)```/g;

      // 替换所有匹配的 echarts 代码块
      return content.replace(echartsRegex, (match, configStr) => {
        try {
          // 生成唯一ID
          const chartId = 'echarts-' + Math.random().toString(36).substr(2, 9);

          // 将配置字符串转换为对象（这里需要确保配置字符串是有效的 JSON）
          // 注意：这里假设配置是有效的 JSON 字符串，实际使用中可能需要更复杂的处理

          // 返回一个占位 div，稍后会初始化为图表
          return `<div class="echarts-container" data-config='${configStr}' id="${chartId}" style="width:100%;height:300px;"></div>`;
        } catch (e) {
          console.error('解析 Echarts 配置失败:', e);
          return match; // 如果解析失败，保留原始代码块
        }
      });
    },
    initEchartsInstances() {
      this.$nextTick(() => {
        // 查找所有 echarts 容器
        const containers = document.querySelectorAll('.echarts-container');
        containers.forEach(container => {
          try {
            // 获取配置字符串
            const configStr = container.getAttribute('data-config');
            // 尝试解析配置
            let config;
            try {
              // 尝试作为 JSON 解析
              config = JSON.parse(configStr);
            } catch (e) {
              // 如果不是有效的 JSON，尝试作为 JavaScript 对象解析
              // 注意：这种方式有安全风险，仅在可信环境中使用
              config = new Function('return ' + configStr)();
            }

            // 初始化图表
            const chart = echarts.init(container);
            chart.setOption({
              ...config, grid: {
                left: 20,
                right: 20,
                bottom: 20,
                containLabel: true
              }
            });

            // 保存实例以便后续可能的操作
            if (!this.echartsInstances) this.echartsInstances = [];
            this.echartsInstances.push(chart);

            // 添加窗口大小变化时的自适应
            window.addEventListener('resize', () => {
              chart.resize();
            });
          } catch (e) {
            console.error('初始化 Echarts 图表失败:', e);
            container.innerHTML = '<div class="echarts-error">图表加载失败</div>';
          }
        });
      });
    }
  }
}
</script>

<style scoped>
.chat-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
}

.chat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #4a90e2;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  position: relative;
  transition: transform 0.3s ease;
}

.chat-icon:hover {
  transform: scale(1.1);
}

.unread-badge {
  position: absolute;
  top: -5px;
  right: -5px;
}

.chat-window {
  width: 500px;
  height: calc(100% - 40px);
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: fixed;
  bottom: 0px;
  right: 0px;
  transition: all 0.3s ease;
}

.chat-fullscreen .chat-window {
  width: calc(100% - 240px);
  height: calc(100% - 240px);
  position: fixed;
  top: 120px;
  left: 120px;
  right: 120px;
  bottom: 120px;
  border-radius: 8px;
  z-index: 1001;
}

.chat-header {
  background-color: #4a90e2;
  color: white;
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-title {
  font-weight: bold;
  font-size: 16px;
  display: flex;
  align-items: center;
}

.chat-title i {
  margin-right: 6px;
}

.chat-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  /* 统一按钮间距 */
}

/* 统一按钮基础样式 */
.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
  height: 28px;
  padding: 0 10px;
}

.action-btn i {
  margin-right: 4px;
}

/* 清除按钮样式 */
.clear-btn {
  background-color: #4a90e2 !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

.clear-btn:hover {
  background-color: #3a80d2 !important;
}

/* 最小化按钮样式 */
.minimize-btn {
  background-color: #4a90e2 !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  padding: 0 8px !important;
}

.minimize-btn:hover {
  background-color: #3a80d2 !important;
}

.minimize-btn i {
  margin-right: 0;
}

/* 最大化按钮样式 */
.maximize-btn {
  background-color: #4a90e2 !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  padding: 0 8px !important;
}

.maximize-btn:hover {
  background-color: #3a80d2 !important;
}

.maximize-btn i {
  margin-right: 0;
}

/* 关闭按钮样式 */
.close-btn {
  background-color: #e81123 !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  padding: 0 8px !important;
}

.close-btn:hover {
  background-color: #f1707a !important;
}

.close-btn i {
  margin-right: 0;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
  background-color: #f5f8fb;
}

.message {
  display: flex;
  margin-bottom: 16px;
  align-items: flex-start;
}

.system-message {
  flex-direction: row;
}

.user-message {
  flex-direction: row-reverse;
}

.message-avatar {
  margin: 0 8px;
  background-color: #2d8cf0 !important;
}

.user-message .message-avatar {
  background-color: #19be6b !important;
}

.message-content {
  max-width: 70%;
  display: flex;
  flex-direction: column;
}

.system-message .message-content {
  align-items: flex-start;
}

.user-message .message-content {
  align-items: flex-end;
}

.message-text {
  padding: 10px 14px;
  border-radius: 4px;
  margin-bottom: 4px;

  word-break: break-word;
  transition: opacity 0.3s ease;
  max-width: 100%;
}

.system-message .message-text {
  background-color: white;
  color: #515a6e;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1);
}

.user-message .message-text {
  background-color: #2d8cf0;
  color: white;
}

.message-time {
  font-size: 12px;
  color: #808695;
  margin-top: 4px;
  display: flex;
  align-items: center;
}

.message-time i {
  margin-right: 4px;
}

.thinking-section {
  width: 100%;
  margin-top: 8px;
  border-radius: 4px;
  overflow: hidden;
  background-color: #f0f5ff;
  border: 1px solid #d0e0ff;
  transition: all 0.3s ease;
}

.thinking-header {
  padding: 8px 12px;
  background-color: #e0eaff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  font-size: 13px;
  color: #4a6baf;
  font-weight: 500;
  transition: background-color 0.2s;
}

.thinking-header:hover {
  background-color: #d5e2ff;
}

.thinking-header span {
  display: flex;
  align-items: center;
}

.thinking-header span i {
  margin-right: 6px;
}

.thinking-content {
  padding: 12px;
  font-size: 13px;
  line-height: 1.5;
  color: #515a6e;
  background-color: #f8faff;
  white-space: pre-wrap;
  overflow-x: auto;
}

.chat-cotainer {
  padding: 12px 16px;
  background-color: white;
  border-top: 1px solid #e8eaec;
}

.chat-input-container {
  display: flex;
  align-items: flex-end;
}

.input-wrapper {
  flex: 1;
  position: relative;
  margin-right: 10px;
}

.file-list {
  display: flex;
  /* flex-direction: column; */
  gap: 5px;
  margin-bottom: 8px;
}

.message-content .file-list {
  background-color: #fff;
}

.file-item {
  width: 144px;
  height: 68px;
  border-radius: 8px;
  border: 1px solid #10182814;
  padding: 8px;
  font-size: 12px;
  position: relative;
}

.file-item:hover .file-delete {
  display: block;
}

.file-delete {
  display: none;
  position: absolute;
  top: -8px;
  right: -8px;
  cursor: pointer;
}

.file-name {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  height: 32px;
  margin-bottom: 4px;
  word-break: break-all;
  line-height: 16px;
  max-height: 32px;
}

.chat-input {
  border-radius: 4px;
}

.attachment-btn {
  position: absolute;
  bottom: 5px;
  right: 5px;
  /* bottom: 5px; */
  color: #808695 !important;
  padding: 0 !important;
  margin: 0 !important;
  z-index: 10;
  cursor: pointer;
}

.attachment-btn:hover {
  color: #2d8cf0 !important;
}

.attachment-btn.disabled {
  color: #808695 !important;
  cursor: not-allowed;
}

.send-button {
  display: flex;
  align-items: center;
}

.send-button i {
  margin-right: 6px;
}

.typing-indicator {
  display: flex;
  padding: 10px 14px;
  background-color: white;
  border-radius: 18px;
  width: fit-content;
  margin-bottom: 15px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  align-items: center;
}

.typing-indicator span {
  margin-left: 8px;
  font-size: 13px;
  color: #515a6e;
}

.spin-icon-loading {
  animation: ani-spin 1s linear infinite;
}

@keyframes ani-spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@media (max-width: 768px) {
  .chat-fullscreen .chat-window {
    width: calc(100% - 40px);
    height: calc(100% - 40px);
    top: 20px;
    left: 20px;
    right: 20px;
    bottom: 20px;
  }
}

@media (max-width: 480px) {
  .chat-window {
    width: 100%;
    height: 100%;
    position: fixed;
    bottom: 0;
    right: 0;
    border-radius: 0;
  }

  .chat-fullscreen .chat-window {
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 0;
  }

  .chat-container {
    bottom: 10px;
    right: 10px;
  }
}
</style>

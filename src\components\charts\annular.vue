<template>
  <div ref="dom" class="charts chart-pie"></div>
</template>

<script>
import echarts from 'echarts'
import tdTheme from './theme.json'
import { on, off } from '@/libs/tools'
echarts.registerTheme('tdTheme', tdTheme)
export default {
  name: 'Chart<PERSON>ie',
  props: {
    value: Array,
    text: String,
    subtext: String,
    legendShow: {
      type: Boolean,
      default: true
    },
    radius: {
      type: Number,
      default: 60
    },
    center: {
      type: Array,
      default: () => {
        return ['50%', '60%']
      }
    },
    color: {
      type: Array,
      default: () => {
        return ['#ff7f7f', '#ff7fbf', '#ff7fff', '#bf7fff', '#7f7fff', '#7fbfff', '#7fffff', '#7fffbf', '#7fff7f', '#bfff7f', '#DFDFDF']
      }
    },
    legend: {
      type: Boolean,
      default: true
    },
    legendPosition: {
      type: String,
      default: 'top'
    },
    legendType: {
      type: String,
      default: 'plain'
    }
  },
  data () {
    return {
      dom: null
    }
  },
  methods: {
    chartInit () {
      this.$nextTick(() => {
        let legend = this.value.map(_ => _.name)
        let option = {
          title: {
            text: this.value.length > 0 ? this.text : '暂无数据',
            subtext: this.subtext,
            x: this.value.length > 0 ? 'center' : '30%',
            top: this.value.length > 0 ? this.center[0] : '40%'
          },
          tooltip: {
            show: this.legendShow,
            trigger: 'item',
            formatter: '{b}:{c}人（{d}%）'
          },
          legend: {
            show: this.legend, // this.legendShow,
            data: legend,
            type: this.legendType
          },
          color: this.color,
          series: [
            {
              type: 'pie',
              radius: ['55%', '75%'],
              center: this.center, // ['50%', '60%'],
              data: this.value,
              itemStyle: {
                emphasis: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              },
              label: {
                align: 'left',
                formatter: '{d}%'
              }
            }
          ]
        }
        this.dom = echarts.init(this.$refs.dom, 'tdTheme')
        this.dom.setOption(option)
        on(window, 'resize', this.resize)
      })
    },
    resize () {
      this.dom.resize()
    }
  },
  mounted () {
    this.chartInit()
  },
  beforeDestroy () {
    off(window, 'resize', this.resize)
  },
  watch: {
    value: {
      handler (n, o) {
        this.chartInit()
      },
      deep: true,
      immediate: true
    }
  }
}
</script>

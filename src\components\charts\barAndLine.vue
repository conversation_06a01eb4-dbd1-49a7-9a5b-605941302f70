<template>
  <div ref="dom" class="charts chart-bar"></div>
</template>

<script>
import echarts from 'echarts'
import tdTheme from './theme.json'
import { on, off } from '@/libs/tools'
echarts.registerTheme('tdTheme', tdTheme)
export default {
  name: 'ChartBarAndLine',
  props: {
    value: Object,
    text: String
  },
  data () {
    return {
      dom: null
    }
  },
  methods: {
    resize () {
      this.dom.resize()
    },
    initChart () {
      this.$nextTick(() => {
        let option = {
          title: {
            text: this.text,
            textStyle: {
              color: '#2B304C',
              fontSize: '16'
            }
          },
          grid: {
            left: 70,
            bottom: 25
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross'
            },
            crossStyle: {
              color: '#999'
            }
          },
          barMaxWidth: 20,
          // toolbox: {
          //   feature: {
          //     dataViem: {
          //       show: true,
          //       readOnly: false
          //     },
          //     magicType: {
          //       show: true,
          //       type: ['bar', 'line']
          //     },
          //     restore: {
          //       show: true
          //     }
          //   }
          // },
          legend: {
            show: this.value.legend.length > 1,
            icon: 'roundRect',
            data: this.value.legend
          },
          xAxis: {
            type: 'category',
            splitLine: {
              show: false
            },
            axisPointer: {
              type: 'shadow'
            },
            data: this.value.xAxis
          },
          yAxis: [],
          series: []
        }
        if (this.value.legend.length > 1) {
          this.value.seriesData.forEach((item, idx) => {
            option.series.push({
              name: this.value.legend[idx],
              data: item.data,
              type: item.type,
              yAxisIndex: this.value.yAxis !== undefined && this.value.yAxis.length > 1 && idx > 0 ? 1 : 0,
              symbol: item.symbol ? item.symbol : '',
              smooth: item.smooth ? item.smooth : '',
              lineStyle: {
                width: 3
              }
            })
          })
        } else {
          option.series.push({
            name: this.value.legend[0],
            data: this.value.data,
            type: this.value.type,
            lineStyle: {
              width: 3
            }
          })
        }
        if (this.value.yAxis !== undefined && this.value.yAxis.length > 1) {
          this.value.yAxis.forEach(item => {
            option.yAxis.push({
              name: item.name,
              type: 'value',
              splitNumber: 4,
              axisLine: {
                show: false
              },
              axisTick: {
                show: false
              },
              nameTextStyle: {
                color: '#4A4A4A',
                fontWeight: '600',
                align: 'right'
              }
            })
          })
        } else {
          option.yAxis.push({
            name: this.unit,
            type: 'value',
            splitNumber: 4,
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            nameTextStyle: {
              color: '#4A4A4A',
              fontWeight: '600',
              align: 'right'
            }
          })
        }
        this.dom = echarts.init(this.$refs.dom, 'tdTheme')
        this.dom.setOption(option)
        on(window, 'resize', this.resize)
      })
    }
  },
  mounted () {
    this.initChart()
  },
  beforeDestroy () {
    off(window, 'resize', this.resize)
  },
  watch: {
    value: {
      handler (n, o) {
        this.initChart()
      },
      deep: true,
      immediate: true
    }
  }
}
</script>

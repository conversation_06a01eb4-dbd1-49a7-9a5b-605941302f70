<template>
  <Form ref="loginForm" label-position="top" :model="form" :rules="rules" @keydown.enter.native="handleSubmit">
    <FormItem prop="userName" label="用户">
      <Input class="input-style" v-model="form.userName" placeholder="请输入用户名">
      </Input>
    </FormItem>
    <FormItem prop="password" label="密码">
      <Input class="input-style" type="password" v-model="form.password" placeholder="请输入密码">
      </Input>
    </FormItem>
    <FormItem class="submit-btn">
      <Button @click="handleSubmit" type="primary" long>登录</Button>
    </FormItem>
    <a class="forget_pwd" @click="forgetPwd">忘记密码</a>
  </Form>
</template>
<script>
export default {
  name: 'LoginForm',
  props: {
    userNameRules: {
      type: Array,
      default: () => {
        return [
          { required: true, message: '账号不能为空', trigger: 'blur' }
        ]
      }
    },
    passwordRules: {
      type: Array,
      default: () => {
        return [
          { required: true, message: '密码不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  data () {
    return {
      form: {
        userName: '',
        password: ''
      }
    }
  },
  computed: {
    rules () {
      return {
        userName: this.userNameRules,
        password: this.passwordRules
      }
    }
  },
  methods: {
    handleSubmit () {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.$emit('on-success-valid', {
            userName: this.form.userName,
            password: this.$md5(this.form.password)
            // way: '1' // 请求方式（PC端1，PDA端2）
          })
        }
      })
    },
    forgetPwd () {
      this.$emit('on-forget-pwd', true)
    }
  }
}
</script>
<style>
  .input-style .ivu-input {
    background: #F1F3F7;
    border: none;
  }
  .form-con .ivu-form .ivu-form-item-label {
    color: #252529 !important;
    font-weight: bold;
  }
  .submit-btn {
    margin-top: 50px;
  }
  .forget_pwd {
    display: block;
    margin-top: -20px;
    text-align: center;
    cursor: pointer;
  }
</style>

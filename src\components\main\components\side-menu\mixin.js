import CommonIcon from '_c/common-icon'
import { showTitle } from '@/libs/util'
export default {
  components: {
    CommonIcon
  },
  methods: {
    showTitle (item) {
      return showTitle(item, this)
    },
    showChildren (item) {
      return item.children && (item.children.length > 1 || (item.meta && item.meta.showAlways))
    },
    getNameOrHref (item, children0) {
      return (item.href.indexOf('http://') > -1 || item.href.indexOf('https://') > -1) ? `isTurnByHref_${item.href}` : (children0 ? item.children[0].href : item.href)
      // return (item.href.indexOf('http://') > -1 || item.href.indexOf('https://') > -1) ? `externalLink` : (children0 ? item.children[0].href : item.href)
    }
  }
}

<template>
  <Modal v-model="modalData.modal" :mask-closable="false" :width="450" :title="modalData.title" @on-visible-change="modalVisible">
    <Form ref="loginForm" label-position="top" :model="form" :rules="rules" @keydown.enter.native="handleSubmit">
      <FormItem prop="curPwd" label="当前密码">
        <Input class="input-style" type="password" v-model="form.curPwd" placeholder="请输入当前密码">
        </Input>
      </FormItem>
      <FormItem prop="newPwd" label="新密码">
        <Input class="input-style" type="password" v-model="form.newPwd" placeholder="请输入新密码">
        </Input>
      </FormItem>
      <FormItem prop="newAPwd" label="确认密码">
        <Input class="input-style" type="password" v-model="form.newAPwd" placeholder="请确认新密码">
        </Input>
      </FormItem>
    </Form>
    <div slot="footer">
      <Button @click="handleCancel">取消</Button>
      <Button @click="handleSubmit" type="primary">确认</Button>
    </div>
  </Modal>
</template>
<script>
import {
  updatePwd
} from '@/api/user'

export default ({
  props: {
    modalData: Object
  },
  data () {
    return {
      form: {
        curPwd: '',
        newPwd: '',
        newAPwd: ''
      },
      rules: {
        curPwd: [{ required: true, message: '原始密码不能为空', trigger: 'blur' }],
        newPwd: [{ required: true, message: '新密码不能为空', trigger: 'blur' }],
        newAPwd: [{ required: true, message: '确认密码不能为空', trigger: 'blur' }]
      }
    }
  },
  methods: {
    handleSubmit () {
      this.$refs['loginForm'].validate(valid => {
        if (valid) {
          if (this.form.newPwd !== this.form.newAPwd) { // 两个密码不一致提示
            this.$Message.warning('两次输入密码不一致')
          } else {
            let _userData = JSON.parse(localStorage.getItem('userData'))
            let _param = {
              unified_account_id: _userData.unified_account_id,
              old_password: this.form.curPwd,
              new_password: this.form.newPwd
            }
            updatePwd(_param).then(res => {
              if (res.data.Code === 10000) {
                this.$Message.success(res.data.Message)
                setTimeout(() => {
                  this.$emit('changeBack')
                }, 1000)
              } else {
                this.$Message.warning(res.data.Message)
              }
            })
          }
        } else {
          this.$Message.error('请确认信息完整！')
        }
      })
    },
    handleCancel () {
      this.modalData.modal = false
      this.form.curPwd = ''
      this.form.newPwd = ''
      this.form.newAPwd = ''
      this.$refs['loginForm'].resetFields()
    },
    modalVisible (val) {
      if (!val) this.handleCancel()
    }
  }
})
</script>

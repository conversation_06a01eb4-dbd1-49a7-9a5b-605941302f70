// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from 'vue'
import App from './App'
import router from './router'
import store from './store'
import ViewUI from 'view-design'
import config from '@/config'
import md5 from './plugin/md5'
import installPlugin from '@/plugin'
import 'babel-polyfill'
import Es6Promise from 'es6-promise'
import * as WPS from './libs/jwps.es6'
import './index.less'
import '@/assets/icons/iconfont.css'
// import '@/assets/iconfont/iconfont.css'
import 'v-org-tree/dist/v-org-tree.css'
import 'view-design/dist/styles/iview.css';
import fullscreen from 'vue-fullscreen' // 全屏显示
import * as filters from './filters'
import VueSignaturePad from 'vue-signature-pad'
import ChatBot from '_c/ChatBot/index.vue'

Vue.prototype.wps = WPS
Vue.prototype.UNLOGIN_PAGE_LIST = ['voyagePlanDetail', 'contractMobile', 'xt_forms'] // 无需登录校验页面
Vue.use(ViewUI, {
  transfer: true
})
Vue.component('ChatBot', ChatBot)
Vue.use(md5)
Vue.use(fullscreen)
Vue.use(VueSignaturePad)

require('es6-promise').polyfill()
Es6Promise.polyfill()

// 全局过滤器
Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key])
})

// 下载指令
Vue.directive('down', {
  inserted: ((el, binding) => {
    el.addEventListener('click', () => {
      let link = document.createElement('a')
      let url = binding.value
      fetch(url).then(res => res.blob()).then(blob => {
        link.href = URL.createObjectURL(blob)
        link.download = ''
        document.body.appendChild(link)
        link.click()
      })
    })
  })
})
/**
 * @description 注册admin内置插件
 */
installPlugin(Vue)
/**
 * @description 生产环境关掉提示
 */
Vue.config.productionTip = false
/**
 * @description 全局注册应用配置
 */
Vue.prototype.$config = config

/* eslint-disable no-new */
new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})

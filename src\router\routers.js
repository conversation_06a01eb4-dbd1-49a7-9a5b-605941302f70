/* jshint esversion: 6 */
import Main from '@/components/main'
import NoMenuMain from '@/components/main/noMenuMain'
import '@/assets/menu_icon/iconfont.css'

/**
 * 这里可以把一些不需要管的路由留下，比如首页，登录，401 ，405。404别留这里，要最后一个动态加载到最末尾
 */
export default [
  {
    path: '/login',
    name: 'login',
    meta: {
      title: '登录',
      hideInMenu: true
    },
    component: () => import('@/view/login/login.vue')
  },
  {
    path: '/unsafeAct',
    name: 'unsafeAct',
    meta: {
      title: '不安全行為',
      hideInMenu: true
    },
    component: () => import('@/view/single-page/unsafeAct/index.vue')
  },
  {
    path: '/xt_forms',
    name: 'xt_forms',
    meta: {
      title: '对外申请单',
      hideInMenu: true
    },
    component: () => import('@/view/single-page/xt_forms/index.vue')
  },
  {
    path: '/viewFile',
    name: 'viewFile',
    meta: {
      title: '文件预览编辑',
      hideInMenu: true
    },
    component: () => import('@/view/wpsView/viewFile.vue')
  },
  {
    path: '/',
    name: '_home',
    redirect: '/home',
    component: Main,
    meta: {
      hideInMenu: false,
      notCache: true
    },
    children: [{
      path: '/home',
      name: 'home',
      meta: {
        hideInMenu: false,
        title: '首页',
        notCache: true,
        icon: 'md-apps'
      },
      component: () => import('@/view/single-page/home')
    }]
  },
  {
    path: '/',
    name: 'agent',
    component: NoMenuMain,
    meta: {
      icon: 'md-medal',
      title: '智能体'
    },
    children: [
      {
        path: 'agent',
        name: 'agent',
        meta: {
          icon: 'md-boat',
          title: '智能体'
        },
        component: () => import('@/view/agent/agent')
      },
      {
        path: '/agentChat',
        name: 'agentChat',
        meta: {
          title: '智能体聊天',
          hideInMenu: true
        },
        component: () => import('@/view/agent/agentChat.vue')
      },
    ]
  },
  // {
  //   path: '/agent',
  //   name: 'agent',
  //   meta: {
  //     title: '智能体',
  //     hideInMenu: true
  //   },
  //   component: () => import ('@/view/agent/agent.vue')
  // },
  // {
  //   path: '/agentChat',
  //   name: 'agentChat',
  //   meta: {
  //     title: '智能体聊天',
  //     hideInMenu: true
  //   },
  //   component: () => import ('@/view/agent/agentChat.vue')
  // },
  // 采购数据分析
  {
    path: '/',
    name: 'materialPrice',
    component: Main,
    meta: {
      icon: 'ios-speedometer',
      title: '物料备件价格'
    },
    children: [
      {
        path: 'materialSinglePrice',
        name: 'materialSinglePrice',
        meta: {
          icon: 'md-podium',
          title: '单种价格走势'
        },
        component: () => import('@/view/materialPurchaseSystem/materialSinglePrice/index.vue')
      },
      {
        path: 'materialTotalPrice',
        name: 'materialTotalPrice',
        meta: {
          icon: 'md-stats',
          title: '总金额分析'
        },
        component: () => import('@/view/materialPurchaseSystem/materialTotalPrice/index.vue')
      },
      {
        path: 'compareAnalysis',
        name: 'compareAnalysis',
        meta: {
          icon: 'md-swap',
          title: '对比分析'
        },
        component: () => import('@/view/materialPurchaseSystem/compareAnalysis/index')
      },
      {
        path: 'oilPrice',
        name: 'oilPrice',
        meta: {
          icon: 'md-water',
          title: '油料分析'
        },
        component: () => import('@/view/materialPurchaseSystem/oilPrice/index.vue')
      }
    ]
  },
  {
    path: '/',
    name: 'materialNum',
    component: Main,
    meta: {
      icon: 'logo-instagram',
      title: '物料备件数量'
    },
    children: [
      {
        path: 'materialSingleNum',
        name: 'materialSingleNum',
        meta: {
          icon: 'md-list-box',
          title: '单种采购量'
        },
        component: () => import('@/view/materialPurchaseSystem/materialSingleNum/index.vue')
      },
      {
        path: 'materialShipNum',
        name: 'materialShipNum',
        meta: {
          icon: 'md-boat',
          title: '船舶采购量'
        },
        component: () => import('@/view/materialPurchaseSystem/materialShipNum/index.vue')
      }
    ]
  },
  {
    path: '/',
    name: 'configure',
    component: Main,
    meta: {
      icon: 'md-cog',
      title: '设置'
    },
    children: [
      {
        path: 'dataConfigure',
        name: 'dataConfigure',
        meta: {
          icon: 'md-lock',
          title: '数据配置'
        },
        component: () => import('@/view/materialPurchaseSystem/dataConfigure/index')
      },
      {
        path: 'trilateralOilPrice',
        name: 'trilateralOilPrice',
        meta: {
          icon: 'md-funnel',
          title: '三方价格'
        },
        component: () => import('@/view/materialPurchaseSystem/trilateralOilPrice/index')
      },
      {
        path: 'areaConfigure',
        name: 'areaConfigure',
        meta: {
          icon: 'ios-pin',
          title: '区域配置'
        },
        component: () => import('@/view/materialPurchaseSystem/areaConfigure/index')
      }
    ]
  },
  // 船员大数据
  {
    path: '/',
    name: 'shipperOwnerManage',
    component: Main,
    meta: {
      icon: 'md-contacts',
      title: '船员大数据'
    },
    children: [
      {
        path: 'shipperOwner',
        name: 'shipperOwner',
        meta: {
          icon: 'md-contacts',
          title: '船员列表'
        },
        component: () => import('@/view/shipperOwner/index/index.vue')
      },
      {
        path: 'shipperOwnerDetail/:id',
        name: 'shipperOwnerDetail',
        meta: {
          icon: 'md-contacts',
          title: '船员大数据详情',
          hideInMenu: true
        },
        component: () => import('@/view/shipperOwner/index/shipperOwnerDetail.vue')
      },
      {
        path: 'crewSchedule',
        name: 'crewSchedule',
        meta: {
          icon: 'md-filing',
          title: '船员排期'
        },
        component: () => import('@/view/shipperOwner/crewSchedule/index.vue')
      }
    ]
  },
  {
    path: '/',
    name: 'shipperOwnerManage',
    component: Main,
    meta: {
      icon: 'md-bookmarks',
      title: '船员合同'
    },
    children: [
      {
        path: 'crewContract',
        name: 'crewContract',
        meta: {
          icon: 'ios-book',
          title: '合同列表'
        },
        component: () => import('@/view/shipperOwner/contract/index.vue')
      },
      {
        path: 'downContract',
        name: 'downContract',
        meta: {
          icon: 'md-cloud-download',
          title: '合同下载'
        },
        component: () => import('@/view/shipperOwner/contract/downContract/index.vue')
      },
      {
        path: 'alarmContract',
        name: 'alarmContract',
        meta: {
          icon: 'md-notifications',
          title: '合同预警'
        },
        component: () => import('@/view/shipperOwner/contract/alarmContract/index.vue')
      },
      {
        path: 'contractMobile',
        name: 'contractMobile',
        meta: {
          icon: 'md-bookmark',
          title: '合同信息',
          hideInMenu: true
        },
        component: () => import('@/view/shipperOwner/contract/contract_mobile.vue')
      }
    ]
  },
  // BI数据看板
  {
    path: '/',
    name: 'biShow',
    component: Main,
    meta: {
      icon: 'md-medal',
      title: 'BI数据看板'
    },
    children: [

    ]
  },
  // 绩效管理
  {
    path: '/',
    name: 'performmance',
    component: Main,
    meta: {
      icon: 'md-medal',
      title: '绩效管理'
    },
    children: [
      {
        path: 'performanceList',
        name: 'performanceList',
        meta: {
          icon: 'md-options',
          title: '绩效列表'
        },
        component: () => import('@/view/performance/index.vue')
      },
      {
        path: 'performanceApprove',
        name: 'performanceApprove',
        meta: {
          icon: 'md-paper-plane',
          title: '绩效审批'
        },
        component: () => import('@/view/performance/approve.vue')
      },
      {
        path: 'performanceStatics',
        name: 'performanceStatics',
        meta: {
          icon: 'ios-analytics',
          title: '绩效统计'
        },
        component: () => import('@/view/performance/statics.vue')
      },
      {
        path: 'performanceProfile',
        name: 'performanceProfile',
        meta: {
          icon: 'ios-folder-open',
          title: '全员绩效'
        },
        component: () => import('@/view/jurisdictionManage/performanceProfile')
      },
      {
        path: 'performanceDetail/:id',
        name: 'performanceDetail',
        meta: {
          icon: 'md-paper-plane',
          title: '绩效详情',
          hideInMenu: true
        },
        component: () => import('@/view/performance/performanceModify.vue')
      }
    ]
  },
  // 船期助手
  {
    path: '/',
    name: 'voyagePlan',
    component: Main,
    meta: {
      icon: 'md-medal',
      title: '船期助手'
    },
    children: [
      {
        path: 'voyagePlan',
        name: 'voyagePlan',
        meta: {
          icon: 'md-boat',
          title: '月度计划'
        },
        component: () => import('@/view/voyagePlan/index.vue')
      },
      {
        path: 'voyagePlanDetail',
        name: 'voyagePlanDetail',
        meta: {
          icon: 'md-book',
          title: '月度计划详情',
          hideInMenu: true
        },
        component: () => import('@/view/voyagePlan/detail_mobile.vue')
      }
      // {
      //   path: 'voyagePlanDetail',
      //   name: 'voyagePlanDetail',
      //   meta: {
      //     icon: 'md-book',
      //     title: 'pdf测试',
      //     hideInMenu: true
      //   },
      //   component: () => import('@/view/voyagePlan/detail_pdf.vue')
      // }
    ]
  },
  // 瀛海系统
  {
    path: '/',
    name: 'erpSys',
    component: Main,
    meta: {
      icon: 'md-medal',
      title: '备件管理'
    },
    children: [
      {
        path: 'equipAdmin',
        name: 'equipAdmin',
        meta: {
          icon: 'md-build',
          title: '设备管理'
        },
        component: () => import('@/view/erpSys/equipManager/equipAdmin/index.vue')
      },
      {
        path: 'equipBook',
        name: 'equipBook',
        meta: {
          icon: 'md-paper',
          title: '备件手册'
        },
        component: () => import('@/view/erpSys/equipManager/equipBook/index.vue')
      }
    ]
  },
  {
    path: '/',
    name: 'materialManager',
    component: Main,
    meta: {
      icon: 'ios-speedometer',
      title: '物料管理'
    },
    children: [
      {
        path: 'companyMaterial',
        name: 'companyMaterial',
        meta: {
          icon: 'md-people',
          title: '公司物料'
        },
        component: () => import('@/view/erpSys/materialManager/index.vue')
      }
    ]
  },
  {
    path: '/',
    name: 'shipManage',
    component: Main,
    meta: {
      icon: 'logo-codepen',
      title: '船舶管理',
      access: ['erpSys']
    },
    children: [
      {
        path: 'shipCertificateManage',
        name: 'shipCertificateManage',
        meta: {
          icon: 'ios-ribbon',
          title: '船舶证书'
        },
        component: () => import('@/view/erpSys/certificate/index.vue')
      }
    ]
  },
  {
    path: '/',
    name: 'application',
    component: Main,
    meta: {
      icon: 'md-pint',
      title: '国际采购',
      access: ['erpSys']
    },
    children: [
      {
        path: 'shipApplication',
        name: 'shipApplication',
        meta: {
          icon: 'ios-pint',
          title: '船管申请单'
        },
        component: () => import('@/view/erpSys/shipApplication/index.vue')
      },
      {
        path: 'applicationSet',
        name: 'applicationSet',
        meta: {
          icon: 'logo-yen',
          title: '费用配置'
        },
        component: () => import('@/view/erpSys/shipApplication/set.vue')
      }
    ]
  },
  // {
  //   path: '/',
  //   name: 'oilManagement',
  //   component: Main,
  //   meta: {
  //     icon: 'ios-speedometer',
  //     title: '油品管理',
  //     access: ['erpSys']
  //   },
  //   children: [
  //     {
  //       path: 'oilManagement',
  //       name: 'oilManagement',
  //       meta: {
  //         icon: 'ios-water',
  //         title: '油品管理'
  //       },
  //       component: () => import('@/view/erpSys/oilManagement/index.vue')
  //     }
  //   ]
  // },
  // {
  //   path: '/',
  //   name: 'navigationMaterial',
  //   component: Main,
  //   meta: {
  //     icon: 'ios-speedometer',
  //     title: '航海资料',
  //     access: ['erpSys']
  //   },
  //   children: [
  //     {
  //       path: 'navigationMaterial',
  //       name: 'navigationMaterial',
  //       meta: {
  //         icon: 'md-people',
  //         title: '航海资料'
  //       },
  //       component: () => import('@/view/erpSys/navigationMaterial/index.vue')
  //     }
  //   ]
  // },
  {
    path: '/',
    name: 'shipperOwnerSet',
    component: Main,
    meta: {
      icon: 'md-cog',
      title: '配置'
    },
    children: [
      {
        path: 'shipperOwnerSet',
        name: 'shipperOwnerSet',
        meta: {
          icon: 'md-cog',
          title: '配置'
        },
        component: () => import('@/view/shipperOwner/set/index.vue')
      }
    ]
  },
  // 后台管理
  {
    path: '/',
    name: 'jurisdictionManage',
    component: Main,
    meta: {
      icon: 'ios-albums-outline',
      title: '组织架构'
    },
    children: [
      {
        path: 'userManagement',
        name: 'userManagement',
        meta: {
          icon: 'md-person',
          title: '用户管理'
        },
        component: () => import('@/view/jurisdictionManage/userManagement')
      },
      {
        path: 'departmentManagement',
        name: 'departmentManagement',
        meta: {
          icon: 'md-people',
          title: '部门管理'
        },
        component: () => import('@/view/jurisdictionManage/departmentManagement')
      },
      {
        path: 'moduleManagement',
        name: 'moduleManagement',
        meta: {
          icon: 'md-cog',
          title: '模块管理'
        },
        component: () => import('@/view/jurisdictionManage/moduleManagement')
      }
    ]
  },
  {
    path: '/',
    name: 'performanceSys',
    component: Main,
    meta: {
      icon: 'ios-color-filter',
      title: '绩效管理'
    },
    children: [
      {
        path: 'positionSystem',
        name: 'positionSystem',
        meta: {
          icon: 'md-swap',
          title: '绩效设置'
        },
        component: () => import('@/view/jurisdictionManage/positionSystem')
      },
      {
        path: 'performanceProfile',
        name: 'performanceProfile',
        meta: {
          icon: 'ios-folder-open',
          title: '绩效档案'
        },
        component: () => import('@/view/jurisdictionManage/performanceProfile')
      },
      {
        path: 'performanceProfileManage',
        name: 'performanceProfileManage',
        meta: {
          icon: 'ios-folder-open',
          title: '绩效档案'
        },
        component: () => import('@/view/jurisdictionManage/performanceProfile')
      },
      {
        path: 'performanceStatistic',
        name: 'performanceStatistic',
        meta: {
          icon: 'ios-analytics',
          title: '绩效统计'
        },
        component: () => import('@/view/jurisdictionManage/performanceStatistic')
      }
    ]
  },
  {
    path: '/',
    name: 'pushSet',
    component: Main,
    meta: {
      icon: 'ios-color-filter',
      title: '推送管理'
    },
    children: [
      {
        path: 'voyagePlanSet',
        name: 'voyagePlanSet',
        meta: {
          icon: 'md-swap',
          title: '月度计划推送'
        },
        component: () => import('@/view/pushSet/voyagePlanSet')
      }
    ]
  },
  // 考试系统
  {
    path: '/',
    name: 'examModule',
    component: Main,
    meta: {
      icon: 'ios-albums-outline',
      title: '考试模块'
    },
    children: [
      {
        path: 'memberManagement',
        name: 'memberManagement',
        meta: {
          icon: 'md-person-add',
          title: '成员管理'
        },
        component: () => import('@/view/examSystem/examModule/memberManagement')
      },
      {
        path: 'questionBankList',
        name: 'questionBankList',
        meta: {
          icon: 'md-list',
          title: '题库列表'
        },
        component: () => import('@/view/examSystem/examModule/questionBankList')
      },
      {
        path: 'questionBankClassify',
        name: 'questionBankClassify',
        meta: {
          icon: 'ios-folder-outline',
          title: '题库类别'
        },
        component: () => import('@/view/examSystem/examModule/questionBankClassify/index.vue')
      },
      {
        path: 'jobCategory',
        name: 'jobCategory',
        meta: {
          icon: 'md-person',
          title: '归属分类'
        },
        component: () => import('@/view/examSystem/examModule/jobCategory/index.vue')
      },
      {
        path: 'configExam',
        name: 'configExam',
        meta: {
          icon: 'md-paper',
          title: '考试配置'
        },
        component: () => import('@/view/examSystem/examModule/configExam/index.vue')
      }
    ]
  },
  {
    path: '/',
    name: 'trainingModule',
    component: Main,
    meta: {
      icon: 'md-book',
      title: '培训模块'
    },
    children: [
      {
        path: 'trainingTopics',
        name: 'trainingTopics',
        meta: {
          icon: 'ios-paper',
          title: '培训管理'
        },
        component: () => import('@/view/examSystem/trainingModule/trainingTopics')
      },
      {
        path: 'memberConfigure',
        name: 'memberConfigure',
        meta: {
          icon: 'md-people',
          title: '成员信息'
        },
        component: () => import('@/view/examSystem/trainingModule/memberManagement')
      },
      {
        path: 'memberStatistics',
        name: 'memberStatistics',
        meta: {
          icon: 'md-podium',
          title: '统计分析'
        },
        component: () => import('@/view/examSystem/trainingModule/memberStatistics')
      }
    ]
  },
  {
    path: '/',
    name: 'setConfigure',
    component: Main,
    meta: {
      icon: 'ios-settings',
      title: '设置'
    },
    children: [
      {
        path: 'evaluateTemplate',
        name: 'evaluateTemplate',
        meta: {
          icon: 'ios-create-outline',
          title: '评价模板'
        },
        component: () => import('@/view/examSystem/dataConfigure/evaluateTemplate')
      }
    ]
  },
  {
    path: '/',
    name: 'iframe',
    component: Main,
    meta: {
      icon: 'md-aperture',
      title: '外部内嵌页面'
    },
    children: [
      {
        path: 'externalLink',
        name: 'externalLink',
        meta: {
          icon: 'md-aperture',
          title: '内嵌页面'
        },
        component: () => import('@/view/single-page/iframe')
      }
    ]
  },
  {
    path: '/',
    name: 'exchange',
    component: Main,
    meta: {
      icon: 'md-aperture',
      title: '汇率'
    },
    children: [
      {
        path: 'exchangeRate',
        name: 'exchangeRate',
        meta: {
          icon: 'md-aperture',
          title: '汇率'
        },
        component: () => import('@/view/single-page/exchangeRate')
      }
    ]
  },
  {
    path: '/tabIframe',
    name: 'tabIframe',
    meta: {
      icon: 'md-analytic',
      title: '船型/航线BI统计',
      hideInMenu: true
    },
    component: () => import('@/view/single-page/tabIframe')
  },
  {
    path: '/401',
    name: 'error_401',
    meta: {
      hideInMenu: true
    },
    component: () => import('@/view/error-page/401.vue')
  },
  {
    path: '/500',
    name: 'error_500',
    meta: {
      hideInMenu: true
    },
    component: () => import('@/view/error-page/500.vue')
  }
]

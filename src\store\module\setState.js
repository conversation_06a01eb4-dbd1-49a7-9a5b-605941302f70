export default {
  state: {
    singlePriceParam: {}, // 单种价格走势入参
    useforParam: {}, // 总金额-用途分析入参
    warehousenameParam: {}, // 总金额-船舶分析入参
    dateAnalyseParam: {}, // 总金额-时间分析入参
    compareAnalyseParam: {}, // 对比分析入参
    monthAnalyseParam: {}, // 油料分析-月份分析入参
    dayAnalyseParam: {}, // 油料分析-日期分析入参
    areaAnalyseParam: {}, // 油料分析-区域分析入参
    monthMaterialParam: {}, // 单种采购量-月份分析入参
    dateMaterialParam: {}, // 单种采购量-日期分析入参
    shipMaterialParam: {}, // 船舶采购量入参
    performanceProfileParam: {} // 后台管理-绩效档案入参
  },
  mutations: {
    // 设置单种价格走势入参
    setSinglePriceParam (state, val) {
      state.singlePriceParam = val
    },
    // 设置用途分析入参
    setUseforParam (state, val) {
      state.useforParam = val
    },
    // 设置船舶分析入参
    setWarehousename (state, val) {
      state.warehousenameParam = val
    },
    // 设置时间分析入参
    setDateAnalyse (state, val) {
      state.dateAnalyseParam = val
    },
    // 设置对比分析入参
    setCompareAnalyse (state, val) {
      state.compareAnalyseParam = val
    },
    // 设置月份分析入参
    setMonthAnalyse (state, val) {
      state.monthAnalyseParam = val
    },
    // 设置日期分析入参
    setDayAnalyse (state, val) {
      state.dayAnalyseParam = val
    },
    // 设置区域分析入参
    setAreaAnalyse (state, val) {
      state.areaAnalyseParam = val
    },
    // 设置单种采购量-月份分析入参
    setMonthMaterial (state, val) {
      state.monthMaterialParam = val
    },
    // 设置单种采购量-日期分析入参
    setDateMaterial (state, val) {
      state.dateMaterialParam = val
    },
    // 设置船舶采购量入参
    setShipMaterial (state, val) {
      state.shipMaterialParam = val
    },
    // 设置姓名入参
    setPerformanceProfile (state, val) {
      state.performanceProfileParam = val
    }
  }
}

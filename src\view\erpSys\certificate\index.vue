<template>
  <div>
    <div class="statistic_area">
      <div>
        <Button v-if="isWarnShow" type="primary" size="small" icon="ios-undo" @click="backToBtnList">返回</Button>
        <Icon style="margin-left: 12px;" type="md-volume-down" size="16" />
        <span>
          超期预警：
          <span style="color: #e74c3c; font-weight: bold; cursor: pointer;" @click="showWarnMess('overdue')">{{ overdueNum }}</span>
          条
        </span>
        <span style="margin-left: 15px;">
          预到期预警：
          <span style="color: #ffa502; font-weight: bold; cursor: pointer;" @click="showWarnMess('warn')">{{ waringNum }}</span>
          条
        </span>
      </div>
    </div>
    <Spin size="large" fix v-if="spinShow">
      <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
      <div>{{ spinTxt }}</div>
    </Spin>
    <div>
      <Card>
        <div v-if="!isWarnShow" class="btn_area">
          <div class="move_back_btn">
            <Select v-model="cur_root" style="width: 120px; text-align: left;margin: 0 8px;" @on-select="shipSelect">
              <Option v-for="(item, idx) in shipList" :key="'directory' + idx" :value="item.file_directory_id">{{ item.name }}</Option>
              <Button style="width: 90%; margin-left: 5%; margin-top: 12px;" type="primary" long size="small" @click="addShip">新增</Button>
            </Select>
            <Button style="margin-left: -10px;" v-if="this.breadNameList.length > 1" type="text" icon="md-arrow-back" @click="backParent">返回</Button>
            <span v-if="breadNameList.length > 1" class="bread-span" v-for="(item, idx) in breadNameList" :key="'bread' + idx" :style="(breadNameList.length > 1 && idx < breadNameList.length - 1) ? 'color: #2d8cf0; cursor: pointer;' : ''" @click="breadClick(idx)">
              <Icon v-if="idx > 0" type="ios-arrow-forward"/>
              <h4>{{ item.name }}</h4>
            </span>
          </div>
          <Tooltip :content="isModify ? '保存' : '编辑'">
            <Button :type="isModify ? 'primary' : 'success'" @click="multiModify">
              <Icon :type="isModify ? 'md-done-all' : 'ios-brush-outline'" size="18" />
            </Button><!--{{ isModify ? '保存' : '编辑' }}-->
          </Tooltip>
          <Tooltip content="添加文件夹">
            <Button type="warning" @click="addFolder" :disabled="shipList.length === 0">
              <Icon type="ios-folder-outline" size="18" />
            </Button><!--添加文件夹-->
          </Tooltip>
          <Tooltip content="文件上传">
            <Button type="primary" @click="multiUpload">
              <Icon type="ios-cloud-upload-outline" size="18" />
            </Button><!--文件上传-->
          </Tooltip>
          <Tooltip content="批量删除">
            <Button :disabled="selectList.length === 0" type="error" @click="delMultiFile">
              <Icon type="md-close" size="18"/>
            </Button><!--批量删除-->
          </Tooltip>
          <Tooltip content="批量下载">
            <Button :disabled="selectList.length === 0" @click="downLoadFile">
              <Icon type="ios-cloud-download-outline" size="18" />
            </Button><!--批量下载-->
          </Tooltip>
        </div>
        <Table class="table-box" ref="tableSelection" border row-key="id" :loading="isLoading" :columns="columnsList" :data="tableList" @on-selection-change="selectChange" @on-row-dblclick="rowDbClick"></Table>
      </Card>
    </div>
    <CertificateModify :modalData="modifyModal" @updateBack="modifyBack"></CertificateModify>
    <input
      ref="fileInput"
      type="file"
      style="display: none;"
      @change="handleFileChange"
    />
    <input
      ref="multiFile"
      type="file"
      multiple
      style="display: none;"
      @change="handleMultiFileChange"
    />
  </div>
</template>

<script>
import CertificateModify from './certificateModify'
import fileUpload from './fileUpload.vue'
import API from '@/api/erpSys/certificate'
import CryptoJS from 'crypto-js'
import axios from 'axios'
import { getToken } from '@/libs/util'
import config from '@/config'
const baseUrl = process.env.NODE_ENV === 'development' ? config.baseUrl.dev : config.baseUrl.pro

export default ({
  components: {
    CertificateModify,
    fileUpload
  },
  data () {
    return {
      waringNum: 0, // 预到期数量
      overdueNum: 0, // 到期数量
      isWarnShow: false, // 是否显示预警内容
      isShipBtnShow: true, // 左侧菜单显隐参数
      secretKey: 'UNIFIED_Xt603209',
      spinShow: false,
      spinTxt: '加载中',
      isLoading: false,
      formDataList: [],
      breadNameList: [], // 面包屑列表
      isModify: false, // 是否批量编辑
      cur_root: '',
      curShip: 0,
      curModifyFileObj: {}, // 当前在修改的单个文件信息
      curFolderObj: {}, // 当前所在的文件夹 信息
      shipList: [], // 船舶列表
      modifyModal: { // 文件编辑弹窗 参数
        modal: false,
        data: {},
        title: '文件编辑'
      },
      selectList: [], // 被选中的内容
      shipQuery: {
        parent_id: null,
        path: null,
        is_root: '1',
        type: 'directory'
      },
      listQuery: {
        pageSize: 10,
        pageIndex: 1
      },
      total: 0,
      columnsList: [
        {
          type: 'index',
          width: 50,
          align: 'center',
          fixed: 'left'
        },
        {
          type: 'selection',
          width: 30,
          align: 'center',
          fixed: 'left'
        },
        {
          key: 'name',
          title: '名称',
          align: 'left',
          fixed: 'left',
          minWidth: 200,
          sortable: true,
          sortMethod: (a, b, type) => {
            if (type === 'asc') {
              return a > b ? -1 : 1
            } else {
              return a > b ? 1 : -1
            }
          },
          render: (h, params) => {
            let _that = this
            if (this.isModify || params.row.isNameModify) { // 打开编辑状态
              return h('span',{
                  style: {
                    display: 'inline-flex',
                    'align-items': 'center',
                    width: '100%'
                  }
                },
                [
                  h('Icon', {
                  props: {
                    type: params.row.type === 'directory' ? 'ios-folder-open' : 'ios-paper',
                    size: '24',
                    color: params.row.type === 'directory' ? '#f90' : '#70BD5B'
                  },
                  style: {
                    // display: params.row.type === 'directory' ? 'inline-block' : 'none',
                    marginRight: '5px'
                  }
                }),
                h('Input', {
                  props: {
                    type: 'textarea',
                    value: params.row.name,
                    autofocus: true,
                    placeholder: '请输入名称'
                  },
                  style: {
                    width: '100%'
                  },
                  on: {
                    input: (val) => {
                      _that.tableList[params.row._index].name = val
                    }
                  }
                })
              ])
            } else {
              return h('Tooltip', {
                props: {
                  maxWidth: 300,
                  transfer: true
                }
              }, [
                h('span', {
                  slot: 'content'
                }, params.row.name),
                h('div', {
                  style: {
                    display: 'inline-flex',
                    alignItems: 'center',
                    marginTop: '5px'
                  }
                }, [
                  h('Icon', {
                    props: {
                      type: params.row.type === 'directory' ? 'ios-folder-open' : 'ios-paper',
                      size: '24',
                      color: params.row.type === 'directory' ? '#f90' : '#70BD5B'
                    },
                    style: {
                      // display: params.row.type === 'directory' ? 'inline-block' : 'none',
                      marginRight: '5px'
                    }
                  }),
                  h('div', {
                    style: {
                      cursor: params.row.type === 'directory' ? 'pointer' : '',
                      display: '-webkit-box',
                      '-webkit-box-orient': 'vertical',
                      overflow: 'hidden',
                      'text-overflow': 'ellipsis',
                      '-webkit-line-clamp': 2
                    },
                    on: {
                      click: async (event) => {
                        this.folderClick(params.row)
                      }
                    }
                  }, [
                    h('span', params.row.name)
                  ])
                ])
                ])
            }
          }
        },
        {
          key: 'name_cn',
          title: '中文名称',
          align: 'center',
          width: 165,
          fixed: 'left',
          render: (h, params) => {
            let _that = this
            if (this.isModify) { // 打开编辑状态
              return h('span',{
                  style: {
                    display: 'inline-flex',
                    'align-items': 'center',
                    width: '100%'
                  }
                },
                [
                h('Input', {
                  props: {
                    value: params.row.name_cn,
                    autofocus: true,
                    placeholder: '请输入中文名称'
                  },
                  style: {
                    width: '100%'
                  },
                  on: {
                    input: (val) => {
                      _that.tableList[params.row._index].name_cn = val
                    }
                  }
                })
              ])
            } else {
              return h('div', {}, params.row.name_cn)
            }
          }
        },
        {
          key: 'status',
          title: '预警状态',
          width: 165,
          align: 'center',
          fixed: 'left',
          render: (h, params) => {
            if (params.row.type === 'directory') {
              let warningStr = '已有' + params.row.expired_file_count + '个文件超期'
              return h('span', {
                style: {
                  display: parseFloat(params.row.expired_file_count) > 0 ? 'inline-block' : 'none',
                  padding: '1px 10px',
                  borderRadius: '15px',
                  color: '#fff',
                  background: '#e74c3c'
                }
              }, warningStr || '--')
            } else {
              return h('span', {
                style: {
                  padding: '1px 10px',
                  borderRadius: '15px',
                  color: '#fff',
                  background: this.getStatusColor(params.row.days_overdue)
                }
              }, params.row.status || '--')
            }
          }
        },
        {
          key: 'issue_date',
          title: '签发日期',
          width: 128,
          align: 'center',
          render: (h, params) => {
            let _that = this
            if (this.isModify) {
              return h('DatePicker', {
                props: {
                  value: params.row.issue_date,
                  type: 'date',
                  format: 'yyyy-MM-dd'
                },
                style: {
                  display: params.row.type === 'directory' ? 'none' : 'inline-block',
                  width: '100%'
                },
                on: {
                  'on-change': (date) => {
                    _that.tableList[params.row._index].issue_date = date // 处理日期更改
                  }
                }
              })
            } else {
              return h('span', {}, params.row.issue_date || '--')
            }
          }
        },
        {
          key: 'expiration_date',
          title: '到期时间',
          width: 128,
          align: 'center',
          render: (h, params) => {
            let _that = this
            if (this.isModify) {
              return h('DatePicker', {
                props: {
                  value: params.row.expiration_date,
                  type: 'date',
                  format: 'yyyy-MM-dd'
                },
                style: {
                  display: params.row.type === 'directory' ? 'none' : 'inline-block',
                  width: '100%'
                },
                on: {
                  'on-change': (date) => {
                    _that.tableList[params.row._index].expiration_date = date // 处理日期更改
                  }
                }
              })
            } else {
              return h('span', {}, params.row.expiration_date || '--')
            }
          }
        },
        {
          key: 'remarks',
          title: '备注',
          width: 130,
          align: 'center',
          render: (h, params) => {
            let _that = this
            if (this.isModify) { // 打开编辑状态
              return h('span',{
                  style: {
                    display: 'inline-flex',
                    'align-items': 'center',
                    width: '100%'
                  }
                },
                [
                h('Input', {
                  props: {
                    value: params.row.remarks,
                    autofocus: true,
                    placeholder: '请输入备注信息'
                  },
                  style: {
                    width: '100%'
                  },
                  on: {
                    input: (val) => {
                      _that.tableList[params.row._index].remarks = val
                    }
                  }
                })
              ])
            } else {
              return h('div', {}, params.row.remarks)
            }
          }
        },
        {
          key: '',
          title: '操作',
          align: 'left',
          width: 150,
          fixed: 'right',
          render: (h, params) => {
            return h('div', [
              h('Tooltip', {
                props: {
                  placement: 'bottom',
                  theme: 'light'
                }
              }, [
                h('Button', {
                  props: {
                    icon: 'md-eye',
                    size: 'small',
                    disabled: this.isModify
                  },
                  style: {
                    margin: '0 5px',
                    display: params.row.type === 'file' ? 'inline-block' : 'none'
                  },
                  on: {
                    click: (event) => {
                      event.stopPropagation()
                      this.curModifyFileObj = params.row
                      this.handleView(params.row)
                    }
                  }
                }),
                h('div', {
                  slot: 'content',
                  style: {
                    margin: '0 5px',
                    cursor: 'pointer'
                  }
                }, '预览')
              ]),
              h('Tooltip', {
                props: {
                  placement: 'bottom',
                  theme: 'light'
                }
              }, [
                h('Button', {
                  props: {
                    icon: 'md-brush',
                    size: 'small',
                    disabled: this.isModify
                  },
                  style: {
                    margin: '0 5px',
                    display: params.row.type === 'file' ? 'inline-block' : 'none'
                  },
                  on: {
                    click: (event) => {
                      event.stopPropagation()
                      this.curModifyFileObj = params.row
                      this.handleEditModal(params.row)
                    }
                  }
                }),
                h('div', {
                  slot: 'content',
                  style: {
                    margin: '0 5px',
                    cursor: 'pointer'
                  }
                }, params.row.type === 'file' ? '修改附件' : '重命名')
              ]),
              h('Tooltip', {
                props: {
                  placement: 'bottom',
                  theme: 'light'
                }
              }, [
                h('Button', {
                  props: {
                    icon: 'md-cloud-download',
                    size: 'small'
                  },
                  style: {
                    margin: '0 5px'
                  },
                  on: {
                    click: (event) => {
                      event.stopPropagation()
                      this.handleDownSingle(params.row)
                    }
                  }
                }),
                h('div', {
                  slot: 'content',
                  style: {
                    margin: '0 5px',
                    cursor: 'pointer'
                  }
                }, '下载')
              ]),
              h('Tooltip', {
                props: {
                  placement: 'bottom',
                  theme: 'light'
                }
              }, [
                h('Button', {
                  style: {
                    margin: '0 5px'
                  },
                  props: {
                    icon: 'md-trash',
                    size: 'small',
                    disabled: this.isModify
                  },
                  on: {
                    click: (event) => {
                      event.stopPropagation()
                      this.handleDelete(params.row)
                    }
                  }
                }),
                h('div', {
                  slot: 'content',
                  style: {
                    margin: '0 5px',
                    cursor: 'pointer'
                  }
                }, '删除')
              ])
            ])
          }
        }
      ],
      tableList: []
    }
  },
  methods: {
    getList (item) { // 获取列表数据
      this.isLoading = true
      Object.assign(this.listQuery, {
        parent_id: item.file_directory_id
      })
      API.queryFileDirectoryList(this.listQuery).then(res => {
        this.isLoading = false
        if (res.data.Code === 10000) {
          this.tableList = res.data.Result
        }
      })
    },
    getShipList () { // 获取船舶信息（根目录文件夹）
      API.queryFileDirectoryList(this.shipQuery).then(res => {
        if (res.data.Code === 10000) {
          this.shipList = res.data.Result
          if (this.shipList.length > 0) {
            this.cur_root = this.shipList[0].file_directory_id
            this.curFolderObj = this.shipList[this.curShip]
            this.breadNameList.push({
              file_directory_id: this.curFolderObj.file_directory_id,
              name: this.curFolderObj.name
            })
            this.getList(this.curFolderObj)
          }
        }
      })
    },
    backToBtnList () { // 返回列表
      this.isWarnShow = false
      this.getList(this.curFolderObj)
    },
    showWarnMess (str) { // 预警数量点击事件
      this.isWarnShow = true
      this.isLoading = true
      this.tableList = []
      let _param = {
        type: 'file'
      }
      if (str === 'overdue') { // 超期预警点击
        Object.assign(_param, {
          days_overdue_st: 0
        })
        API.queryFileDirectoryList(_param).then(res => {
          this.isLoading = false
          if (res.data.Code === 10000) {
            this.tableList = res.data.Result
          }
        })
      }
      if (str === 'warn') { // 预到期预警点击
        Object.assign(_param, {
          days_overdue_st: -30,
          days_overdue_ed: 0
        })
        API.queryFileDirectoryList(_param).then(res => {
          this.isLoading = false
          if (res.data.Code === 10000) {
            this.tableList = res.data.Result
          }
        })
      }
    },
    async handleFileChange (event) { // 替换单个文件
      const file = event.target.files[0]
      const formData = new FormData()
      const token = getToken()
      if (file.type !== undefined) {
        formData.append('token', token)
        formData.append('file_directory_id', this.curModifyFileObj.file_directory_id)
        formData.append('file_hash', this.curModifyFileObj.file_hash)
        formData.append('file', file)
      }
      this.spinShow = true
      this.spinTxt = '正在上传中…'
      const config = {
        onUploadProgress: progressEvent => {
          let progress = parseFloat(progressEvent.progress) * 100
          let percent = progress.toFixed(2)
          this.spinTxt = '已上传' + percent + '%'
          if (progress === 100) {
            this.spinTxt = '等待服务器解析中……'
          }
        }
      }
      const postUrl = baseUrl + 'file/directory/replaceSingleFile'
      await axios.post(postUrl, formData, config).then(res => {
        this.spinShow = false
        if (res.data.Code === 10000) {
          this.isModify = true
          this.getList(this.curFolderObj)
        } else {
          this.$Message.error(res.data.Message)
        }
      })
      // API.replaceSingleFile(formData).then(res => {
      //   this.curModifyFileObj = {} // 拉取后置空 防止指向出错
      //   if (res.data.Code === 10000) {
      //     this.spinShow = false
      //     this.$Message.success(res.data.Message)
      //     this.getList(this.curFolderObj)
      //   } else {
      //     this.$Message.error(res.data.Message)
      //   }
      // })
    },
    breadClick (idx) { // 面包屑点击事件
      if (idx !== this.breadNameList.length - 1) {
        this.curFolderObj = this.breadNameList[idx]
        let curBread = this.breadNameList.findIndex(item => item.file_directory_id === this.breadNameList[idx].file_directory_id)
        if (curBread !== -1) {
          this.breadNameList.splice(curBread + 1)
        }
        this.getList(this.breadNameList[idx])
      }
    },
    backParent () { // 后退至上一个文件夹
      this.tableList = []
      if (this.breadNameList.length > 1) {
        this.breadNameList.pop()
        let _param = this.breadNameList[this.breadNameList.length - 1]
        this.getList(_param)
      }
    },
    multiModify () { // 文件名及到期时间批量编辑
      this.isModify = !this.isModify
      if (!this.isModify && this.tableList.length > 0) {
        let _tableList = this.tableList.map(item => {
          return {
            file_directory_id: item.file_directory_id,
            name: item.name,
            name_cn: item.name_cn,
            issue_date: item.issue_date,
            expiration_date: item.expiration_date,
            remarks: item.remarks
          }
        })
        let _param = {
          detailJson: JSON.stringify(_tableList)
        }
        this.spinShow = true
        this.spinTxt = '正在上传'
        API.updateFileDirectoryBatch(_param).then(res => {
          this.spinShow = false
          if (res.data.Code === 10000) {
            this.getList(this.curFolderObj)
            this.$Message.success(res.data.Message)
          } else {
            this.$Message.error(res.data.Message)
          }
        })
      }
    },
    async handleMultiFileChange (event) { // 批量文件上传
      const files = event.target.files
      const token = getToken()
      const formData = new FormData()
      const uploadChange = (progressEvent) => {
        console.log(progressEvent)
      }
      Array.from(files).forEach(file => {
        if (file.type !== undefined) {
          formData.append('token', token)
          formData.append('parent_id', this.curFolderObj.file_directory_id)
          formData.append('path', this.curFolderObj.path)
          formData.append('type', 'file')
          formData.append('files', file)
        }
      })
      this.spinShow = true
      this.spinTxt = '正在上传中…'
      const config = {
        onUploadProgress: progressEvent => {
          let progress = parseFloat(progressEvent.progress) * 100
          let percent = progress.toFixed(2)
          this.spinTxt = '已上传' + percent + '%'
          if (progress === 100) {
            this.spinTxt = '等待服务器解析中……'
          }
        }
      }
      const postUrl = baseUrl + 'file/directory/addFileDirectory'
      await axios.post(postUrl, formData, config).then(res => {
        this.spinShow = false
        if (res.data.Code === 10000) {
          this.isModify = true
          this.getList(this.curFolderObj)
        } else {
          this.$Message.error(res.data.Message)
        }
      })
      // API.fileUpload(formData, config).then(res => {
      //   this.spinShow = false
      //   if (res.data.Code === 10000) {
      //     this.isModify = true
      //     this.getList(this.curFolderObj)
      //   } else {
      //     this.$Message.error(res.data.Message)
      //   }
      // })
    },
    multiUpload () {
      this.$refs.multiFile.value = ''
      this.$refs.multiFile.click()
    },
    rowDbClick (row) { // 表格双击预览
      if (row.type === 'file') { // 先判断是文件才走预览
        this.handleView(row)
      }
    },
    handleView (row) { // 文件预览
      try {
        const encryptedData = CryptoJS.enc.Base64.parse(row.wpsUrl)
        const key = CryptoJS.enc.Utf8.parse(this.secretKey)

        const decrypted = CryptoJS.AES.decrypt(
          { ciphertext: encryptedData },
          key,
          { mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7 }  // ECB 模式，不需要 IV
        )
        const decryptedText = decrypted.toString(CryptoJS.enc.Utf8)
        let imgType = ['bmp','jpg','jpeg','png','tif','gif','pcx','tga','exif','fpx','svg','psd','cdr','pcd','dxf','ufo','eps','ai','raw','WMF','webp','avif','apng']
        if (imgType.includes(row.file_type.split('.')[1])) {
          this.fileType = 'img'
          const imgData = CryptoJS.enc.Base64.parse(row.url)
          const key = CryptoJS.enc.Utf8.parse(this.secretKey)

          const decrypted = CryptoJS.AES.decrypt(
            { ciphertext: imgData },
            key,
            { mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7 }  // ECB 模式，不需要 IV
          )
          const imgUrl = decrypted.toString(CryptoJS.enc.Utf8)
          window.open(imgUrl, '_blank')
        } else {
          sessionStorage.setItem('wpsUrl', decryptedText)
          sessionStorage.setItem('token', getToken())
          const jump = this.$router.resolve({ name: 'viewFile' })
          window.open(jump.href, '_blank')
        }
      } catch (err) {
        console.log('出错了： ' + err)
      }
    },
    handleEditModal (row) { // 编辑 文件夹和附件 单个修改
      if (row.type === 'directory') { // 修改文件夹名称 打开弹窗
        // this.modifyModal.modal = true
        // this.modifyModal.data = row
        Object.assign(this.tableList[row._index], {
          isNameModify: true
        })
        setTimeout(() => {
          this.$forceUpdate()
        }, 100)
      } else { // 修改附件调起本地文件上传
        this.$refs.fileInput.value = ''
        this.$refs.fileInput.click()
      }
    },
    handleDownSingle (row) { // 单个文件下载
      if (row.type === 'file') { // 单个文件下载
        try {
          const encryptedData = CryptoJS.enc.Base64.parse(row.url)
          const key = CryptoJS.enc.Utf8.parse(this.secretKey)

          const decrypted = CryptoJS.AES.decrypt(
            { ciphertext: encryptedData },
            key,
            { mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7 }  // ECB 模式，不需要 IV
          )
          const decryptedText = decrypted.toString(CryptoJS.enc.Utf8)
          const link = document.createElement('a')

          fetch(decryptedText)
            .then(response => response.blob())
            .then(blob => {
              // 创建 blob URL
              const url = window.URL || window.webkitURL || window.moxURL
              link.href = url.createObjectURL(blob)
              link.download = row.name.split('.')[0] + row.file_type // 设置文件名
              document.body.appendChild(link)
              link.click() // 触发下载
              document.body.removeChild(link) // 移除临时链接
              URL.revokeObjectURL(link.href) // 释放 URL
            })
            .catch(error => {
              console.error('下载失败:', error)
            })
        } catch (err) {
          console.log('出错了：' + err)
        }
      }
      if (row.type === 'directory') { // 当前文件夹下载
        let _param = {
          file_directory_ids: row.file_directory_id,
          path_like: row.path
        }
        this.spinShow = true
        this.spinTxt = '正在下载'
        API.downloadFileDirectory(_param).then(res => {
          if (res.status === 200) {
            this.spinShow = false
            const link = document.createElement('a')
            const name = this.curFolderObj.name + '.zip'
            try {
              const blob = new Blob([res.data], { type: 'application/zip' })
              const url = window.URL || window.webkitURL || window.moxURL

              link.href = url.createObjectURL(blob)
              link.setAttribute('download', name)
              document.body.appendChild(link)
              link.click()
              document.body.removeChild(link)
              url.revokeObjectURL(link.href)
              this.$refs.tableSelection.selectAll(false)
            } catch (e) {
              console.log('下载出错', e)
            }
          } else {
            console.log(res)
          }
        })
      }
    },
    handleDelete (row) { // 删除文件
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确认删除文件？</p>',
        loading: true,
        onOk: () => {
          this.loading = false
          let _param = {
            file_directory_id: row.file_directory_id,
            path: row.path,
            name: row.name,
            type: row.type
          }
          API.delFileDirectory(_param).then(res => {
            if (res.data.Code === 10000) {
              this.$Message.success(res.data.Message)
              this.getList(this.curFolderObj)
            } else {
              this.$Message.error(res.data.Message)
            }
          })
          this.$Modal.remove()
        }
      })
    },
    handleAddFile (row) { // 表格内部添加二级文件夹
      this.$Modal.confirm({
        render: (h) => {
          return h('Input', {
            props: {
              value: this.value,
              autofocus: true,
              placeholder: '请输入文件夹名'
            },
            on: {
              input: (val) => {
                this.value = val
              }
            }
          })
        },
        onOk: () => { // 树状文件夹 待完善
          if (this.tableList[row._index].hasOwnProperty('children')) { // 如果已经有children数据，直接push
            this.tableList[row._index].children.push({
              file_name: this.value,
              type: 'directory'
            })
          } else {
            Object.assign(this.tableList[row._index], {
              children: [
                {
                  file_name: this.value,
                  type: 'directory'
                }
              ]
            })
          }
          this.value = ''
        },
        onCancel: () => {
          this.value = ''
        }
      })
    },
    handleUploadFile (row) { // 文件夹内上传文件
      
    },
    modifyBack () { // 文件名更新回调
      this.getList(this.curFolderObj)
    },
    shipBtnShowHide () {
      this.isShipBtnShow = !this.isShipBtnShow
    },
    shipSelect (obj) {
      this.curShip = this.shipList.findIndex(item => item.file_directory_id === obj.value)
      this.curFolderObj = this.shipList[this.curShip]
      this.breadNameList = [] // 先把列表置空再Push
      this.breadNameList.push({
        file_directory_id: this.curFolderObj.file_directory_id,
        name: this.curFolderObj.name,
        path: this.curFolderObj.path
      })
      this.getList(this.shipList[this.curShip])
    },
    shipClick (idx) { // 船舶点击
      this.curShip = idx
      this.curFolderObj = this.shipList[idx]
      this.breadNameList = [] // 先把列表置空再Push
      this.breadNameList.push({
        file_directory_id: this.curFolderObj.file_directory_id,
        name: this.curFolderObj.name,
        path: this.curFolderObj.path
      })
      this.getList(this.shipList[idx])
    },
    addShip () { // 添加船舶
      this.$Modal.confirm({
        render: (h) => {
          return h('Input', {
            props: {
              value: this.value,
              autofocus: true,
              placeholder: '请输入船舶名称'
            },
            on: {
              input: (val) => {
                this.value = val
              }
            }
          })
        },
        title: '添加船舶',
        onOk: () => {
          let _param = {
            parent_id: null,
            name: this.value,
            path: null,
            type: 'directory'
          }
          API.addFileDirectory(_param).then(res => {
            if (res.data.Code === 10000) {
              this.$Message.success(res.data.Message)
              this.breadNameList = []
              this.getShipList()
              this.value = ''
            } else {
              this.$Message.error(res.data.Message)
            }
          })
        },
        onCancel: () => {
          this.value = ''
        }
      })
    },
    delShip (item) { // 删除船舶
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确定要删除本条船舶下所有数据？</p>',
        loading: true,
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          let _param = {
            file_directory_id: item.file_directory_id,
            path: null,
            name: item.name,
            type: item.type
          }
          API.delFileDirectory(_param).then(res => {
            this.$Modal.remove()
            if (res.data.Code === 10000) {
              this.breadNameList = []
              this.curShip = 0
              this.getShipList()
            } else {
              this.$Message.error(res.data.Message)
            }
          })
        }
      })
    },
    addFolder () { // 添加文件夹
      this.$Modal.confirm({
        render: (h) => {
          return h('Input', {
            props: {
              value: this.value,
              autofocus: true,
              placeholder: '请输入文件夹名'
            },
            on: {
              input: (val) => {
                this.value = val
              }
            }
          })
        },
        title: '新增文件夹',
        onOk: () => {
          let _param = {
            parent_id: this.curFolderObj.file_directory_id,
            name: this.value,
            path: this.curFolderObj.path,
            type: 'directory'
          }
          API.addFileDirectory(_param).then(res => {
            this.$Modal.remove()
            if (res.data.Code === 10000) {
              this.getList(this.curFolderObj)
            } else {
              this.$Message.error(res.data.Message)
            }
          })
          this.value = ''
        },
        onCancel: () => {
          this.value = ''
        }
      })
    },
    folderClick (row) { // 文件夹点击
      if (row.type === 'file') return
      this.tableList = []
      this.curFolderObj = row
      this.breadNameList.push({
        file_directory_id: this.curFolderObj.file_directory_id,
        name: this.curFolderObj.name
      })
      this.getList(row)
    },
    getStatusColor (days_overdue) {
      let colorStr = ''
      if (days_overdue !== '' && parseFloat(days_overdue) < -30) { // 未超期
        colorStr = '#19be6b'
      }
      if (days_overdue !== '' && parseFloat(days_overdue) >= -30 && parseFloat(days_overdue) < 0) { // 30天内预警
        colorStr = '#ffa502'
      }
      if (days_overdue !== '' && parseFloat(days_overdue) >= 0) { // 超期预警
        colorStr = '#e74c3c'
      }
      return colorStr
    },
    delMultiFile () { // 批量删除
      if (this.selectList.length > 0) {
        this.$Modal.confirm({
          title: '提示',
          content: '<p>确定要删除选中数据，删除后将无法恢复？</p>',
          loading: true,
          okText: '确定',
          cancelText: '取消',
          onOk: () => {
            let idList = this.selectList.map(item => item.file_directory_id)
            let _param = {
              file_directory_ids: idList.join()
            }
            this.spinShow = true
            this.spinTxt = '正在删除'
            API.deleteFileDirectories(_param).then(res => {
              this.spinShow = false
              this.$Modal.remove()
              if (res.data.Code === 10000) {
                this.$Message.success(res.data.Message)
                this.getList(this.curFolderObj)
              } else {
                this.$Message.error(res.data.Message)
              }
            })
          }
        })
      }
    },
    downLoadFile () { // 下载附件
      if (this.selectList.length > 0) {
        let idList = this.selectList.map(item => item.file_directory_id)
        let _param = {
          file_directory_ids: idList.join(),
          path_like: this.selectList[0].path
        }
        this.spinShow = true
        this.spinTxt = '正在下载'
        API.downloadFileDirectory(_param).then(res => {
          if (res.status === 200) {
            this.spinShow = false
            const link = document.createElement('a')
            const name = this.curFolderObj.name + '.zip'
            try {
              const blob = new Blob([res.data], { type: 'application/zip' })
              const url = window.URL || window.webkitURL || window.moxURL

              link.href = url.createObjectURL(blob)
              link.setAttribute('download', name)
              document.body.appendChild(link)
              link.click()
              document.body.removeChild(link)
              url.revokeObjectURL(link.href)
              this.$refs.tableSelection.selectAll(false)
            } catch (e) {
              console.log('下载出错', e)
            }
          } else {
            console.log(res)
          }
        })
      }
    },
    selectChange (arr) { // 选中
      this.selectList = arr
    },
    getWarnNum () { // 获取预警数量
      API.queryFileDirectoryOverdueCount().then(res => {
        if (res.data.Code === 10000) {
          this.waringNum = res.data.waringNum
          this.overdueNum = res.data.overdueNum
        }
      })
    }
  },
  created () {
    this.$store.commit('setCollapsed', true)
    this.getWarnNum()
    this.getShipList()
  },
  beforeDestroy () {
    this.$store.commit('setCollapsed', false)
  }
})
</script>
<style>
  .statistic_area {
    position: absolute;
    top: 10px;
    right: 300px;
    z-index: 999;
  }
  .table-box .ivu-table-cell {
    padding-left: 8px !important;
    padding-right: 5px !important;
  }
  .ship_btn_area {
    cursor: pointer;
    background: #f7f7f7;
    padding: 10px 8px;
    margin-bottom: 10px;
    border-radius: 5px;
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-all;
    white-space: nowrap;
  }
  .ship_btn_close {
    cursor: pointer;
    float: right;
  }
  .ship_btn_close:hover {
    color: #2d8cf0;
  }
  .left_show_hide {
    /* width: 20px; */
    display: inline-flex;
    height: 64px;
    text-align: center;
    padding: 2px 0;
    background: #fff;
    border-radius: 15px;
    border: 1px solid #d0cdcd;
    align-items: center;
    position: absolute;
    top: calc(50% - 32px);
    right: -8px;
    cursor: pointer;
  }
  .left_show_hide:hover {
    background: #2d8cf0;
    color: #fff;
  }
  .add_ship_btn {
    margin-top: 10px;
  }
  .btn_area {
    text-align: right;
    margin-bottom: 15px;
  }
  .move_back_btn {
    display: inline-flex;
    align-items: center;
    position: absolute;
    left: 10px;
  }
  .btn_area button {
    margin-left: 10px;
  }
  .bread-span {
    display: inline-flex;
    align-items: center;
  }
  .demo-spin-icon-load{
    animation: ani-demo-spin 1s linear infinite;
  }
  @keyframes ani-demo-spin {
    from { transform: rotate(0deg);}
    50%  { transform: rotate(180deg);}
    to   { transform: rotate(360deg);}
  }
</style>
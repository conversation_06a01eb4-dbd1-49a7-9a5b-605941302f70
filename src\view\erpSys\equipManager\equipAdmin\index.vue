<template>
  <Card>
    <Tabs @on-click="tabClick">
      <TabPane label="设备管理" name="tab1">
        <Row>
          <Col span="14">
            <Button v-show="isEquipAdmin" type="primary" @click="handleEdit">新增设备</Button>
          </Col>
          <Col span="10" style="text-align: right;">
            <Dropdown @on-click="shipChange">
              <a href="javascript:void(0)" v-html="ship_name" style="color: #515a6e;"></a>
              <Icon type="md-arrow-dropdown"></Icon>
              <Dropdown-menu slot="list">
                <Dropdown-item v-for="(item, idx) in shipList" :name="item.name" :key="'ship'+idx">{{item.name}}</Dropdown-item>
              </Dropdown-menu>
            </Dropdown>
            <span> 共{{total}}条结果 </span>
            <search @searchResults="searchResults" :setSearch="setSearchData" @resetResults="resetResults" class="search-vie"></search>
          </Col>
        </Row>
        <Row>
          <Col span="4" style="border: 1px solid #ccc;">
            <h3 class="col-text">船舶设备</h3>
            <Tree :data="equipTree" :render="renderContent" class="tree" @on-select-change="treeSelect" @on-toggle-expand="toggleExpand" empty-text></Tree>
          </Col>
          <Col span="20" style="padding-left: 10px;">
            <Table
              border
              ref="selection"
              :loading="loading"
              :columns="columns"
              :data="equipList"
              @on-row-click="tableClick"
              @on-select-all="tableSelectAll"
              @on-select-all-cancel="tableSelectCancel"
              @on-select="tableSelectAll"
              @on-select-cancel="tableSelectCancel"></Table>
            <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
            :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
          </Col>
        </Row>
      </TabPane>
      <TabPane label="设备分类" name="tab2">
        <Table border :loading="loading1" :columns="classiColumns" :data="classiList" maxHeight="500"></Table>
      </TabPane>
    </Tabs>
    <!-- 新增编辑弹窗内容 -->
    <Modal v-model="equipModal"
      :title="titleModal"
      @on-visible-change="modalShowHide"
      :width="modalType !== 'file' ? '70%' : '40%'"
      :mask-closable="false">
      <div class="ship_title">{{ ship_name }}</div>
      <Form ref="formInline" :model="formInline" :rules="ruleInline" inline :label-width="100">
        <div v-if="modalType !== 'file'" class="form-class-div">
          <Row>
            <Col span="12">
              <Form-item label="设备分类">
                <Input type="text" v-model="formInline.parent_parent_name" readonly></Input>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item label="父设备">
                <div>{{formInline.parent_name || '--'}}</div>
              </Form-item>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <Form-item label="系统编码">
                <div>{{ this.formInline.number || '--' }}</div>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item label="出厂编号">
                <Input type="text" v-model="formInline.xtgf_factoryno" placeholder="请输入"></Input>
              </Form-item>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <Form-item prop="name" label="设备名称">
                <Input type="text" v-model="formInline.name" placeholder="请输入"></Input>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item label="设备型号">
                <Input type="text" v-model="formInline.xtgf_cgequipmentmodel" placeholder="请输入"></Input>
              </Form-item>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <Form-item prop="classi" label="设备级别">
                <Select v-model="formInline.xtgf_equipmentlevelid" placeholder="请选择">
                  <Option value="1">一般设备</Option>
                  <Option value="2">安全设备</Option>
                  <Option value="3">关键设备</Option>
                  <Option value="4">应急设备</Option>
                </Select>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item label="制造厂商">
                <Input type="text" v-model="formInline.xtgf_manufacturer" placeholder="请输入"></Input>
              </Form-item>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <Form-item label="图纸编号">
                <Input type="text" v-model="formInline.xtgf_drawingno" placeholder="请输入"></Input>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item label="生产日期">
                <Date-picker type="date" placeholder="请选择" @on-change="manufacturedateChange" v-model="formInline.xtgf_manufacturedate" style="width: 100%"></Date-picker>
              </Form-item>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <Form-item label="证书号">
                <Input type="text" v-model="formInline.xtgf_certificateno" placeholder="请输入"></Input>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item label="技术参数">
                <Input type="text" v-model="formInline.xtgf_technicalparameter" placeholder="请输入"></Input>
              </Form-item>
            </Col>
          </Row>
        </div>
      </Form>
      <div slot="footer">
        <Button type="primary" @click="handleCancel">取消</Button>
        <Button type="primary" @click="handleSubmit('formInline')">保存</Button>
      </div>
    </Modal>
  </Card>
</template>
<script>
import search from '_c/search'
import fileUpload from '../../../performance/performanceTemp/fileUpload'
import { shipQuery, equipListLevelThree, equipListLevelMore, equipListLevelSearch } from '@/api/erpSys/equipManager'
import API from '@/api/erpSys/common'

export default {
  components: {
    search,
    fileUpload
  },
  data () {
    return {
      isEquipAdmin: false, // 是否有设备管理新增权限
      // 设备管理
      status: ['A', 'B', 'C'],
      statusList: [{
        value: 'A',
        label: '暂存'
      }, {
        value: 'B',
        label: '已提交'
      }, {
        value: 'C',
        label: '已审核'
      }], // 数据状态
      ship_name: '', // 船舶名称
      ship_id: '', // 船舶id
      shipList: [], // 储存船名下拉
      equipTreeSeled: {}, // 当前已选中的节点数组
      fileDataList: [], // 附件
      wps_ids: '',
      equipModal: false,
      equipModalType: '',
      titleModal: '',
      modalType: '',
      columns: [
        {
          type: 'index',
          title: '序号',
          width: 70,
          align: 'center'
        },
        {
          title: '设备名称',
          key: 'name',
          align: 'center'
        },
        {
          title: '设备型号',
          key: 'xtgf_cgequipmentmodel',
          align: 'center'
        },
        {
          title: '制造厂商',
          key: 'xtgf_manufacturer',
          align: 'center'
        },
        {
          title: '出厂编号',
          key: 'xtgf_factoryno',
          align: 'center'
        },
        {
          title: '图纸编号',
          key: 'xtgf_drawingno',
          align: 'center'
        },
        {
          title: '生产日期',
          key: 'xtgf_manufacturedate',
          align: 'center',
          render: (h, params) => {
            let _backDateStr = '-'
            if (params.row.xtgf_manufacturedate) {
              _backDateStr = params.row.xtgf_manufacturedate.substring(0, 10)
            }
            return h('div', {}, _backDateStr)
          }
        },
        {
          title: '设备级别',
          key: 'xtgf_equipmentlevelid',
          align: 'center',
          render: (h, params) => {
            let xtgf_equipmentlevelid = ''
            switch (params.row.xtgf_equipmentlevelid) {
              case '1':
                xtgf_equipmentlevelid = '一般设备'
                break
              case '2':
                xtgf_equipmentlevelid = '安全设备'
                break
              case '3':
                xtgf_equipmentlevelid = '关键设备'
                break
              case '4':
                xtgf_equipmentlevelid = '应急设备'
                break
              default:
                break
            }
            return h('div', {}, xtgf_equipmentlevelid)
          }
        },
        {
          title: '数据状态',
          key: 'status',
          align: 'center',
          width: 100,
          render: (h, params) => {
            let curVal = ''
            switch (params.row.status) {
              case 'A':
                curVal = '暂存'
                break
              case 'B':
                curVal = '已提交'
                break
              case 'C':
                curVal = '已审核'
                break
              default:
                break
            }
            return h('div', {}, curVal)
          }
        }
        // {
        //   title: '操作',
        //   key: '',
        //   width: 100,
        //   align: 'center',
        //   render: (h, params) => {
        //     return h('Button', {
        //       props: {
        //         icon: 'md-trash',
        //         size: 'small'
        //       },
        //       on: {
        //         click: (e) => {
        //           e.stopPropagation()
        //           this.handleDelete(params.row)
        //         }
        //       }
        //     })
        //   }
        // }
      ],
      equipTree: [],
      selectedIds: [], // 已勾选数据
      selectionData: [],
      equipList: [], // 设备列表
      loading: false,
      total: 0,
      listQuery: {
        number: '02',
        createorg_number: 'xt',
        level: '2',
        status: ['A', 'B', 'C'],
        pageSize: 10,
        pageNo: 1
      },
      listCurrent: 1,
      setSearchData: {
        name: {
          type: 'text',
          width: 220,
          value: '',
          isdisable: false,
          placeholder: '请输入设备名称、设备型号、制造厂商'
        }
      },
      formInline: {},
      ruleInline: {
        name: [{ required: true, message: '此处不能为空!', trigger: 'change' }]
      },
      // 设备分类
      loading1: false,
      classiQuery: {
        number: '02',
        createorg_number: 'xt',
        level: '2',
        status: ['A', 'B', 'C'],
        pageSize: 10000,
        pageNo: 1
      },
      classiList: [],
      classiColumns: [
        {
          type: 'index',
          title: '序号',
          width: 70,
          align: 'center'
        },
        {
          title: '分类编码',
          key: 'number',
          width: 120,
          align: 'center'
        },
        {
          title: '分类名称',
          key: 'name',
          align: 'center',
          width: 300
        },
        {
          title: '适用船舶',
          key: 'xtgf_boatid',
          align: 'center',
          render: (h, params) => {
            let shipVal = []
            params.row.xtgf_boatid.forEach(e => {
              shipVal.push(e.name.toString() + '; ')
            })
            return h('span', shipVal)
          }
        }
      ]
    }
  },
  created () {
    if (localStorage.getItem('userDataId') === '9195D8D08B35449CA4AB1E4F545BDDD1' || localStorage.getItem('userDataId') === 'A4438CBA319B44149C8472353B9206AB') { // 固定刘建龙与史浩可新增备件
      this.isEquipAdmin = true
    }
    shipQuery({ pageSize: 10000, pageNo: 1 }).then(res => { // 获取船舶列表
      if (res.status === 200) {
        this.shipList = res.data.data.rows
        this.ship_id = this.shipList[0].number
        if (localStorage.getItem('equip_ship_name')) {
          this.ship_name = localStorage.getItem('equip_ship_name')
          this.ship_id = this.shipList.filter(item => { return item.name === this.ship_name })[0].number
        } else {
          this.ship_name = this.shipList[0].name
        }
        this.getEquipListLevelThree()
      }
    })
  },
  methods: {
    // 获取船舶设备
    getEquipListLevelThree () {
      let data = {
        level: '3',
        name: this.ship_name,
        enable: '1',
        pageSize: 10000,
        pageNo: 1
      }
      this.equipTree = []
      equipListLevelThree(data).then(res => { // 船舶设备一级列表-通过船舶名称进行查询
        if (res.status === 200) {
          if (res.data.data.rows.length > 0) {
            this.equipTreeSeled.parent_parent_name = res.data.data.rows[0].parent_name
            this.equipTreeSeled.parent_name = '--'
            this.equipTreeSeled.number = res.data.data.rows[0].number
            this.getSecLeveList(res.data.data.rows[0], 0)
            res.data.data.rows.forEach((e, idx) => {
              this.equipTree.push({
                title: e.parent_name,
                parent_parent_name: e.parent_name,
                parent_name: '--',
                number: e.number,
                selected: idx === 0 ? true : undefined,
                render: (h, { root, node, data }) => { // root根节点, node当前节点, data
                  return h('span', {
                    style: {
                      display: 'inline-block',
                      width: '100%'
                    },
                    on: {
                      click: () => {
                        this.listQuery.pageNo = 1
                        this.getSecLeveList(e, idx)
                      }
                    }
                  }, [
                    h('span', [
                      h('Icon', {
                        props: {
                          type: 'ios-home-outline'
                        },
                        style: {
                          marginRight: '8px'
                        },
                        on: {
                          click: () => {
                            this.getSecLeveList(e, idx)
                          }
                        }
                      }),
                      h('span', data.title)
                    ])
                  ])
                },
                children: [{
                  title: ''
                }]
              })
            })
          }
        }
      })
    },
    modalShowHide (val) {
      if (!val) {
        // this.formInline = {}
        // this.equipModalType = ''
      }
    },
    getSecLeveList (row, idx) {
      this.listQuery.number = row.number
      this.listQuery.level = 4
      this.listQuery.status = 'C' // 只显示已审核数据
      equipListLevelMore(this.listQuery).then(res => { // 获取设备二级
        if (res.data.status) {
          this.equipList = res.data.data.rows
          this.total = res.data.data.totalCount
        }
      })
    },
    // 获取三级设备
    getTriLeveList (row, idx, tidx) {
      this.listQuery.number = row.number
      this.listQuery.level = 5
      this.listQuery.status = 'C' // 只显示已审核数据
      equipListLevelMore(this.listQuery).then(res => { // 获取设备三级
        if (res.data.status) {
          this.equipList = res.data.data.rows
          this.total = res.data.data.totalCount
        }
      })
    },
    manufacturedateChange (date) {
      this.formInline.xtgf_manufacturedate = date
    },
    // 树节点点击事件
    treeSelect (row) {
      if (row.length > 0) {
        this.equipTreeSeled.parent_parent_name = row[0].parent_parent_name
        this.equipTreeSeled.parent_name = row[0].parent_name
        this.equipTreeSeled.number = row[0].number
      }
    },
    toggleExpand (row) {
      if (row.number.length > 6) return
      this.listQuery.number = row.number
      this.listQuery.level = row.number.length > 6 ? 5 : 4
      let data = {
        number: row.number,
        level: row.number.length > 6 ? 5 : 4,
        status: 'C',
        pageSize: 10000,
        pageNo: 1
      }
      equipListLevelMore(data).then(res => { // 获取设备二级
        if (res.data.status) {
          if (res.data.data.rows.length > 0) {
            row.children = []
            res.data.data.rows.forEach((item, idx) => {
              row.children.push({
                parent_parent_name: item.parent_parent_name,
                parent_name: item.name,
                title: item.name,
                number: item.number,
                render: (h, { root, node, data }) => { // root根节点, node当前节点, data
                  return h('span', {
                    style: {
                      display: 'inline-block',
                      width: '100%'
                    },
                    on: {
                      click: () => {
                        if (item.number.length > 8) {
                          this.equipList = []
                          this.total = 0
                          return
                        }
                        this.listQuery.pageNo = 1
                        this.listQuery.number = item.number
                        this.listQuery.level = item.number.length > 6 ? 5 : 4
                        this.getequipList()
                      }
                    }
                  }, [
                    h('span', [
                      h('span', data.title)
                    ])
                  ])
                }
              })
            })
          } else {
            row.children = [{
              title: '暂无数据',
              render: (h, { root, node, data }) => {
                return h('span', {}, '暂无数据')
              }
            }]
          }
          setTimeout(() => {
            this.$forceUpdate()
          }, 100)
        }
      })
    },
    // 获取查询设备列表
    getEquipSearchList () {
      this.loading = true
      let _param = {
        level: 3,
        name: this.setSearchData.name.value || '',
        status: this.listQuery.status,
        pageNo: this.listQuery.pageNo,
        pageSize: this.listQuery.pageSize,
        parent_name: this.ship_name, // 2023年10月8号加
        parent_parent_name: this.ship_name // 2023年10月8号加
      }
      equipListLevelSearch(_param).then(res => { // 获取设备列表
        this.loading = false
        if (res.data.status) {
          this.equipList = res.data.data.rows
          this.total = res.data.data.totalCount
        }
      })
    },
    // 获取设备列表
    getequipList (d) {
      this.loading = true
      this.listQuery.status = 'C' // 只显示已审核数据
      equipListLevelMore(this.listQuery).then(res => { // 获取设备列表
        this.loading = false
        if (res.data.status) {
          this.equipList = res.data.data.rows
          this.total = res.data.data.totalCount
        }
      })
    },
    // 新增设备
    handleEdit () {
      if (this.equipTreeSeled.parent_parent_name === undefined) {
        this.$Message.error('请先选择设备分类或设备')
      } else {
        this.titleModal = '新增设备信息'
        this.equipModal = true
        this.equipModalType = 'add'
        this.formInline = {}
        this.formInline.parent_parent_name = this.equipTreeSeled.parent_parent_name
        this.formInline.parent_name = this.equipTreeSeled.parent_name
        this.formInline.number = this.equipTreeSeled.number
      }
    },
    // 编辑设备信息
    tableClick (list) {
      this.equipModal = true
      this.equipModalType = 'modify'
      this.titleModal = '编辑设备信息'
      this.formInline = list
      this.formInline.parent_parent_name = this.equipTreeSeled.parent_parent_name
      this.formInline.parent_name = this.equipTreeSeled.parent_name
      this.formInline.number = list.number
    },
    // 计算新增当前子级备件序号
    async getCurEquitNumber () {
      let _backNumber = ''
      if (this.equipModalType === 'modify') {
        _backNumber = this.formInline.number
      }
      if (this.equipModalType === 'add') {
        let _query = {
          status: ['A', 'B', 'C'], // 特殊，要拉取所有数据才能知道最大的number，再做自增1
          number: this.formInline.number,
          level: this.formInline.number.length === 6 ? 4 : 5,
          pageSize: 1,
          pageNo: 1
        }
        let _equipList = []
        await equipListLevelMore(_query).then(res => { // 获取设备列表  只获取一条便于加1
          if (res.data.status) {
            _equipList = res.data.data.rows
            if (_equipList.length === 0) { // 子级无数据时默认加'01'
              _backNumber = this.formInline.number + '01'
            } else { // 子级有数据时获取最后一个子级编码加1
              let _lastNumber = parseInt(_equipList[0].number) + 1
              if (this.formInline.number.substring(0, 1) === '0') {
                _backNumber = '0' + _lastNumber
              } else {
                _backNumber = _lastNumber + ''
              }
            }
          }
        })
      }
      return _backNumber
    },
    formatDate(date) {
      const _date = new Date(date)
      const _year = _date.getFullYear()
      const _month = String(_date.getMonth() + 1).padStart(2, '0')
      const _day = String(_date.getDate()).padStart(2, '0')
      const backStr = _year + '-' + _month + '-' + _day
      return backStr
    },
    // 保存
    async handleSubmit (name) {
      let _curNumber = await this.getCurEquitNumber()
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.$Modal.confirm({
            title: '提示',
            content: '<p>确认保存设备信息？</p>',
            loading: true,
            onOk: () => {
              console.log(this.formInline.xtgf_manufacturedate)
              let _param = {
                data: [{
                  number: _curNumber,
                  name: this.formInline.name,
                  description: this.formInline.description,
                  parent_number: this.equipModalType === 'modify' ? this.formInline.parent_number : this.formInline.number,
                  xtgf_manufacturer: this.formInline.xtgf_manufacturer,
                  creator_number: 'ID-000023',
                  createorg_number: 'xt',
                  status: 'A',
                  xtgf_cgequipmentmodel: this.formInline.xtgf_cgequipmentmodel,
                  xtgf_factoryno: this.formInline.xtgf_factoryno,
                  xtgf_technicalparameter: this.formInline.xtgf_technicalparameter,
                  xtgf_drawingno: this.formInline.xtgf_drawingno,
                  xtgf_certificateno: this.formInline.xtgf_certificateno,
                  xtgf_equipmentlevelid: this.formInline.xtgf_equipmentlevelid,
                  standard_number: 'JBFLBZ',
                  xtgf_boatid: [
                    { number: this.ship_id }
                  ]
                }]
              }
              if(this.formInline.xtgf_manufacturedate) {
                Object.assign(_param.data[0], {
                  xtgf_manufacturedate: this.formatDate(this.formInline.xtgf_manufacturedate)
                })
              }
              // xtgf_manufacturedate: this.formInline.xtgf_manufacturedate ? this.formatDate(this.formInline.xtgf_manufacturedate) : '',
              if (this.equipModalType === 'modify') {
                Object.assign(_param.data[0], {
                  id: this.formInline.id
                })
              }
              let dataParam = {
                data: JSON.stringify(_param),
                url: '/ierp/kapi/v2/xtgf/basedata/bd_materialgroup/saveownWL'
              }
              if (this.equipModalType === 'add') {
                API.transferStation(dataParam).then(res => {
                  if (res.data.Result.status) {
                    this.loading = false
                    this.$Modal.remove()
                    this.submitSort(res.data.Result.data.result[0].id, 'add')
                  } else {
                    this.loading = false
                    this.$Modal.remove()
                    this.$Message.error(res.data.Message)
                  }
                })
              }
              if (this.equipModalType === 'modify') {
                let dataId = {
                  data: {
                    id: this.formInline.id
                  }
                }
                let backExamDataParam = {
                  data: JSON.stringify(dataId),
                  url: '/ierp/kapi/v2/xtgf/basedata/bd_materialgroup/GoodsUnaudit'
                }
                API.transferStation(backExamDataParam).then(res => { // 反审核
                  if (res.data.Result.status) {
                    API.transferStation(dataParam).then(res => {
                      if (res.data.Result.status) {
                        this.loading = false
                        this.$Modal.remove()
                        this.submitSort(res.data.Result.data.result[0].id, this.equipModalType)
                      } else {
                        this.loading = false
                        this.$Modal.remove()
                        this.$Message.error(res.data.Result.message)
                      }
                    })
                  } else {
                    this.$Message.error(res.data.Result.message)
                  }
                })
              }
            }
          })
        } else {
          this.$Message.error(res.data.Result.message)
        }
      })
    },
    // 关闭弹窗
    handleCancel () {
      this.modalType = ''
      this.equipModal = false
      this.$refs['formInline'].resetFields()
    },
    // 删除设备管理列表
    handleDelete (row) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>删除后无法恢复，是否确认删除？</p>',
        loading: true,
        onOk: () => {
          let dataId = {
            data: {
              id: row.id
            }
          }
          let dataParam = {
            data: JSON.stringify(dataId),
            url: '/ierp/kapi/v2/xtgf/basedata/bd_materialgroup/GoodsUnaudit'
          }
          API.transferStation(dataParam).then(res => {
            if (res.data.Result.status) {
              API.delMaterialClassi({ id: row.id }).then(res => {
                if (res.data.status) {
                  this.loading = false
                  this.$Modal.remove()
                  this.$Message.success('删除成功！')
                  this.getequipList()
                } else {
                  this.loading = false
                  this.$Modal.remove()
                  this.$Message.error('删除失败！')
                }
              })
            }
          })
        }
      })
    },
    // 附件上传
    handleUpload (d) {},
    getFileId () {
      this.wps_ids = this.fileDataList.map(item => {
        return item.id
      }).join()
    },
    // 船名搜索
    shipChange (name) {
      this.ship_name = name
      localStorage.setItem('equip_ship_name', name)
      this.ship_id = this.shipList.filter(item => { return item.name === name })[0].number
      this.equipTree = []
      this.getEquipListLevelThree()
    },
    // 查询
    searchResults (e) {
      this.listCurrent = 1
      this.listQuery.pageNo = 1
      if (this.setSearchData.name.value !== '') {
        this.getEquipSearchList()
      } else {
        this.getequipList()
      }
    },
    // 重置
    resetResults () {
      this.listCurrent = 1
      this.listQuery.pageSize = 10
      this.listQuery.pageNo = 1
      this.setSearchData.name.value = ''
      if (this.setSearchData.name.value !== '') {
        this.getEquipSearchList()
      } else {
        this.getequipList()
      }
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      if (this.setSearchData.name.value !== '') {
        this.searchResults(this.setSearchData.name.value)
        this.getEquipSearchList()
      } else {
        this.getequipList()
      }
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageNo = val
      if (this.setSearchData.name.value !== '') {
        this.searchResults(this.setSearchData.name.value)
        this.getEquipSearchList()
      } else {
        this.getequipList()
      }
    },
    // 获取分类列表
    getClassiList () {
      this.loading1 = true
      API.getEquipList(this.classiQuery).then(res => {
        this.loading1 = false
        if (res.status === 200) {
          this.classiList = res.data.data.rows
        } else {}
      })
    },
    // 提交
    submitSort (id, type) {
      let dataId = {
        data: {
          id: id
        }
      }
      let submitDataParam = {
        data: JSON.stringify(dataId),
        url: '/ierp/kapi/v2/xtgf/basedata/bd_materialgroup/GoodsSubmit'
      }
      let examDataParam = {
        data: JSON.stringify(dataId),
        url: '/ierp/kapi/v2/xtgf/basedata/bd_materialgroup/GoodsAudit'
      }
      API.transferStation(submitDataParam).then(res => { // 提交
        if (res.data.Result.status) {
          API.transferStation(examDataParam).then(res => { // 审核
            if (res.data.Result.status) {
              if (type === 'add') this.$Message.success('新增成功。')
              if (type === 'modify') this.$Message.success('修改成功。')
              this.clearData()
              this.handleCancel()
              this.getEquipListLevelThree() // 左侧树刷新
              if (this.setSearchData.name.value !== '') {
                this.searchResults(this.setSearchData.name.value)
                this.getEquipSearchList()
              } else {
                this.getequipList()
              }
            }
          })
        }
      })
    },
    // 撤销提交
    recallSort () {
      let idx = this.selectionData.findIndex(item => item.status !== 'B')
      if (idx < 0) {
        if (this.selectedIds.length === 0) {
          this.$Message.warning('请选择要执行的数据。')
        } else {
          let dataId = {
            data: {
              id: this.selectedIds
            }
          }
          let dataParam = {
            data: JSON.stringify(dataId),
            url: '/ierp/kapi/v2/xtgf/basedata/bd_materialgroup/GoodsUnsubmit'
          }
          API.transferStation(dataParam).then(res => {
            if (res.data.Result.status) {
              this.$Message.success('撤销成功。')
              this.clearData()
              this.getequipList()
            }
          })
        }
      } else {
        this.$Message.error('只有已提交的数据才能撤销。')
      }
    },
    // 审核
    auditSort () {
      let idx = this.selectionData.findIndex(item => item.status !== 'B')
      if (idx < 0) {
        if (this.selectedIds.length === 0) {
          this.$Message.warning('请选择要执行的数据。')
        } else {
          let dataId = {
            data: {
              id: this.selectedIds
            }
          }
          let dataParam = {
            data: JSON.stringify(dataId),
            url: '/ierp/kapi/v2/xtgf/basedata/bd_materialgroup/GoodsAudit'
          }
          API.transferStation(dataParam).then(res => {
            if (res.data.Result.status) {
              this.$Message.success('审核成功。')
              this.clearData()
              this.getequipList()
            }
          })
        }
      } else {
        this.$Message.error('只有已提交的数据才能审核。')
      }
    },
    // 反审核
    unAuditSort () {
      let idx = this.selectionData.findIndex(item => item.status !== 'C')
      if (idx < 0) {
        if (this.selectedIds.length === 0) {
          this.$Message.warning('请选择要执行的数据。')
        } else {
          let dataId = {
            data: {
              id: this.selectedIds
            }
          }
          let dataParam = {
            data: JSON.stringify(dataId),
            url: '/ierp/kapi/v2/xtgf/basedata/bd_materialgroup/GoodsUnaudit'
          }
          API.transferStation(dataParam).then(res => {
            if (res.data.Result.status) {
              this.$Message.success('反审核成功。')
              this.clearData()
              this.getequipList()
            }
          })
        }
      } else {
        this.$Message.error('只有已审核的数据才能反审核。')
      }
    },
    // 清除数据
    clearData () {
      this.formInline = {}
      this.selectedIds = []
      this.selectionData = []
    },
    // 取消勾选
    tableSelectCancel (selection, row) {
      this.selectAll = false
      this.selectionData = selection
      if (selection.length === 0) {
        this.selectedIds = []
      } else {
        this.selectedIds.forEach((e, idx) => {
          if (e === row.id) {
            this.selectedIds.splice(idx, 1)
          }
        })
      }
    },
    // 选中勾选触发
    tableSelectAll (selection, row) {
      if (selection.length === this.equipList.length) this.selectAll = true
      this.selectionData = selection
      if (row === undefined) {
        selection.map(e => {
          this.selectedIds.push(e.id)
        })
      } else {
        this.selectedIds.push(row.id)
      }
    },
    // 数据状态选择
    statusChange (val) {
      this.listQuery.status = val.length === 0 ? ['A', 'B', 'C'] : val
      if (this.setSearchData.name.value !== '') {
        this.searchResults(this.setSearchData.name.value)
        this.getEquipSearchList()
      } else {
        this.getequipList()
      }
    },
    // tab切换
    tabClick (name) {
      if (name === 'tab1') {
        // this.getequipList()
      } else {
        this.getClassiList()
      }
    },
    // 树节点
    renderContent (h, { root, node, data }) {
    //   return h('span', {
    //     style: {
    //       display: 'inline-block',
    //       width: '100%'
    //     },
    //     on: {
    //       click: () => {
    //         // this.getequipList(data)
    //       }
    //     }
    //   }, [
    //     h('span', [
    //       h('Icon', {
    //         props: {
    //           type: 'ios-folder-outline'
    //         },
    //         style: {
    //           marginRight: '8px'
    //         },
    //         on: {
    //           click: () => {
    //             // this.getequipList(data)
    //           }
    //         }
    //       }),
    //       h('span', data.title)
    //     ])
    //   ])
    }
  }
}
</script>
<style lang="less" scoped>
.col-text {
  line-height: 40px;
  border-bottom: 1px solid #ccc;
  font-weight: 700;
  font-size: 16px;
  color: #000;
  text-align: center;
}
.search-vie {
  display: inline-block;
  vertical-align: middle;
  margin-left: 5px;
}
.tr-name {
  font-size: 16px;
  color: #333;
  font-weight: 700;
  margin: 15px 0;
  &&::before {
    content: "";
    font-size: 16px;
    padding-right: 10px;
    border-left: 4px solid #1d6ced;
  }
}
.form-class-div {
  border-top: 1px solid #ccc;
  border-left: 1px solid #ccc;
}
.but_on {
  margin: 0 5px;
  position: relative;
  display: inline-block;
  border-radius: 5px;
  z-index: 999;
  line-height: 32px;
  color: #fff;
  background-color: #2d8cf0;
  button.ivu-btn-text {
    color: #fff;
    padding: 0 0 0 10px;
    &:hover {
      background-color:transparent;
    }
  }
  .ivu-dropdown {
    padding: 0 5px;
  }
}
</style>
<style>
.ship_title {
  position: absolute;
  font-size: 20px;
  top: 10px;
  display: inline-block;
  text-align: center;
  width: 100%;
  font-weight: bold;
  color: #17233d;
}
.tree {
  max-height: 600px;
  overflow: auto;
}
.tree.ivu-tree ul li {
  margin: 5px 0;
}
.tree .ivu-tree-title span {
  font-size: 16px;
  color: #666;
}
.ivu-form .form-class-div .ivu-col.ivu-col-span-12, .ivu-form .form-class-div > .ivu-row > .ivu-form-item {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-bottom: 1px solid #ccc;
  border-right: 1px solid #ccc;
}
.ivu-form .form-class-div .ivu-form-item-label {
  background-color: #eee;
  padding: 0 8px;
  font-size: 12px;
  font-weight: 700;
  color: #333;
  display: inline-block;
  line-height: 40px;
  border-right: 1px solid #ccc;
}
.form-class-div .ivu-form-item {
  margin: 0;
  line-height: 40px;
  width: 100%;
}
.ivu-form .form-class-div .ivu-form-item-content {
  padding: 0 10px;
  margin-top: 3px;
}
</style>

<template>
  <Card>
    <Tabs @on-click="tabClick">
      <TabPane label="航海资料" name="tab1">
        <Row>
          <Col span="4">
            <Button type="primary" @click="handleEdit">新增</Button>
          </Col>
          <Col span="20" style="text-align: right;">
            <Dropdown @on-click="shipChange">
              <a href="javascript:void(0)" v-html="ship_name !== '' ? ship_name : '请选择'" style="color: #515a6e;"></a>
              <Icon type="md-arrow-dropdown"></Icon>
              <Dropdown-menu slot="list">
                <Dropdown-item v-for="(item, idx) in shipList" :name="item.ship_name">{{item.ship_name}}</Dropdown-item>
              </Dropdown-menu>
            </Dropdown>
            <Checkbox-group v-model="tableColumnsChecked" @on-change="changeTableColumns" style="display: inline-block;">
              <Checkbox label="isOld">查看旧版本海图</Checkbox>
              <Checkbox label="isNews">查看最新版本航海资料</Checkbox>
            </Checkbox-group>
            <search @searchResults="searchResults" :setSearch="setSearchData" @resetResults="resetResults" class="search-vie"></search>
          </Col>
        </Row>        
        <Row>
          <Col span="4" style="border: 1px solid #ccc;">
            <h3 class="col-text">航海资料分类</h3>
            <Tree :data="dataTree" :render="renderContent" class="tree"></Tree>
          </Col>
          <Col span="20" style="padding-left: 10px;">
            <Table border :loading="loading" :columns="columns" :data="list" @on-row-click="tableClick"></Table>
            <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
            :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
          </Col>
        </Row>
      </TabPane>
      <TabPane label="航海资料分类" name="tab2">
        <p style="text-align: right;margin-bottom: 10px;">
          <Button type="primary" @click="handleClassify">新增</Button>
        </p>
        <Table border
          :loading="loading1"
          :columns="classifyColumns"
          :data="classifyList"
          row-key="id"
          maxHeight="700"
          :draggable="true"
          @on-drag-drop="onDragDrop"
          class="tables-tree"></Table>
      </TabPane>
    </Tabs>
    <!-- 航海资料弹窗内容 -->
    <Modal v-model="infoModal" :title="titleModal" @on-ok="handleSubmit('formInline')" @on-cancel="handleCancel" width="50%" :mask-closable="false" id="form_items">
      <Form ref="formInline" :model="formInline" :rules="ruleInline" inline :label-width="140">
        <div class="form-class-div">
          <Row>
            <Col span="12">
              <Form-item prop="name" label="航海资料名称">
                <Input type="text" v-model="formInline.name" placeholder="请输入"></Input>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item label="图号/图书号">
                <Input type="text" v-model="formInline.code" placeholder="请输入"></Input>
              </Form-item>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <Form-item label="航海资料英文名称">
                <Input type="text" v-model="formInline.name" placeholder="请输入"></Input>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item prop="sort" label="所属分类">
                <Select v-model="formInline.sort" placeholder="请选择">
                  <Option v-for="(item, idx) in sortSeleList" :key="idx" :value="item.id">{{ item.name }}</Option>
                </Select>
              </Form-item>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <Form-item label="单位">
                <Dropdown trigger="custom" :visible="visible">
                  <div :class="visible ? 'drop_down_sel sel_focu' : 'drop_down_sel'" :style="{width: `${formItemWidth}px`}">
                    <a href="javascript:void(0)" @click="handleOpen" v-html="unit_name === '' ? '请选择' : unit_name"></a>
                    <Icon type="md-arrow-dropdown" color='#333'></Icon>
                  </div>
                  <Dropdown-menu slot="list" :style="{width: `${formItemWidth}px`}">
                    <Dropdown-item v-for="(item, idx) in unitList" :name="item.name" style="padding: 7px 10px">
                      <span v-if="item.isEdit" @click="seleClick(item)" style="width: 75%; display: inline-block;">{{ item.name }}</span>
                      <span v-else><Input type="text" v-model="item.value" style="width: 75%;"></Input></span>
                      <span class="positon-span">
                        <Icon @click="editBtn(item)" type="ios-brush-outline" v-if="item.isEdit"></Icon>
                        <Icon @click="saveBtn(item)" type="md-checkmark" v-else></Icon>
                        <Icon @click="removeBtn(idx)" type="ios-trash-outline"></Icon>
                      </span>
                    </Dropdown-item>
                    <Button @click="handleAdd" style="margin: 5px 4%;width: 92%;">新增</Button>
                  </Dropdown-menu>
                </Dropdown>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item label="单价">
                <Input-number v-model="formInline.price" placeholder="请输入" style="width:50%"></Input-number>
                <Select v-model="formInline.price_unit" placeholder="请选择" style="width:49%;float:right;">
                  <Option value="元">元</Option>
                  <Option value="卢布">卢布</Option>
                  <Option value="美元">美元</Option>
                  <Option value="英镑元">英镑</Option>
                  <Option value="日元">日元</Option>
                  <Option value="港元">港元</Option>
                  <Option value="欧元">欧元</Option>
                  <Option value="新西兰元">新西兰元</Option>
                  <Option value="新加坡元">新加坡元</Option>
                  <Option value="泰铢">泰铢</Option>
                </Select>
              </Form-item>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <Form-item label="船存数量">
                <Input-number v-model="formInline.num" style="width: 100%;"></Input-number>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item label="船存版本">
                <Date-picker type="month" placeholder="请选择" style="width: 100%"></Date-picker>
              </Form-item>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <Form-item label="最新版本">
                <Date-picker type="month" placeholder="请选择" style="width: 100%"></Date-picker>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item label="存放地点">
                <Input type="text" v-model="formInline.name" placeholder="请输入"></Input>
              </Form-item>
            </Col>
          </Row>
          <Row>
            <Col span="12">
              <Form-item label="说明" class="remak_la">
                <Input type="textarea" v-model="formInline.name" placeholder="请输入"></Input>
              </Form-item>
            </Col>
            <Col span="12">
              <Form-item label="备注" class="remak_la">
                <Input type="textarea" v-model="formInline.name" placeholder="请输入"></Input>
              </Form-item>
            </Col>
          </Row>
        </div>
      </Form>
    </Modal>
  </Card>
</template>
<script>
import search from '_c/search'
export default {
  components: {
    search
  },
  data () {
    return {
      formItemWidth: null,
      visible: false,
      unit_name: '',
      ship_name: '',
      shipList: [],
      tableColumnsChecked: ['isOld', 'isNews'],
      loading: false,
      total: 0,
      listQuery: {
        pageSize: 10,
        pageIndex: 1
      },
      listCurrent: 1,
      dataTree: [{
        title: '航保部海图',
        render: (h, { root, node, data }) => {
          return h('span', {
            style: {
              display: 'inline-block',
              width: '100%'
            },
            on: {
              click: () => {}
            }
          }, [
            h('span', [
              h('Icon', {
                props: {
                  type: 'ios-home-outline'
                },
                style: {
                  marginRight: '8px'
                }
              }),
              h('span', data.title)
            ])
          ])
        },
        children: []
      }],
      columns: [
        {
          type: 'index',
          title: '序号',
          width: 70,
          align: 'center'
        },
        {
          title: '图号/图书号',
          key: 'code',
          sortable: true,
          maxWidth: 180,
          align: 'center'
        },
        {
          title: '名称',
          key: 'name',
          sortable: true,
          align: 'center'
        },
        {
          title: '船存版本',
          key: '',
          width: 120,
          align: 'center'
        },
        {
          title: '单位',
          key: '',
          width: 70,
          align: 'center'
        },
        {
          title: '说明',
          key: '',
          align: 'center'
        },
        {
          title: '船存数量',
          key: '',
          width: 100,
          align: 'center'
        },
        {
          title: '操作',
          key: '',
          width: 100,
          align: 'center',
          render: (h, params) => {
            return h('Button', {
              props: {
                icon: 'md-trash',
                size: 'small'
              },
              on: {
                click: (e) => {
                  e.stopPropagation()
                  this.handleDelete(params.row)                  
                }
              }
            })
          }
        }
      ],
      infoModal: false,
      titleModal: '',
      list: [{
        name: '舷窗',
        code: '1',
        sort_id: '11',
        id: '001'
      }, {
        name: '舷窗',
        code: '1',
        sort_id: '12',
        id: '001'
      }],
      setSearchData: {
        member_name: {
          type: 'text',
          label: '共条结果',
          width: 180,
          value: '',
          isdisable: false,
          placeholder: '请输入物料名称、物料编码、规格'
        }
      },
      formInline: {},
      ruleInline: {
        name: [{ required: true, message: '此处不能为空!', trigger: 'blur' }],
        code: [{ required: true, message: '此处不能为空!', trigger: 'blur' }],
        sort: [{ required: true, message: '此处不能为空!', trigger: 'change' }]
      },
      sortSeleList: [], // 分类下拉选项
      unitList: [{
        id: '1',
        name: '次',
        isEdit: true,
      }],
      // 分类
      loading1: false,
      shipSelList: [{
        id: '5',
        name: '兴通789'
      }], // 适用船舶下拉
      classifyList: [{
        classi_name: '救命救难用具',
        id: '1',
        ship_name: '',
        children: [],
        _showChildren: true
      }, {
        classi_name: '救命救难用具、消火器类',
        id: '2',
        children: [],
        ship_name: '',
        _showChildren: true
      }],
      keyType: false, // 子列表编辑保存状态
      classifyColumns: [
        {
          type: 'indexMethod',
          title: '序号',
          width: 80,
          tree: true,
          align: 'left',
          render: (h, params) => {
            if (params.row.key === undefined) {
              return h('span', params.index + 1)
            } else {
              return h('span', params.row.key)
            }
          }
        },
        {
          title: '分类编码',
          key: 'code',
          width: 120,
          align: 'center',
          render: (h, params) => {
            const inp = h('Input', {
              on: {
                input: (val) => {
                  this.currentVal = val
                }
              }
            }, params.row.code)
            return this.currentId === params.row.id ? inp : h('span', params.row.code)
          }
        },
        {
          title: '分类名称',
          key: 'classi_name',
          align: 'center',
          width: 300,
          render: (h, params) => {
            const inp = h('Input', {
              on: {
                input: (val) => {
                  this.currentVal = val
                }
              }
            }, params.row.classi_name)
            return this.currentId === params.row.id ? inp : h('span', params.row.classi_name)
          }
        },
        {
          title: '适用船舶',
          align: 'center',
          key: 'ship_name',
          render: (h, params) => {
            const inp = h('Select', {
              props: {
                'label-in-value': true,
                value: params.row.id
              },
              on: {
                'on-change': (val) => {
                  this.currentVal = val.label
                }
              }
            },
            this.shipSelList.map(item => {
              return h('Option', {
                props: {
                  value: item.id,
                  label: item.name
                }
              })
            })
          )
          return this.currentId === params.row.id ? inp : h('span', params.row.ship_name)
          }
        },
        {
          title: '显示排序',
          key: '',
          width: 100,
          align: 'center',
          render: (h, params) => {
            return h('Button', {
              props: {
                icon: 'ios-paper',
                size: 'small'
              },
              on: {
                change: () => {
                  this.getClassiList()
                }
              }
            })
          }
        },
        {
          title: '操作',
          width: 155,
          align: 'center',
          render: (h, params) => {
            let parense = [
              h('Button', {
                props: {
                  icon: this.keyType ? 'md-brush' : 'ios-folder',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.keyType === !this.keyType
                    if (!this.keyType) {
                      this.handleSave(params.row)
                    }
                  }
                }
              }),
              h('Button', {
                props: {
                  icon: 'md-add',
                  size: 'small'
                },
                style: {
                  margin: '0 5px'
                },
                on: {
                  click: () => {
                    this.addListChild(params.row)
                  }
                }
              }),
              h('Button', {
                props: {
                  icon: 'md-trash',
                  size: 'small'
                },
                style: {
                  margin: '0 5px'
                },
                on: {
                  click: () => {
                    this.handleDelete(params.row, 'classify')
                  }
                }
              })
            ]
            let children = [
              h('Button', {
                props: {
                  icon: this.keyType ? 'md-brush' : 'ios-folder',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.keyType === !this.keyType
                    if (!this.keyType) {
                      this.handleSaveChild(params.row)
                    }
                  }
                }
              }),
              h('Button', {
                props: {
                  icon: 'md-trash',
                  size: 'small'
                },
                style: {
                  margin: '0 5px'
                },
                on: {
                  click: () => {
                    this.handleDelete(params.row, 'classify')
                  }
                }
              })
            ]
            return h('div', params.row.key === undefined ? parense : children)
          }
        }
      ],
      currentId: '', // 当前编辑的id
      currentVal: '', // 存储当前编辑的值
    }
  },
  created () {
    this.getlist(this.dataTree[0].id)
  },
  methods: {
    // 航海资料
    // 左侧分类树
    renderContent (h, { root, node, data }) {
      return h('span', {
        style: {
          display: 'inline-block',
          width: '100%'
        },
        on: {
          click: () => {
            this.getlist(data.id)
          }
        }
      }, [
        h('span', [
          h('Icon', {
            props: {
              type: 'ios-folder-outline'
            },
            style: {
              marginRight: '8px'
            }
          }),
          h('span', data.title)
        ])
      ])
    },
    // 获取资料列表
    getlist (d) {
      this.listQuery.sort_id = d
      // this.loading = true
      // API(this.listQuery).then(res => {
      //   if (res.data.Code === 10000) {
      //     this.loading = false
      //     this.list = res.data.Result
      //     this.total = res.data.Total
      //   } else {
      //     this.$Message.error(res.data.Message)
      //   }
      // })
    },
    // 新增航海资料
    handleEdit () {
      this.titleModal = '新增航海资料基本信息'
      this.infoModal = true
      this.formItemWidth = (document.getElementById('form_items').clientWidth / 2 -32) / 2 - 160
    },
    // 编辑航海资料
    tableClick (list) {
      this.infoModal = true
      this.titleModal = '编辑航海资料基本信息'
      this.formInline = list
      this.formItemWidth = (document.getElementById('form_items').clientWidth / 2 -32) / 2 - 160
    },
    // 船名搜索
    shipChange (name) {
      this.ship_name = name
    },
    // 勾选Checkbox触发
    changeTableColumns () {
      this.tableColumns2 = this.getTable2Columns();
    },
    // 查询
    searchResults (e) {
      this.listCurrent = 1
      this.listQuery.pageIndex = 1
      this.getlist()
    },
    // 重置
    resetResults () {
      this.listCurrent = 1
      this.listQuery = {
        pageSize: 10,
        pageIndex: 1
      }
      this.getlist(this.dataTree[0].id)
    },
    // 保存航海资料
    handleSubmit (name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.$Modal.confirm({
            title: '提示',
            content: '<p>确认保存航海资料信息？</p>',
            loading: true,
            onOk: () => {
              API(this.formInline).then(res => {
                if (res.data.Code === 10000) {
                  this.loading = false
                  this.$Modal.remove()
                  this.$Message.success(res.data.Message)
                  this.$refs['name'].resetFields()
                  this.getlist(this.dataTree[0].id)
                } else {
                  this.loading = false
                  this.$Modal.remove()
                  this.$Message.error(res.data.Message)
                }
              })
            }
          })
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 删除列表
    handleDelete (row, type) {
      if (type && row.id === '') {
        this.classifyList.splice(row, 1)
      } else {
        this.$Modal.confirm({
          title: '提示',
          content: '<p>删除后无法恢复，是否确认删除？</p>',
          loading: true,
          onOk: () => {
            if (type) { // 航海资料分类列表
              // API.({ id: row.id }).then(res => {
              //   if (res.data.Code === 10000) {
              //     this.loading = false
              //     this.$Modal.remove()
              //     this.$Message.success(res.data.Message)
              //     this.getlist(this.dataTree[0].id)
              //   } else {
              //     this.loading = false
              //     this.$Modal.remove()
              //     this.$Message.error(res.data.Message)
              //   }
              // })
            } else { // 航海资料列表
              // API.({ id: row.id }).then(res => {
              //   if (res.data.Code === 10000) {
              //     this.loading = false
              //     this.$Modal.remove()
              //     this.$Message.success(res.data.Message)
              //     this.getlist(this.dataTree[0].id)
              //   } else {
              //     this.loading = false
              //     this.$Modal.remove()
              //     this.$Message.error(res.data.Message)
              //   }
              // })
            }
          }
        })
      }
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getlist(this.dataTree[0].id)
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getlist(this.dataTree[0].id)
    },
    // tab切换
    tabClick (name) {
      if (name === 'tab1') {
        this.getlist()
      } else {
        this.getclassifyList()
      }
    },
    // ...航海资料分类...//
    // 获取分类列表
    getclassifyList () {
      // this.loading1 = true
      // API().then(res => {
      //   if (res.data.Code === 10000) {
      //     this.loading1 = false
      //     this.classifyList = res.data.Result
      //   } else {
      //     this.$Message.error(res.data.Message)
      //   }
      // })
    },
    // 新增列表分类
    handleClassify () {
      if (this.classifyList[0].id === '') {
        this.$Message.error('请先保存后，才能操作下一条！')
      } else {
        this.classifyList.unshift({
          id: '',
          sort_name: '',
          classi_name: '',
          children: [],
          _showChildren: true
        })
      }
    },
    // 增加列表子分类
    addListChild (row) {
      let childArr = this.classifyList[row._index].children
      if (childArr.length > 0 && childArr[childArr.length-1].id === '') {
        this.$Message.warning('一次只能操作一行！')
      } else {
        this.classifyList[row._index].children.push({
          id: '',
          code: '',
          classi_name: '',
          ship_name: '',
          key: row._index + 1 + '.' + (this.classifyList[row._index].children.length + 1)
        })
      }
    },
    // 保存分类列表
    handleSave (row) {
      if (row.code === '' || row.classi_name === '' || row.ship_name === '') {
        this.$Message.error('带*号的为必填项')
      } else {
        this.classifyList = this.classifyList.map(v => {
          return v.id === row.id ? { ...v, ...this.currentVal } : v
        })
        this.currentId = ''
        this.currentVal = ''
      }
    },
    // 保存分类子列表
    handleSaveChild (row) {
      if (row.code === '' || row.classi_name === '' || row.ship_name === '') {
        this.$Message.error('有字段为空！')
      } else {
        // this.classifyList = this.classifyList.map(v => {
        //   return v.id === row.id ? { ...v, ...this.currentVal } : v
        // })
        // this.currentId = ''
        // this.currentVal = ''
      }
    },
    // 拖拽排序
    onDragDrop(first, end) {
      //转成int型，方便后续使用
      first = parseInt(first)
      end = parseInt(end)
      let tmp = this.classifyList[first]
      if(first < end) {
        for(var i=first+1; i<=end; i++) {
          this.classifyList.splice(i-1, 1, this.classifyList[i])
        }
        this.classifyList.splice(end, 1, tmp)
      }
      if(first > end) {
        for(var i=first; i>end; i--) {
          this.classifyList.splice(i, 1, this.classifyList[i-1])
        }
        this.classifyList.splice(end, 1, tmp)
      }
      // let idx = this.classifyList.findIndex(e => {return e.id === ''})
      // if (idx > -1) {
      //   this.classifyList.splice(this.classifyList[idx], 1)
      // }
      if (this.classifyList[0].id === '') {
        this.classifyList.splice(this.classifyList[0], 1)
      }
      this.getclassifyList()
    },
    // 编辑单位下拉项
    editBtn (item) {
      item.isEdit = false
    },
    // 保存单位下拉项
    saveBtn (item) {
      item.name = item.value
      item.isEdit = true
    },
    // 删除单位下拉项
    removeBtn (index) {
      this.unitList.splice(index, 1)
    },
    // 增加单位下拉项
    handleAdd () {
      this.unitList.push({
        id: '',
        name: ''
      })
    },
    // 点击单位下拉项触发
    seleClick (item) {
      this.visible = false
      this.unit_name = item.name
      this.formInline.unit = item.name
    },
    // 打开单位下拉
    handleOpen () {
      this.visible = true
    },
    handleCancel () {
      this.visible = false
      this.unitList = []
    }
  }
}
</script>
<style lang="less" scoped>
.drop_down_sel {
  padding: 0 10px;
  border-radius: 5px;
  border: 1px solid #dcdee2;
  a {
    color: #666;
    width: 80%;
    display: inline-block;
  }
  i {
    float: right;
    margin-top: 10px;
  }
}
.drop_down_sel.sel_focux {
  border: 1px solid #2D8cF0;
}
.unit-sele {
  position: relative;
  li {
    height: 33px;
    line-height: 30px;
    padding: 0 13px;
  }
  span {
    display: inline-block;
    font-size: 14px;
  }
}
.positon-span {
  text-align: right;
  margin-left: 5px;
  i {
    padding: 0 5px;
    font-size: 16px;
  }
}
.col-text {
  line-height: 40px;
  border-bottom: 1px solid #ccc;
  font-weight: 700;
  font-size: 16px;
  color: #000;
  text-align: center;
}
.search-vie {
  display: inline-block;
  vertical-align: middle;
  margin-left: 5px;
}
.tr-name {
  font-size: 16px;
  color: #333;
  font-weight: 700;
  margin: 15px 0;
  &&::before {
    content: "";
    font-size: 16px;
    padding-right: 10px;
    border-left: 4px solid #1d6ced;
  }
}
.form-class-div {
  border-top: 1px solid #ccc;
  border-left: 1px solid #ccc;
}
</style>
<style>
.tables-tree .ivu-table-cell-tree {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 1px solid #dcdee2;
  border-radius: 2px;
  background-color: #fff;
  line-height: 12px;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: color .2s ease-in-out,border-color .2s ease-in-out;
  transition: color .2s ease-in-out,border-color .2s ease-in-out;
}
.tables-tree .ivu-table-cell-tree-empty {
  cursor: default;
  color: transparent;
  background-color: transparent;
  border-color: transparent;
}
.tree.ivu-tree ul li {
  margin: 5px 0;
}
.tree .ivu-tree-title span {
  font-size: 16px;
  color: #666;
}
.ivu-form .form-class-div .ivu-col.ivu-col-span-12, .ivu-form .form-class-div > .ivu-row > .ivu-form-item {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-bottom: 1px solid #ccc;
  border-right: 1px solid #ccc;
}
.ivu-form .form-class-div .ivu-form-item-label {
  background-color: #eee;
  padding: 0 8px;
  font-size: 12px;
  font-weight: 700;
  color: #333;
  display: inline-block;
  line-height: 40px;
  border-right: 1px solid #ccc;
}
.form-class-div .ivu-form-item {
  margin: 0;
  line-height: 40px;
  width: 100%;
}
.ivu-form .form-class-div .ivu-form-item-content {
  padding: 0 10px;
  margin-top: 3px;
}
.ivu-form .form-class-div .remak_la .ivu-form-item-label {
  line-height: 58px;
}
</style>

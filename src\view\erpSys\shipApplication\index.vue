<template>
  <div>
    <Card>
      <search @searchResults="searchResults" @selectOnChanged="dateChanged" :setSearch="setSearchData" @resetResults="resetResults" class="search-vie"></search>
      <div v-if="tableList.length > 0" class="export_btn">
        <Button class="copy_btn" style="margin-right: 10px;" type="primary" @click="copySecTxt">复制密钥</Button>
        <Button type="primary" @click="exportUrl">导出</Button>
      </div>
      <Table
        style="margin-top: 10px;"
        border
        ref="selection"
        :loading="loading"
        :columns="columns"
        :data="tableList"></Table>
    </Card>
    <DetailDrawer :modalData="detailDrawer"></DetailDrawer>
  </div>
</template>
<script>
import search from '_c/search'
import DetailDrawer from './applicationDrawer'
import Clipboard from 'clipboard'
import API from '@/api/erpSys/common'
import { getToken, encryptData, decryptData } from '@/libs/util.js'
import config from '@/config'

export default ({
  components: {
    search,
    DetailDrawer
  },
  data () {
    return {
      loading: false,
      baseUrl: process.env.NODE_ENV === 'development' ? config.baseUrl.dev : config.baseUrl.pro,
      token: '',
      listQuery: {
        biztime: '',
        xtgf_supplier_number: ''
      },
      detailDrawer: {
        modal: false,
        data: {}
      },
      setSearchData: {
        biztime: {
          type: 'date',
          label: '申请日期',
          selected: '',
          width: 135,
          value: '',
          isdisable: false
        },
        xtgf_supplier_number: {
          type: 'select',
          label: '供应商',
          selected: '',
          width: 135,
          value: '',
          selectData: [
            { label: 'THOME', value: 'GYGJ000088'},
            { label: 'IMC', value: 'GYGJ002056'}
          ],
          isdisable: false
        }
      },
      columns: [
        {
          title: '船名',
          key: 'xtgf_vesselid_name',
          align: 'center'
        },
        {
          title: '船名(EN)',
          key: 'xtgf_vesselid_xtgf_shipname',
          align: 'center'
        },
        {
          title: '订单编号',
          key: 'billno',
          align: 'center'
        },
        {
          title: '申请日期',
          key: 'biztime',
          align: 'center',
          render: (h, params) => {
            let biztime = params.row.biztime.split(' ')[0]
            return h('div', {}, biztime)
          }
        },
        {
          title: '操作',
          key: '',
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                style: {
                  margin: '0 5px'
                },
                props: {
                  icon: 'md-brush',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleDetail(params.row)
                  }
                }
              }, '查看')
            ])
          }
        }
      ],
      tableList: []
    }
  },
  created() {
    this.listQuery.xtgf_supplier_number = 'GYGJ000088'
    this.setSearchData.xtgf_supplier_number.selected = 'GYGJ000088'
    const currentDate = new Date()
    let prevMonth = currentDate.getMonth()
    let prevYear = currentDate.getFullYear()
    if(prevMonth === 0) {
      prevMonth = 11
      prevYear --
    } else {
      prevMonth--
    }
    const lastDay = new Date(prevYear, prevMonth + 1, 0)
    const formattedDate = `${prevYear}-${(prevMonth + 1).toString().padStart(2, '0')}-${lastDay.getDate().toString().padStart(2, '0')}`
    this.setSearchData.biztime.selected = formattedDate
    this.listQuery.biztime = formattedDate
    this.getList()
  },
  methods: {
    getList () {
      this.tableList = []
      let _url = '/ierp/kapi/v2/xtgf/pm/pm_purapplybill/purchasemonthsearch?pageSize=100&pageNo=1&biztime=' + this.listQuery.biztime + '&xtgf_supplier_number=' + this.listQuery.xtgf_supplier_number
      let dataParam = {
        url: _url,
        user: 'cengyabin'
      }
      API.transferStationGet(dataParam).then(res => {
        if (res.data.errorCode === '0') {
          if (res.data.data.totalCount && res.data.data.totalCount > 0) {
            this.tableList = res.data.data.rows
          } else {
            this.$Message.warning('暂无数据！')
          }
        } else {
          if (!this.listQuery.biztime) {
            // this.$Message.warning('请选择申请日期')
          } else if (!this.listQuery.xtgf_supplier_number) {
            // this.$Message.warning('请选择供应商')
          } else {
            this.$Message.error('数据获取出错，请联系管理员！')
          }

        }
      })
    },
    handleDetail (row) { // 查看详情
      this.detailDrawer.title = row.xtgf_vesselid_name + '(' + row.billno + ')'
      this.detailDrawer.modal = true
      this.detailDrawer.data = row
    },
    exportUrl () { // 导出加密地址
      let encryStr = 't=' + this.listQuery.biztime + '&n=' + this.listQuery.xtgf_supplier_number
      let entryCode = encryptData(encryStr)
      let openUrl = this.baseUrl + 'xt_forms?' + encodeURIComponent(entryCode)
      window.open(openUrl, '_blank')
    },
    copySecTxt () { // 复制密钥
      let _token = getToken()
      let clipboard = new Clipboard('.copy_btn', {
        text: function () {
          return _token
        }
      })
      clipboard.on('success', e => {
        this.$Message.success('复制密钥成功！')
        clipboard.destroy()
      })
      clipboard.on('error', e => {
        this.$Message.error('该浏览器不支持自动复制')
        clipboard.destroy()
      })
    },
    dateChanged (e) {
      if (e.flag === 'date_start') {
        this.listQuery.biztime = e.key
      } else if (e.xtgf_supplier_number) {
        this.listQuery.xtgf_supplier_number = e.xtgf_supplier_number
      } else {
        this.listQuery.xtgf_supplier_number = ''
      }
    },
    // 查询
    searchResults (e) {
      this.getList()
    },
    // 重置
    resetResults () {
      this.setSearchData.biztime.selected = ''
      this.setSearchData.xtgf_supplier_number.selected = ''
      this.listQuery.biztime = ''
      this.listQuery.xtgf_supplier_number = ''
      this.getList()
    }
  }
})
</script>
<style scoped>
  .export_btn {
    position: absolute;
    right: 20px;
    top: 20px;
  }
</style>

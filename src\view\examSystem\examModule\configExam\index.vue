<template>
  <div>
    <Card>
      <div class="edit_top_area">
        <h3>考试配置</h3>
        <Button type="primary" size="small" @click="handleEdit">{{ isEdit ? '保存' : '编辑' }}</Button>
      </div>
      <Form ref="formRef" :model="examObj">
        <FormItem  class="exam_line" label="题目数量：" prop="question_num" required :show-message="false">
          <InputNumber :min="1" :disabled="!isEdit" v-model="examObj.question_num" @on-change="sumScore"/>
          道
        </FormItem>
        <FormItem  class="exam_line" label="考试时长：" prop="paper_total_time" required :show-message="false">
          <InputNumber :min="1" :disabled="!isEdit" v-model="examObj.paper_total_time"/>
          分钟
        </FormItem>
        <FormItem  class="exam_line" label="单题分数：" prop="single_question_score" required :show-message="false">
          <InputNumber :min="1" :disabled="!isEdit" v-model="examObj.single_question_score" @on-change="sumScore"/>
          分
        </FormItem>
        <FormItem  class="exam_line" label="考试总分：" prop="paper_total_score" required :show-message="false">
          <InputNumber :min="1" disabled v-model="examObj.paper_total_score"/>
          分
        </FormItem>
      </Form>
    </Card>
  </div>
</template>
<script>
import API from '@/api/examSystem/examModule/configExam'

export default {
  data () {
    return {
      isEdit: false, // 是否编辑状态
      examObj: {
        question_num: 0, // 题目数量
        paper_total_time: 0, // 总时长
        single_question_score: 0, // 单题分数
        paper_total_score: 0 // 总分
      }, // 考试配置数据
      ruleValidate: { // 校验规则
        question_num: [{ required: true, message: '数据不能为空', trigger: 'blur' }],
        paper_total_time: [{ required: true, message: '数据不能为空', trigger: 'blur' }],
        single_question_score: [{ required: true, message: '数据不能为空', trigger: 'blur' }],
        paper_total_score: [{ required: true, message: '数据不能为空', trigger: 'blur' }]
      }
    }
  },
  methods: {
    getConfigList () {
      API.queryExamConfig().then(res => {
        if (res.data.Code === 10000) {
          if (JSON.stringify(res.data.Result) !== '{}') {
            let _item = res.data.Result
            this.examObj = {
              question_num: parseFloat(_item.question_num), // 题目数量
              paper_total_time: parseFloat(_item.paper_total_time), // 总时长
              single_question_score: parseFloat(_item.single_question_score), // 单题分数
              paper_total_score: parseFloat(_item.paper_total_score) // 总分
            }
          }
        }
      })
    },
    handleEdit () {
      if (this.isEdit) {
        this.$refs['formRef'].validate((valid) => {
          if (valid) {
            API.setExamConfig(this.examObj).then(res => {
              if (res.data.Code === 10000) {
                this.$Message.success(res.data.Message)
                this.isEdit = !this.isEdit
                this.getConfigList()
              } else {
                this.$Message.error(res.data.Message)
              }
            })
          } else {
            this.$Message.warning('请完善红框内数据！')
          }
        })
      } else {
        this.isEdit = !this.isEdit
      }
    },
    sumScore (val) {
      this.examObj.paper_total_score = (parseFloat(this.examObj.single_question_score) * parseFloat(this.examObj.question_num))
    }
  },
  created () {
    this.getConfigList()
  }
}
</script>
<style>
.edit_top_area {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.edit_top_area h3 {
  margin-right: 85px;
}
.exam_line {
  margin-top: 10px;
}
</style>

<template>
  <div>
    <Card>
      <Button icon="md-add" @click="createClassify('create')" class="exam_add_btn">职务类别新增</Button>
      <div v-show="classifyData.length === 0" class="null-data">暂无数据</div>
      <Row :gutter="40" class="classify_list">
        <Col span="6" v-for="(item, idx) in classifyData" :key="idx">
          <div class="list">
            <b>{{ item.post_name }}</b>
            <p>
              <Button type="text" @click="createClassify('update', item)" class="update_btn">编辑</Button>
            </p>
          </div>
        </Col>
      </Row>
    </Card>
    <!-- 类别弹窗内容 -->
    <Modal v-model="classifyModal" :title="classifyModalTitle" :mask-closable="false" width="320">
      <div class="modaldiv">
        <label>职务类别名称：</label>
        <Input v-model="listQuery.post_name" style="width: 200px" />
      </div>
      <div slot="footer">
        <Button type="primary" @click="cancel">取消</Button>
        <Button v-if="curType=='create'" type="primary" @click="createData">保存</Button>
        <Button v-else type="primary" @click="updateData">保存</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import API from '@/api/examSystem/examModule/jobCategory'
export default {
  data () {
    return {
      curType: '',
      classifyData: [],
      classifyModal: false,
      classifyModalTitle: '',
      listQuery: {
        post_name: '',
        question_post_id: ''
      }
    }
  },
  created () {
    this.getList()
  },
  methods: {
    // 获取职务列表
    getList () {
      API.queryExamPostList().then(res => {
        if (res.data.Code === 10000) {
          this.classifyData = res.data.Result
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 开启职务类别弹窗
    createClassify (type, row) {
      this.curType = type
      this.classifyModal = true
      if (type === 'create') {
        this.classifyModalTitle = '职务类别新增'
      } else {
        this.classifyModalTitle = '职务类别编辑'
        this.listQuery = {
          post_name: row.post_name,
          question_post_id: row.question_post_id
        }
      }
    },
    // 保存新增
    createData () {
      if (this.listQuery.post_name === '') return this.$Message.error('职务类别名称不能为空！')
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确认新增职务类别？</p>',
        loading: true,
        onOk: () => {
          API.addExamPost({ post_name: this.listQuery.post_name }).then(res => {
            if (res.data.Code === 10000) {
              this.loading = false
              this.$Modal.remove()
              this.$Message.success(res.data.Message)
              this.getList()
              this.cancel()
            } else {
              this.loading = false
              this.$Modal.remove()
              this.$Message.error(res.data.Message)
            }
          })
        }
      })
    },
    // 修改保存
    updateData () {
      if (this.listQuery.post_name === '') return this.$Message.error('职务类别名称不能为空！')
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确认修改职务类别名称？</p>',
        loading: true,
        onOk: () => {
          API.updateExamPost(this.listQuery).then(res => {
            if (res.data.Code === 10000) {
              this.loading = false
              this.$Modal.remove()
              this.$Message.success(res.data.Message)
              this.getList()
              this.cancel()
            } else {
              this.loading = false
              this.$Modal.remove()
              this.$Message.error(res.data.Message)
            }
          })
        }
      })
    },
    // 关闭类别弹窗
    cancel () {
      this.listQuery = {
        post_name: '',
        question_post_id: ''
      }
      this.classifyModal = false
    }
  }
}
</script>
<style lang="less" scoped>
.null-data {
  padding: 15px 0;
  text-align: center;
}
.classify_list {
  .list {
    padding: 10px 15px;
    border-radius: 5px;
    position: relative;
    margin-bottom: 15px;
    background-color: #EEF4FF;
    b {
      color: #000;
      font-size: 18px;
      display: block;
      margin-bottom: 10px;
    }
    .update_btn {
      color: #1943A9;
      padding: 0;
      font-size: 14px;
    }
    &::after {
      content: '';
      width: 28px;
      height: 28px;
      display: block;
      top: 33%;
      right: 20px;
      position: absolute;
      background: url(../../../../assets/images/port.png) no-repeat center;
    }
  }
}
.exam_add_btn {
  color: #fff;
  border: none;
  font-size: 16px;
  letter-spacing: 2px;
  margin-bottom: 25px;
  padding: 8px 10px;
  background-color: #1943A9;
}
</style>

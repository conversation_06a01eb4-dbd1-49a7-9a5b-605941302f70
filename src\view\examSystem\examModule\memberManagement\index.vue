<template>
  <div>
    <Card>
      <search @searchResults='searchResults' :setSearch='setSearchData' @resetResults='resetResults'></search>
      <div class="extra" slot="extra">
        <formAction :setFormAction='setFormAction' @handleCreate="handleMember('create')"></formAction>
      </div>
    </Card>
    <Card style="margin-top: 10px">
      <Table border :loading="loading" ref="selection" :columns="columns" :data="list"></Table>
      <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
        :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
    </Card>
    <Modal v-model="memberModal" :title="memberModalTitle" :mask-closable="false" width="320">
      <Form ref="formInline" :model="formInline" :rules="ruleValidate" :label-width="90">
        <FormItem prop="member_name" label="姓名：">
          <Input type="text" v-model="formInline.member_name"></Input>
        </FormItem>
        <FormItem prop="member_mobile" label="手机号码：">
          <Input type="text" v-model="formInline.member_mobile" :disabled="disabled"></Input>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="primary" @click="cancel">取消</Button>
        <Button v-if="curType=='create'" type="primary" @click="createData">保存</Button>
        <Button v-else type="primary" @click="updateData">保存</Button>
      </div>
    </Modal>
    <scoreQueryModal :modalData="modalData"></scoreQueryModal>
  </div>
</template>
<script>
import search from '_c/search' // 查询组件
import formAction from '_c/formAction' // 表单操作组件
import scoreQueryModal from './scoreQueryModal'
import API from '@/api/examSystem/examModule/memberManagement'
import { validateMobilePhone } from '@/assets/js/iViewValidate'
export default {
  components: {
    search,
    formAction,
    scoreQueryModal
  },
  data () {
    return {
      curType: '',
      disabled: false,
      memberModal: false,
      memberModalTitle: '',
      formInline: {
        member_id: '',
        member_name: '',
        member_mobile: ''
      },
      ruleValidate: {
        member_name: [{ required: true, message: '姓名不能为空！', trigger: 'blur' }],
        member_mobile: [
          { required: true, message: '手机号码不能为空！', trigger: 'blur' },
          { validator: validateMobilePhone, trigger: 'blur' }
        ]
      },
      modalData: {
        modal: false,
        data: undefined
      },
      loading: false,
      total: 0,
      list: [],
      listCurrent: 1,
      listQuery: {
        member_name: '',
        member_mobile: '',
        pageSize: 10,
        pageIndex: 1
      },
      setFormAction: {
        operation: ['create']
      },
      setSearchData: {
        member_name: {
          type: 'text',
          label: '姓名',
          width: 180,
          value: '',
          isdisable: false
        },
        member_mobile: {
          type: 'text',
          label: '手机号码',
          width: 180,
          value: '',
          isdisable: false
        }
      },
      columns: [
        {
          title: '姓名',
          key: 'member_name',
          align: 'center'
        },
        {
          title: '手机号码',
          key: 'member_mobile',
          align: 'center'
        },
        {
          title: '考试次数',
          key: 'exam_times',
          align: 'center'
        },
        {
          title: '操作',
          align: 'center',
          width: 260,
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: {
                  icon: 'md-paper',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handlePreview(params.row)
                  }
                }
              }, '成绩查询'),
              h('Button', {
                style: {
                  margin: '0 5px'
                },
                props: {
                  icon: 'md-brush',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleMember('update', params.row)
                  }
                }
              }, '编辑'),
              h('Button', {
                style: {
                  margin: '0 5px'
                },
                props: {
                  icon: 'md-trash',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleDelete(params.row.member_id)
                  }
                }
              }, '删除')
            ])
          }
        }
      ]
    }
  },
  created () {
    this.getList()
  },
  methods: {
    // 获取列表
    getList () {
      this.loading = true
      API.queryExamMemberPage(this.listQuery).then(res => {
        if (res.data.Code === 10000) {
          this.loading = false
          this.list = res.data.Result
          this.total = res.data.Total
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 开启新增/编辑成员弹窗
    handleMember (type, row) {
      this.curType = type
      this.memberModal = true
      if (type === 'create') {
        this.memberModalTitle = '添加成员'
      } else {
        this.memberModalTitle = '成员编辑'
        this.disabled = true
        this.formInline = {
          member_id: row.member_id,
          member_name: row.member_name,
          member_mobile: row.member_mobile
        }
      }
    },
    // 新增保存
    createData () {
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          this.$Modal.confirm({
            title: '提示',
            content: '<p>确认新增成员？</p>',
            loading: true,
            onOk: () => {
              API.addMember(this.formInline).then(res => {
                if (res.data.Code === 10000) {
                  this.loading = false
                  this.$Modal.remove()
                  this.$Message.success(res.data.Message)
                  this.getList()
                  this.cancel()
                } else {
                  this.loading = false
                  this.$Modal.remove()
                  this.$Message.error(res.data.Message)
                }
              })
            }
          })
        }
      })
    },
    // 编辑保存
    updateData () {
      this.$refs['formInline'].validate((valid) => {
        if (valid) {
          this.$Modal.confirm({
            title: '提示',
            content: '<p>确认修改成员？</p>',
            loading: true,
            onOk: () => {
              API.updateMember(this.formInline).then(res => {
                if (res.data.Code === 10000) {
                  this.loading = false
                  this.$Modal.remove()
                  this.$Message.success(res.data.Message)
                  this.getList()
                  this.cancel()
                } else {
                  this.loading = false
                  this.$Modal.remove()
                  this.$Message.error(res.data.Message)
                }
              })
            }
          })
        }
      })
    },
    // 关闭新增/编辑成员弹窗
    cancel () {
      this.disabled = false
      this.memberModal = false
      this.formInline = {
        member_id: '',
        member_name: '',
        member_mobile: ''
      }
    },
    // 删除
    handleDelete (d) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确认删除成员？</p>',
        loading: true,
        onOk: () => {
          API.deleteMember({ member_id: d }).then(res => {
            if (res.data.Code === 10000) {
              this.loading = false
              this.$Modal.remove()
              this.$Message.success(res.data.Message)
              this.getList()
            } else {
              this.loading = false
              this.$Modal.remove()
              this.$Message.error(res.data.Message)
            }
          })
        }
      })
    },
    // 成绩查询
    handlePreview (row) {
      this.modalData = {
        modal: true,
        data: row
      }
    },
    // 查询
    searchResults (e) {
      this.listCurrent = 1
      this.listQuery.pageIndex = 1
      this.listQuery.member_name = e.member_name
      this.listQuery.member_mobile = e.member_mobile
      this.getList()
    },
    // 重置
    resetResults () {
      this.listCurrent = 1
      this.listQuery = {
        member_name: '',
        member_mobile: '',
        pageSize: 10,
        pageIndex: 1
      }
      this.setSearchData.member_name.value = ''
      this.setSearchData.member_mobile.value = ''
      this.getList()
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    }
  }
}
</script>
<style lang="less" scoped>
  .ivu-form-item:last-child {
    margin-bottom: 0;
  }
</style>

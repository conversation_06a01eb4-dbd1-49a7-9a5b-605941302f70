<template>
  <div class="tabs_change">
    <Card>
      <Button icon="md-add" @click="createClassify('create')" class="examSys_add_btn">类别新增</Button>
      <Row style="display: flex;">
        <Col span="5" style="background-color: #F4F5FA;">
          <p class="list_tab">题库类别</p>
          <div class="tabs" :class="{'curidx': curidx === idx}" v-for="(item, idx) in classifyData" :key="idx">
            <span @click="changeClassify(item, idx)" class="span_list">{{ item.type_name }}</span>
            <span @click="createClassify('update', item)" class="update_btn">编辑</span>
          </div>
          <div v-show="classifyData.length === 0" class="null-data">暂无题库类别</div>
        </Col>
        <Col span="18" offset="1" class="classify_list">
          <div class="add_section">
            <Button @click="createChapter('create')" icon="md-add" :disabled="classifyData.length === 0">新增章节</Button>
          </div>
          <Col span="8" v-for="(item, idx) in chapterData" :key="idx">
            <div class="list">
              <b>{{ item.section_name }}</b>
              <p>
                <Button type="text" @click="createChapter('update', item)" class="update_btn">编辑</Button>
              </p>
            </div>
          </Col>
        </Col>
      </Row>
    </Card>
    <!-- 类别弹窗内容 -->
    <Modal v-model="classifyModal" :title="classifyModalTitle" :mask-closable="false" width="320">
      <div class="modaldiv">
        <label>题库类别名称：</label>
        <Input v-model="listQuery.type_name" style="width: 200px" />
      </div>
      <div slot="footer">
        <Button type="primary" @click="classifyModalCancel">取消</Button>
        <Button v-if="curType=='create'" type="primary" @click="createTypeData">保存</Button>
        <Button v-else type="primary" @click="updateTypeData">保存</Button>
      </div>
    </Modal>
    <!-- 章节弹窗内容 -->
    <Modal v-model="chapterModal" :title="chapterModalTitle" :mask-closable="false" width="320">
      <div class="modaldiv">
        <label>题库类别名称：</label>
        <Select v-model="listQuery.question_type_id" filterable @change="changeSection" :disabled="curChapterType=='update'" style="width: 200px">
          <Option v-for="(itm, idx) in classifyData" :key="idx" :value="itm.question_type_id">{{ itm.type_name }}</Option>
        </Select>
      </div>
      <div class="modaldiv">
        <label>题库章节名称：</label>
        <Input v-model="listQuery.section_name" style="width: 200px"/>
      </div>
      <div slot="footer">
        <Button type="primary" @click="chapterModalCancel">取消</Button>
        <Button v-if="curChapterType=='create'" type="primary" @click="createChapterData">保存</Button>
        <Button v-else type="primary" @click="updateChapterData">保存</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import API from '@/api/examSystem/examModule/questionBankClassify'
export default {
  data () {
    return {
      curidx: 0, // 存储当前切换的题库idx
      curId: '', // 存储当前选中的题库idx
      curType: '',
      curChapterType: '',
      classifyData: [],
      chapterData: [],
      classifyModal: false,
      classifyModalTitle: '',
      chapterModal: false,
      chapterModalTitle: '',
      listQuery: {
        question_type_id: '',
        question_section_id: '',
        type_name: '',
        section_name: ''
      }
    }
  },
  created () {
    this.getTypeList()
  },
  methods: {
    // 获取题库类别列表
    getTypeList () {
      API.queryQuestionTypeList().then(res => {
        if (res.data.Code === 10000) {
          this.classifyData = res.data.Result
          if (res.data.Result.length !== 0) {
            this.listQuery.type_name = res.data.Result[0].type_name
            this.listQuery.question_type_id = res.data.Result[0].question_type_id
          }
          this.getSectionList()
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 获取章节列表
    getSectionList () {
      API.queryQuestionSectionList(this.listQuery).then(res => {
        if (res.data.Code === 10000) {
          this.chapterData = res.data.Result
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 点击左侧类别列表切换
    changeClassify (row, d) {
      this.curidx = d
      this.listQuery.type_name = row.type_name
      this.listQuery.question_type_id = row.question_type_id
      this.getSectionList()
    },
    // 开启类别弹窗
    createClassify (type, row) {
      this.curType = type
      this.classifyModal = true
      if (type === 'create') {
        this.classifyModalTitle = '题库类别新增'
      } else {
        this.listQuery.type_name = row.type_name
        this.listQuery.question_type_id = row.question_type_id
        this.classifyModalTitle = '题库类别编辑'
      }      

    },
    // 开启章节弹窗
    createChapter (type, row) {
      this.curChapterType = type
      this.chapterModal = true
      if (type === 'create') {
        this.chapterModalTitle = '章节新增'
      } else {
        this.chapterModalTitle = '章节编辑'
        this.listQuery.type_name = row.type_name
        this.listQuery.question_type_id = row.question_type_id
        this.listQuery.question_section_id = row.question_section_id
        this.listQuery.section_name = row.section_name
      }
    },
    // 类别新增保存
    createTypeData () {
      if (this.listQuery.type_name === '') return this.$Message.error('职务类别名称不能为空！')
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确认新增题库类别？</p>',
        loading: true,
        onOk: () => {
          API.addQuestionType({ type_name: this.listQuery.type_name }).then(res => {
            if (res.data.Code === 10000) {
              this.loading = false
              this.$Modal.remove()
              this.$Message.success(res.data.Message)
              this.classifyModalCancel()
              this.getTypeList()
            } else {
              this.loading = false
              this.$Modal.remove()
              this.$Message.error(res.data.Message)
            }
          })
        }
      })
    },
    // 类别编辑保存
    updateTypeData () {
      if (this.listQuery.type_name === '') return this.$Message.error('职务类别名称不能为空！')
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确认修改题库类别？</p>',
        loading: true,
        onOk: () => {
          API.updateQuestionType(this.listQuery).then(res => {
            if (res.data.Code === 10000) {
              this.loading = false
              this.$Modal.remove()
              this.$Message.success(res.data.Message)
              this.classifyModalCancel()
              this.getTypeList()
            } else {
              this.loading = false
              this.$Modal.remove()
              this.$Message.error(res.data.Message)
            }
          })
        }
      })
    },
    // 关闭类别弹窗
    classifyModalCancel () {
      this.classifyModal = false
      this.listQuery = {
        question_type_id: '',
        question_section_id: '',
        type_name: '',
        section_name: ''
      }
    },
    changeSection (name) {
      this.curId = this.classifyData.findIndex(e => { return name === e.type_name })
      this.listQuery.question_type_id = this.classifyData[this.curId].question_type_id
    },
    // 章节新增保存
    createChapterData () {
      if (this.listQuery.section_name === '') return this.$Message.error('章节名称不能为空！')
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确认新增章节？</p>',
        loading: true,
        onOk: () => {
          API.addQuestionSection(this.listQuery).then(res => {
            if (res.data.Code === 10000) {
              this.loading = false
              this.$Modal.remove()
              this.$Message.success(res.data.Message)
              this.chapterModalCancel()
              this.getSectionList()
            } else {
              this.loading = false
              this.$Modal.remove()
              this.$Message.error(res.data.Message)
            }
          })
        }
      })
    },
    // 章节编辑保存
    updateChapterData () {
      if (this.listQuery.section_name === '') return this.$Message.error('章节名称不能为空！')
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确认修改章节名称？</p>',
        loading: true,
        onOk: () => {
          API.updateQuestionSection(this.listQuery).then(res => {
            if (res.data.Code === 10000) {
              this.loading = false
              this.$Modal.remove()
              this.$Message.success(res.data.Message)
              this.chapterModalCancel()
              this.getSectionList()
            } else {
              this.loading = false
              this.$Modal.remove()
              this.$Message.error(res.data.Message)
            }
          })
        }
      })
    },
    // 关闭章节弹窗
    chapterModalCancel () {
      this.curidx = this.curId !== '' ? this.curId : this.curidx
      this.chapterModal = false
      this.listQuery.section_name = ''
      this.listQuery.question_section_id = ''
      this.listQuery.type_name = this.classifyData[this.curidx].type_name
      this.listQuery.question_type_id = this.classifyData[this.curidx].question_type_id
    }
  }
}
</script>
<style lang="less" scoped>
.modaldiv {
  padding: 10px 0;
  text-align: center;
  margin-bottom: 10px;
}
.null-data {
  padding: 5px 0;
}
.tabs_change {
  .list_tab {
    font-size: 18px;
    color: black;
    font-weight: bold;
    margin: 0 0 10px 10px;
    line-height: 45px;
  }
  .ivu-col {
    padding: 15px 0 15px 15px;
    border-radius: 5px;
  }
  .tabs {
    cursor: default;
    overflow: hidden;
    margin-bottom: 2px;
    padding: 5px 0 5px 15px;
    border-radius: 30px 0 0 30px;
    .span_list {
      width: 78%;
      line-height: 45px;
      font-size: 14px;
      text-align: left;
      display: inline-block;
      -webkit-transition: 0.3s;
      -moz-transition: 0.3s;
      -o-transition: 0.3s;
      transition: 0.3s;
    }
    .update_btn {
      color: #1943A9;
      font-size: 14px;
    }
  }
  .tabs:hover, .curidx {
    background-color: #fff !important;
    .span_list {
      color: #1943A9;
      font-size: 16px;
      font-weight: bold;
    }
  }
  .examSys_add_btn {
    color: #fff;
    border: none;
    font-size: 16px;
    letter-spacing: 2px;
    margin-bottom: 25px;
    padding: 8px 10px;
    background-color: #1943A9;
  }
  .classify_list {
    padding: 15px 0;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.1);
    .add_section {
      text-align: right;
      button {
        color: white;
        padding: 8px 15px;
        margin: 0 15px 15px 0;
        background-color: #1943A9;
      }
    }
    .ivu-col.ivu-col-span-8 {
      width: 29.33%;
      margin: 0 2% 4%;
      padding: 0;
    }
    .list {
      padding: 10px 15px;
      border-radius: 5px;
      position: relative;
      background-color: #EEF4FF;
      b {
        color: #000;
        font-size: 16px;
        display: block;
        margin-bottom: 10px;
      }
      .update_btn {
        color: #1943A9;
        padding: 0;
        font-size: 14px;
      }
      &::after {
        content: '';
        width: 28px;
        height: 28px;
        display: block;
        top: 33%;
        right: 20px;
        position: absolute;
        background: url(../../../../assets/images/seaction.png) no-repeat center;
      }
    }
  }
}
</style>

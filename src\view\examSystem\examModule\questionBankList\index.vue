<template>
  <div>
    <Card>
      <search @searchResults='searchResults' :setSearch='setSearchData' @resetResults='resetResults'></search>
      <div class="extra" slot="extra">
        <formAction :setFormAction='setFormAction' @handleCreate="handleEditModal('create')"></formAction>
      </div>
    </Card>
    <Card style="margin-top: 10px">
      <Table border :loading="loading" ref="selection" :columns="columns" :data="list"></Table>
      <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
        :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
    </Card>
    <editModal :modalData="modalData" @callback="getList"></editModal>
  </div>
</template>
<script>
import search from '_c/search' // 查询组件
import formAction from '_c/formAction' // 表单操作组件
import editModal from './editModal'
import API from '@/api/examSystem/examModule/questionBankList'
import { queryQuestionTypeList, queryQuestionSectionList } from '@/api/examSystem/examModule/questionBankClassify'
export default {
  components: {
    search,
    formAction,
    editModal
  },
  data () {
    return {
      loading: false,
      total: 0,
      list: [],
      listCurrent: 1,
      listQuery: {
        question_topic: '',
        question_type_id: '',
        question_section_id: '',
        pageSize: 10,
        pageIndex: 1
      },
      setFormAction: {
        operation: ['create']
      },
      modalData: {
        modal: false,
        title: '',
        type: '',
        data: undefined
      },
      setSearchData: {
        question_topic: {
          type: 'text',
          label: '题目',
          width: 180,
          value: '',
          isdisable: false
        },
        question_type_id: {
          type: 'select',
          label: '类别',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 140,
          value: '',
          filterable: true,
          change: this.changeTypeName
        },
        question_section_id: {
          type: 'select',
          label: '章节',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 140,
          value: '',
          filterable: true,
          isdisabled: true
        }
      },
      columns: [
        {
          type: 'index',
          width: 60,
          align: 'center'
        },
        {
          title: '题目',
          key: 'question_topic',
          align: 'center'
        },
        {
          title: '分类',
          align: 'center',
          render: (h, params) => {
            let _arr = params.row.attributArray.map(item => item.type_name)
            return h('div', _arr.join())
          }
        },
        {
          title: '章节',
          align: 'center',
          render: (h, params) => {
            let _arr = params.row.attributArray.map(item => item.section_name)
            return h('div', _arr.join())
          }
        },
        {
          title: '录入时间',
          align: 'center',
          width: 100,
          render: (h, params) => {
            return h('div', params.row.insert_time.substring(0, 10))
          }
        },
        {
          title: '操作',
          align: 'center',
          width: 180,
          render: (h, params) => {
            return h('div', [
              h('Button', {
                style: {
                  margin: '0 5px'
                },
                props: {
                  icon: 'md-brush',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleEditModal('update', params.row)
                  }
                }
              }, '编辑'),
              h('Button', {
                style: {
                  margin: '0 5px'
                },
                props: {
                  icon: 'md-trash',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleDelete(params.row.question_id)
                  }
                }
              }, '删除')
            ])
          }
        }
      ]
    }
  },
  created () {
    this.getDataList()
    this.getList()
  },
  methods: {
    // 获取列表
    getList () {
      this.loading = true
      API.queryQuestionPage(this.listQuery).then(res => {
        if (res.data.Code === 10000) {
          this.loading = false
          this.list = res.data.Result
          this.total = res.data.Total
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 新增/编辑题目
    handleEditModal (type, row) {
      if (type === 'create') {
        this.modalData = {
          modal: true,
          title: '题目新增',
          data: undefined,
          type: type,
          questionTypeData: this.setSearchData.question_type_id.selectData
        }
      } else {
        this.modalData = {
          modal: true,
          type: type,
          title: '题目编辑',
          data: row,
          questionTypeData: this.setSearchData.question_type_id.selectData
        }
      }
    },
    // 删除
    handleDelete (d) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确认删除所选项？</p>',
        loading: true,
        onOk: () => {
          API.deleteExamQuestion({ question_id: d }).then(res => {
            if (res.data.Code === 10000) {
              this.loading = false
              this.$Modal.remove()
              this.$Message.success(res.data.Message)
              this.getList()
            } else {
              this.loading = false
              this.$Modal.remove()
              this.$Message.error(res.data.Message)
            }
          })
        }
      })
    },
    // 获取题库类别下拉
    getDataList () {
      queryQuestionTypeList().then(res => {
        if (res.data.Code === 10000) {
          res.data.Result.map(item => {
            this.setSearchData.question_type_id.selectData.push({
              label: item.type_name,
              value: item.question_type_id
            })
          })
        }
      })
    },
    // 选择类别触发
    changeTypeName (val) {
      this.setSearchData.question_section_id.selectData = []
      this.setSearchData.question_section_id.isdisabled = val.selected === undefined
      queryQuestionSectionList({ question_type_id: val.selected }).then(res => { // 获取章节下拉
        if (res.data.Code === 10000) {
          res.data.Result.map(e => {
            this.setSearchData.question_section_id.selectData.push({
              label: e.section_name,
              value: e.question_section_id
            })
          })
        }
      })
    },
    // 查询
    searchResults (e) {
      this.listQuery.question_topic = e.question_topic
      this.listQuery.question_type_id = e.question_type_id
      this.listQuery.question_section_id = e.question_section_id
      this.listCurrent = 1
      this.listQuery.pageIndex = 1
      this.getList()
    },
    // 重置
    resetResults () {
      this.listCurrent = 1
      this.listQuery = {
        question_topic: '',
        question_type_id: '',
        question_section_id: '',
        pageSize: 10,
        pageIndex: 1
      }
      this.setSearchData.question_topic.value = ''
      this.setSearchData.question_type_id.selected = ''
      this.setSearchData.question_section_id.selected = ''
      this.getList()
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    }
  }
}
</script>
<style lang="less" scoped>
  .ivu-form-item:last-child {
    margin-bottom: 0;
  }
</style>

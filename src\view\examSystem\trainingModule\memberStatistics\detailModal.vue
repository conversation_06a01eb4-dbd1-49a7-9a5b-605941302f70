<template>
  <div>
    <Drawer :title="modalTitle" v-model="modalData.modal" :data="modalData.data" @on-visible-change="modalShow" width="850">
      <div slot="close">
        <Button v-if="modalData.type === 'train' || modalData.type === 'evaluate'" type="primary" @click="exportData">导出</Button>
      </div>
      <Table border :loading="loading" ref="selection" v-if="modalData.type === 'train'" :columns="trainColumns" :data="list" style="margin-top: 10px;"></Table>
      <Table border :loading="loading" ref="selection" v-else-if="modalData.type === 'exam'" :columns="examColumns" :data="list" style="margin-top: 10px;"></Table>
      <Table border :loading="loading" ref="selection" v-else :columns="evaluateColumns" :data="list" style="margin-top: 10px;"></Table>
      <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
        :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
      <div class="demo-drawer-footer">
        <Button type="primary" @click="modalData.modal = false">返回</Button>
      </div>
    </Drawer>
  </div>
</template>
<script>
import API from '@/api/examSystem/trainingModule/memberStatistics'
export default {
  props: {
    modalData: Object
  },
  data () {
    return {
      modalTitle: '',
      loading: false,
      list: [],
      total: 0,
      listCurrent: 1,
      trainColumns: [
        {
          title: '姓名',
          key: 'member_name',
          align: 'center'
        },
        {
          align: 'center',
          renderHeader: (h, params) => {
            return h('Button', {
              props: {
                icon: 'md-arrow-down',
                size: 'large',
                type: 'text'
              },
              on: {
                click: () => {
                  this.changeOrder('0')
                }
              }
            }, '时长')
          },
          render: (h, params) => {
            let curVal = params.row.member_train_hour.split('.')[0] + '小时' + params.row.member_train_minute.split('.')[0] + '分钟'
            return h('div', {
              props: {
                icon: 'md-arrow-down',
                size: 'small'
              },
            }, curVal)
          }
        },
        {
          key: 'member_sign_times',
          align: 'center',
          renderHeader: (h, params) => {
            return h('Button', {
              props: {
                icon: 'md-arrow-down',
                size: 'large',
                type: 'text'
              },
              on: {
                click: () => {
                  this.changeOrder('1')
                }
              }
            }, '次数/次')
          }
        }
      ],
      examColumns: [
        {
          title: '姓名',
          key: 'member_name',
          align: 'center'
        },
        {
          key: 'member_avg_score',
          align: 'center',
          renderHeader: (h, params) => {
            return h('Button', {
              props: {
                icon: 'md-arrow-down',
                size: 'large',
                type: 'text'
              },
              on: {
                click: () => {
                  this.changeOrder('1')
                }
              }
            }, '成绩/分')
          }
        },
        {
          key: 'member_exam_times',
          align: 'center',
          renderHeader: (h, params) => {
            return h('Button', {
              props: {
                icon: 'md-arrow-down',
                size: 'large',
                type: 'text'
              },
              on: {
                click: () => {
                  this.changeOrder('0')
                }
              }
            }, '次数/次')
          }
        }
      ],
      evaluateColumns: [
        {
          title: '姓名',
          key: 'train_lecturer',
          align: 'center'
        },
        {
          title: '课题',
          align: 'center',
          render: (h, params) => {
            return h('div', {
              style: {
                wordBreak: 'break-all',
                display: '-webkit-box',
                '-webkit-line-clamp': '2',
                '-webkit-box-orient': 'vertical',
                overflow: 'hidden'
              },
            }, params.row.theme_title)
          }
        },
        {
          title: '评分/分',
          key: 'theme_evaluate_score',
          align: 'center',
          renderHeader: (h, params) => {
            return h('Button', {
              props: {
                icon: 'md-arrow-down',
                size: 'large',
                type: 'text'
              },
              on: {
                click: () => {
                  this.changeOrder('0')
                }
              }
            }, '评分/分')
          }
        },
        {
          key: 'train_member_num',
          align: 'center',
          renderHeader: (h, params) => {
            return h('Button', {
              props: {
                icon: 'md-arrow-down',
                size: 'large',
                type: 'text'
              },
              on: {
                click: () => {
                  this.changeOrder('1')
                }
              }
            }, '参会人数/人')
          }
        },
        {
          key: 'member_evaluate_num',
          align: 'center',
          renderHeader: (h, params) => {
            return h('Button', {
              props: {
                icon: 'md-arrow-down',
                size: 'large',
                type: 'text'
              },
              on: {
                click: () => {
                  this.changeOrder('2')
                }
              }
            }, '参评人数/人')
          }
        }
      ],
      listQuery: {
        train_date_start: '',
        train_date_end: '',
        order_type: '0', // 排序方式
        pageSize: 10,
        pageIndex: 1
      }
    }
  },
  methods: {
    // 模态框展示
    modalShow (val) {
      if (val) {
        this.modalTitle = this.modalData.title
        this.listQuery.train_date_start = this.modalData.time.train_date_start
        this.listQuery.train_date_end = this.modalData.time.train_date_end
        this.getList()
      }
    },
    // 获取培训列表
    getList () {
      this.loading = true
      if (this.modalData.type === 'train') {
        API.queryTrainMemberDurationPage(this.listQuery).then(res => {
          if (res.data.Code === 10000) {
            this.loading = false
            this.list = res.data.Result
            this.total = res.data.Total
          } else {
            this.$Message.error(res.data.Message)
          }
        })
      } else if (this.modalData.type === 'exam') {
        API.queryTrainMemberExamAvgPage(this.listQuery).then(res => {
          if (res.data.Code === 10000) {
            this.loading = false
            this.list = res.data.Result
            this.total = res.data.Total
          } else {
            this.$Message.error(res.data.Message)
          }
        })
      } else {
        API.queryTrainThemeLecturerPage(this.listQuery).then(res => {
          if (res.data.Code === 10000) {
            this.loading = false
            this.list = res.data.Result
            this.total = res.data.Total
          } else {
            this.$Message.error(res.data.Message)
          }
        })
      }
    },
    // 导出培训总时长/签到次数
    exportData () {
      if (this.modalData.type === 'train') {
        API.exportTrainDuration(this.listQuery).then(res => {
          if (res.data.Code === 10000) {
            window.open(res.data.fileUrl, '_blank')
          } else {
            this.$Message.warning(res.data.Message)
          }
        })
      }
      if (this.modalData.type === 'evaluate') {
        API.exportLecturerStat(this.listQuery).then(res => {
          if (res.data.Code === 10000) {
            window.open(res.data.fileUrl, '_blank')
          } else {
            this.$Message.warning(res.data.Message)
          }
        })
      }
    },
    // 排序
    changeOrder (val) {
      this.listQuery.order_type = val
      this.getList()
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    }
  }
}
</script>
<style lang="less" scoped>
.demo-drawer-footer {
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  background: #fff;
}
</style>

<template>
  <Drawer :title="modalData.title" v-model="modalData.modal" :data="modalData.data" @on-visible-change="modalShow" :mask-closable="false" width="1050">
    <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="105" style="padding-bottom: 50px;">
      <Row>
        <FormItem label="类型" prop="theme_type" :label-width="70">
          <Select v-model="formValidate.theme_type" filterable style="width: 160px;">
            <Option v-for="(item, idx) in typeList" :key="idx" :value="item.theme_type">{{ item.theme_type_name }}</Option>
          </Select>
        </FormItem>
      </Row>
      <Row>
        <Col span="6">
          <FormItem label="标题" prop="theme_title" :label-width="70">
            <Input v-model="formValidate.theme_title"></Input>
          </FormItem>
        </Col>
        <Col span="5">
          <FormItem label="部门" prop="train_dept" :label-width="70">
            <Input v-model="formValidate.train_dept"></Input>
          </FormItem>
        </Col>
        <Col span="6">
          <FormItem label="培训/调研人" prop="train_lecturer">
            <Input v-model="formValidate.train_lecturer"></Input>
          </FormItem>
        </Col>
        <Col span="7">
          <FormItem label="培训/调研日期" prop="train_date">
            <DatePicker type="datetime" format="yyyy-MM-dd HH:mm" @on-change="data=>formValidate.train_date=data" :value="formValidate.train_date" :editable="false"></DatePicker>
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span="5">
          <FormItem label="培训/调研时长" prop="total_time">
            <Input v-model="formValidate.total_time" style="display: inline-block;"></Input>
          </FormItem>
        </Col>
        <Col span="1" style="margin: 8px 0 0 5px;">分钟</Col>
        <Col span="7">
          <FormItem label="报名开始时间" prop="start_time">
            <DatePicker type="datetime" format="yyyy-MM-dd HH:mm" @on-change="data=>formValidate.start_time=data" :value="formValidate.start_time" :editable="false"></DatePicker>
          </FormItem>
        </Col>
        <Col span="7">
          <FormItem label="报名结束时间" prop="end_time">
            <DatePicker type="datetime" format="yyyy-MM-dd HH:mm" @on-change="data=>formValidate.end_time=data" :value="formValidate.end_time" :editable="false"></DatePicker>
          </FormItem>
        </Col>
      </Row>
      <Row>
        <FormItem label="简介" prop="intro">
          <Input type="textarea" v-model="formValidate.intro"></Input>
        </FormItem>
      </Row>
      <div class="tab_btn">
        <Button :type="curTab === 'evaluate' ? 'primary' : 'default'" size="large" @click="changeTab('evaluate')">评价内容</Button>
        <Button :type="curTab === 'exam' ? 'primary' : 'default'" size="large" @click="changeTab('exam')">考题内容</Button>
      </div>
      <!-- 评价内容 -->
      <div v-show="curTab === 'evaluate'">
        <div v-for="(item, idx) in evaluateList" :key="idx" class="start_div">
          <span style="margin-right: 6px;min-width: 30px;text-align: right;display: inline-block;">{{ idx + 1 }}.</span>
          <Input type="text" v-model="item.theme_evaluate_title" style="width: calc(100% - 40px);"></Input>
          <Rate disabled v-if="item.theme_evaluate_title_type !== '2'" :value="parseInt(item.theme_evaluate_score)" :count='10' />
          <Input disabled v-if="item.theme_evaluate_title_type === '2'" type="textarea" style="width: calc(100% - 40px);margin: 10px 0 0 35px;"></Input>
        </div>
      </div>
      <!-- 考题内容 -->
      <div v-show="curTab === 'exam'" style="padding: 0 15px;">
        <div style="margin-bottom: 15px;" v-if="modalData.state !== '1'">
          <Button type="primary" @click="handleAdd('single')" icon="md-add">单选题</Button>
          <Button type="primary" @click="handleAdd('multiple')" icon="md-add" style="margin-left: 10px;">多选题</Button>
          <Button type="primary" @click="handleAdd('QAndA')" icon="md-add" style="margin-left: 10px;">问答题</Button>
        </div>
        <div v-if="isExamList">
          <div v-for="(item, indexs) in formValidate.themeExamJson" :key="indexs">
            <Row>
              <span class="label_div">{{ indexs + 1 }}.</span>
              <Col span="23">
                <FormItem label="题目" :prop="'themeExamJson.' + indexs + '.theme_exam_title'" :rules="{required: true, message: '此处不能为空', trigger: 'blur'}" :label-width="70">
                  <Input type='textarea' v-model='item.theme_exam_title' :disabled="modalData.state === '1'"></Input>
                </FormItem>
              </Col>
              <Col span="1">
                <Button v-if="modalData.state !== '1'" type="text" @click="handleRemove('examTitle', indexs)" icon="md-remove-circle" size="large"></Button>
              </Col>
            </Row>
            <div v-if="!item.isQAndA">
              <Row>
                <label class="label_div">选项</label>
                <div v-for="(itm, idx) in item.options" :key="idx">
                  <Col span="13">
                    <FormItem :label="idxArr[idx]" :prop="'themeExamJson.' + indexs + '.options.' + idx + '.exam_option_content'" :rules="{required: true, message: '此处不能为空', trigger: 'blur'}" :label-width="70">
                      <Input v-model='itm.exam_option_content' :disabled="modalData.state === '1'"></Input>
                    </FormItem>
                  </Col>
                  <Col span="1">
                    <Button v-if="idx === (item.options.length - 1) && item.options.length < 6 && modalData.state !== '1'" type="text" @click="handleAdd('option', indexs, idx)" icon="md-add-circle" size="large"></Button>
                  </Col>
                  <Col span="1">
                    <Button v-if="item.showOptionRemove && modalData.state !== '1'" type="text" @click="handleRemove('option', indexs, idx)" icon="md-remove-circle" size="large"></Button>
                  </Col>
                </div>
              </Row>
              <Row>
                <FormItem v-if="item.isMultiple" label="答案" :prop="'themeExamJson.' + indexs + '.theme_exam_answer'" :rules="{required: true, message: '此处不能为空', trigger: 'change', type: 'array'}" :label-width="70">
                  <Select v-model="item.theme_exam_answer" :disabled="modalData.state === '1'" filterable :style="item.isMultiple ? 'width: 170px' : 'width: 73px'" :multiple="item.isMultiple">
                    <Option v-for="(item1, idx) in answerList[indexs]" :key="idx" :value="item1">{{ item1 }}</Option>
                  </Select>
                </FormItem>
                <FormItem v-else label="答案" :prop="'themeExamJson.' + indexs + '.theme_exam_answer'" :rules="{required: true, message: '此处不能为空', trigger: 'change'}" :label-width="70">
                  <Select v-model="item.theme_exam_answer" :disabled="modalData.state === '1'" filterable :style="item.isMultiple ? 'width: 170px' : 'width: 73px'" :multiple="item.isMultiple">
                    <Option v-for="(item1, idx) in answerList[indexs]" :key="idx" :value="item1">{{ item1 }}</Option>
                  </Select>
                </FormItem>
              </Row>
            </div>
            <Row v-else>
              <Col span="23">
                <FormItem label="答案" :prop="'themeExamJson.' + indexs + '.theme_exam_answer_analysis'" :rules="{required: true, message: '此处不能为空', trigger: 'blur'}" :label-width="70">
                  <Input :disabled="modalData.state === '1'" type="textarea" v-model="item.theme_exam_answer_analysis"></Input>
                </FormItem>
              </Col>
            </Row>
          </div>
        </div>
      </div>
    </Form>
    <div class="demo-drawer-footer">
      <Button style="margin-right: 8px" @click="modalData.modal = false">取消</Button>
      <Button type="primary" v-if="modalData.type === 'create'" @click="createData">保存</Button>
      <Button type="primary" v-else @click="updateData">保存</Button>
    </div>
  </Drawer>
</template>
<script>
import { validateNumber } from '@/assets/js/iViewValidate'
import { trainThemeAdd, trainThemeUpdateAll } from '@/api/examSystem/trainingModule/trainingTopics'
import { queryEvaluateTemplateList } from '@/api/examSystem/dataConfigure/evaluateTemplate'
export default {
  props: {
    modalData: Object
  },
  data () {
    return {
      typeList: [{
        theme_type: '1',
        theme_type_name: '培训'
      }, {
        theme_type: '2',
        theme_type_name: '调研'
      }],
      curTab: 'evaluate',
      isExamList: false,
      evaluateList: [],
      idxArr: ['A', 'B', 'C', 'D', 'E', 'F'],
      answerList: [], // 答案下拉
      formValidate: {
        theme_id: '', // 主题id
        theme_type: '', // 培训类型（1培训；2调研）
        theme_title: '', // 标题
        train_dept: '', // 部门
        train_lecturer: '', // 培训/调研人
        train_date: '', // 培训/调研日期
        total_time: '', // 培训/调研时长
        start_time: '', // 报名开始时间
        end_time: '', // 报名结束时间
        theme_state: '', // 状态（0计划；1进行；2完成。默认为0）
        intro: '', // 简介
        evaluateJson: [{
          theme_evaluate_title_type: '',
          theme_evaluate_title: '',
          theme_evaluate_score: '10'
        }],
        themeExamJson: []
      },
      ruleValidate: {
        theme_type: [{ required: true, message: '此处不能为空', trigger: 'change' }],
        theme_title: [{ required: true, message: '此处不能为空', trigger: 'blur' }],
        train_dept: [{ required: true, message: '此处不能为空', trigger: 'blur' }],
        train_lecturer: [{ required: true, message: '此处不能为空', trigger: 'blur' }],
        train_date: [{ required: true, message: '此处不能为空', trigger: 'change' }],
        total_time: [
          { required: true, message: '此处不能为空', trigger: 'blur' },
          { validator: validateNumber, message: '请输入分钟数', trigger: 'blur' }
        ],
        start_time: [{ required: true, message: '此处不能为空', trigger: 'change' }],
        end_time: [{ required: true, message: '此处不能为空', trigger: 'change' }],
        intro: [{ required: true, message: '此处不能为空', trigger: 'blur' }]
      }
    }
  },
  methods: {
    modalShow (val) {
      if (val) {
        if (this.modalData.type === 'create') {
          this.isExamList = false
          // 获取评价内容
          queryEvaluateTemplateList(this.listQuery).then(res => {
            if (res.data.Code === 10000) {
              res.data.Result.map(item => {
                this.evaluateList.push({
                  theme_evaluate_title: item.evaluate_template_title,
                  theme_evaluate_score: item.evaluate_template_score,
                  theme_evaluate_title_type: item.evaluate_template_type
                })
              })
            }
          })
        } else {
          this.isExamList = true
          this.evaluateList = this.modalData.data.evaluateJson
          this.formValidate = {
            theme_id: this.modalData.data.theme_id,
            theme_type: this.modalData.data.theme_type,
            theme_title: this.modalData.data.theme_title,
            train_dept: this.modalData.data.train_dept,
            train_lecturer: this.modalData.data.train_lecturer,
            train_date: this.modalData.data.train_date,
            total_time: this.modalData.data.total_time,
            start_time: this.modalData.data.start_time,
            end_time: this.modalData.data.end_time,
            theme_state: this.modalData.data.theme_state,
            intro: this.modalData.data.intro,
            evaluateJson: this.modalData.data.evaluateJson,
            themeExamJson: []
          }
          if (this.modalData.data.themeExamJson.length < 1) return
          let themeJson = this.modalData.data.themeExamJson.map((item, idx) => {
            let _obj = { ...{}, ...item }
            _obj.isMultiple = item.theme_exam_title_type === '2'
            _obj.isQAndA = item.theme_exam_title_type === '3'
            if (item.theme_exam_title_type === '1') {
              _obj.isCurType = 'single'
              _obj.showOptionRemove = item.options.length > 2
              this.answerList[idx] = this.idxArr.slice(0, item.options.length)
            } else if (item.theme_exam_title_type === '2') {
              _obj.isCurType = 'multiple'
              _obj.showOptionRemove = item.options.length > 4
              this.answerList[idx] = this.idxArr.slice(0, item.options.length)
              _obj.theme_exam_answer = item.theme_exam_answer.split(',')
            } else {
              this.answerList[idx] = []
            }
            return _obj
          })
          Object.assign(this.formValidate, {
            themeExamJson: themeJson
          })
        }
      } else {
        this.clearData()
        this.$refs['formValidate'].resetFields()
      }
    },
    // tab切换
    changeTab (type) {
      if (type === 'evaluate') {
        this.curTab = 'evaluate'
      } else {
        this.curTab = 'exam'
      }
    },
    // 增加内容信息
    handleAdd (type, parentIdx, idx) {
      if (type === 'single') { // 新增单选题
        this.isExamList = true
        this.formValidate.themeExamJson.push({
          theme_exam_title_type: '1',
          theme_exam_title: '',
          theme_exam_answer: '',
          theme_exam_answer_analysis: '',
          isCurType: 'single',
          isMultiple: false,
          isQAndA: false,
          options: [{
            exam_option_code: 'A',
            exam_option_content: ''
          }, {
            exam_option_code: 'B',
            exam_option_content: ''
          }]
        })
        this.answerList = [...this.answerList, ['A', 'B']]
      } else if (type === 'multiple') { // 新增多选题
        this.isExamList = true
        this.formValidate.themeExamJson.push({
          theme_exam_title_type: '2',
          theme_exam_title: '',
          theme_exam_answer: '',
          theme_exam_answer_analysis: '',
          isCurType: 'multiple',
          isMultiple: true,
          isQAndA: false,
          options: [{
            exam_option_code: 'A',
            exam_option_content: ''
          }, {
            exam_option_code: 'B',
            exam_option_content: ''
          }, {
            exam_option_code: 'C',
            exam_option_content: ''
          }, {
            exam_option_code: 'D',
            exam_option_content: ''
          }]
        })
        this.answerList = [...this.answerList, ['A', 'B', 'C', 'D']]
      } else if (type === 'QAndA') { // 问答题
        this.isExamList = true
        this.formValidate.themeExamJson.push({
          isQAndA: true,
          theme_exam_title_type: '3',
          theme_exam_title: '',
          theme_exam_answer_analysis: ''
        })
        this.answerList = [...this.answerList, []]
      } else if (type === 'option') { // 新增选项
        this.formValidate.themeExamJson[parentIdx].options.push({
          exam_option_code: this.idxArr[idx + 1],
          exam_option_content: ''
        })
        this.answerList[parentIdx] = [...this.answerList[parentIdx], this.idxArr[idx + 1]]
        if (this.formValidate.themeExamJson[parentIdx].isCurType && this.formValidate.themeExamJson[parentIdx].isCurType === 'single') {
          this.formValidate.themeExamJson[parentIdx].showOptionRemove = this.formValidate.themeExamJson[parentIdx].options.length > 2
        } else if (this.formValidate.themeExamJson[parentIdx].isCurType && this.formValidate.themeExamJson[parentIdx].isCurType === 'multiple') {
          this.formValidate.themeExamJson[parentIdx].showOptionRemove = this.formValidate.themeExamJson[parentIdx].options.length > 4
        }
      }
    },
    // 移除内容信息
    handleRemove (type, parentIdx, idx) {
      if (type === 'examTitle') { // 移除考题
        this.formValidate.themeExamJson.splice(parentIdx, 1)
        this.answerList.splice(parentIdx, 1)
        if (this.formValidate.themeExamJson.length === 0) {
          this.isExamList = false
        }
      } else { // 移除选项
        this.answerList[parentIdx] = this.idxArr.slice(0, this.formValidate.themeExamJson[parentIdx].options.length - 1)
        this.formValidate.themeExamJson[parentIdx].options.splice(idx, 1)
        this.formValidate.themeExamJson[parentIdx].theme_exam_answer = ''
        if (this.formValidate.themeExamJson[parentIdx].isCurType && this.formValidate.themeExamJson[parentIdx].isCurType === 'single') {
          this.formValidate.themeExamJson[parentIdx].showOptionRemove = this.formValidate.themeExamJson[parentIdx].options.length > 2
        } else if (this.formValidate.themeExamJson[parentIdx].isCurType && this.formValidate.themeExamJson[parentIdx].isCurType === 'multiple') {
          this.formValidate.themeExamJson[parentIdx].showOptionRemove = this.formValidate.themeExamJson[parentIdx].options.length > 4
        }
      }
    },
    // 新增保存
    createData () {
      this.$refs['formValidate'].validate((valid) => {
        if (valid) {
          this.$Modal.confirm({
            title: '提示',
            content: '<p>确认增加培训课题？</p>',
            loading: true,
            onOk: () => {
              let themeJson = []
              if (this.formValidate.themeExamJson) {
                themeJson = this.formValidate.themeExamJson.map(item => {
                  delete item.isCurType
                  delete item.isMultiple
                  delete item.isQAndA
                  delete item.showOptionRemove
                  let _obj = { ...{}, ...item }
                  _obj.theme_exam_answer = typeof _obj.theme_exam_answer === 'object' ? _obj.theme_exam_answer.join() : _obj.theme_exam_answer
                  return _obj
                })
              }
              let data = {
                theme_type: this.formValidate.theme_type,
                theme_title: this.formValidate.theme_title,
                train_dept: this.formValidate.train_dept,
                train_lecturer: this.formValidate.train_lecturer,
                train_date: this.formValidate.train_date,
                total_time: this.formValidate.total_time,
                start_time: this.formValidate.start_time,
                end_time: this.formValidate.end_time,
                theme_state: this.formValidate.theme_state,
                intro: this.formValidate.intro,
                evaluateJson: JSON.stringify(this.evaluateList),
                themeExamJson: this.formValidate.themeExamJson ? JSON.stringify(themeJson) : themeJson
              }
              trainThemeAdd(data).then(res => {
                if (res.data.Code === 10000) {
                  this.loading = false
                  this.$Modal.remove()
                  this.modalData.modal = false
                  this.$Message.success(res.data.Message)
                  this.$emit('callback')
                } else {
                  this.loading = false
                  this.$Modal.remove()
                  this.$Message.error(res.data.Message)
                }
              })
            }
          })
        }
      })
    },
    // 编辑保存
    updateData () {
      this.$refs['formValidate'].validate((valid) => {
        if (valid) {
          this.$Modal.confirm({
            title: '提示',
            content: '<p>确认增加培训课题？</p>',
            loading: true,
            onOk: () => {
              let themeJson = []
              if (this.formValidate.themeExamJson) {
                themeJson = this.formValidate.themeExamJson.map(item => {
                  delete item.isCurType
                  delete item.isMultiple
                  delete item.isQAndA
                  delete item.showOptionRemove
                  let _obj = { ...{}, ...item }
                  _obj.theme_exam_answer = typeof _obj.theme_exam_answer === 'object' ? _obj.theme_exam_answer.join() : _obj.theme_exam_answer
                  return _obj
                })
              }
              let data = {
                theme_id: this.formValidate.theme_id,
                theme_type: this.formValidate.theme_type,
                theme_title: this.formValidate.theme_title,
                train_dept: this.formValidate.train_dept,
                train_lecturer: this.formValidate.train_lecturer,
                train_date: this.formValidate.train_date,
                total_time: this.formValidate.total_time,
                start_time: this.formValidate.start_time,
                end_time: this.formValidate.end_time,
                theme_state: this.formValidate.theme_state,
                intro: this.formValidate.intro,
                evaluateJson: JSON.stringify(this.evaluateList),
                themeExamJson: this.formValidate.themeExamJson ? JSON.stringify(themeJson) : themeJson
              }
              trainThemeUpdateAll(data).then(res => {
                if (res.data.Code === 10000) {
                  this.loading = false
                  this.$Modal.remove()
                  this.modalData.modal = false
                  this.$Message.success(res.data.Message)
                  this.$emit('callback')
                } else {
                  this.loading = false
                  this.$Modal.remove()
                  this.$Message.error(res.data.Message)
                }
              })
            }
          })
        }
      })
    },
    // 清除数据
    clearData () {
      this.curTab = 'evaluate'
      this.evaluateList = []
      this.answerList = []
      this.isExamList = false
      this.formValidate = {
        theme_id: '',
        theme_type: '',
        theme_title: '',
        train_dept: '',
        train_lecturer: '',
        train_date: '',
        total_time: '',
        start_time: '',
        end_time: '',
        theme_state: '',
        intro: '',
        evaluateJson: [{
          theme_evaluate_title_type: '',
          theme_evaluate_title: '',
          theme_evaluate_score: '10'
        }],
        themeExamJson: []
      }
    }
  }
}
</script>
<style lang="less" scoped>
.demo-drawer-footer {
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  background: #fff;
}
.tab_btn {
  margin-bottom: 10px;
  button:first-child {
    margin-right: 10px;
  }
}
.start_div {
  margin-bottom: 15px;
  .ivu-rate {
    margin: 6px 0 0 35px;
  }
}
.label_div {
  position: absolute;
  left: 10px;
  top: 7px;
}
</style>

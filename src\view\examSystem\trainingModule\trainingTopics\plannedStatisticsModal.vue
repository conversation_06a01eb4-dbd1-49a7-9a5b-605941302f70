<template>
  <!-- 已计划 统计 -->
  <Drawer :title="plannedStatisticsModalData.title"
    v-model="plannedStatisticsModalData.modal"
    :data="plannedStatisticsModalData.data"
    @on-visible-change="modalShow"
    width="850">
    <h3 class="table_headinfo" v-if="plannedStatisticsModalData.modal">
      <span>报名总人数：{{ plannedStatisticsModalData.data.member_apply_num }}</span>
      <!-- <span style="margin-left: 30px">公司就餐人数：{{ plannedStatisticsModalData.data.member_dine_num }}</span> -->
      <Button style="float: right;" @click="exportData">导出</Button>
    </h3>
    <Table border :loading="listLoading" ref="selection" :columns="columns" :data="list"></Table>
    <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
      :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
    <!-- <Button type="primary" style="margin: 20px auto 0; display: block;">发送消息至未签到人员</Button> -->
    <div class="demo-drawer-footer">
      <Button type="primary" @click="plannedStatisticsModalData.modal = false">返回</Button>
    </div>
  </Drawer>
</template>
<script>
import { queryTrainMemberThemePage, exportThemeTemplate } from '@/api/examSystem/trainingModule/memberManagement'
export default {
  props: {
    plannedStatisticsModalData: Object
  },
  data () {
    return {
      curMemberExamArr: [],
      curEvaluateArr: [],
      examModal: false,
      evaluateModal: false,
      curTab: 'noSignIn',
      listLoading: false,
      list: [],
      total: null,
      listCurrent: 1,
      listQuery: {
        member_id: '',
        train_member_id: '',
        theme_id: '',
        member_apply_state: '',
        member_sign_state: '0',
        member_dine_state: '',
        member_evaluate_state: '',
        member_exam_state: '',
        pageSize: 10,
        pageIndex: 1
      },
      columns: [
        {
          title: '报名人员',
          key: 'member_name',
          align: 'center'
        },
        {
          title: '电话',
          key: 'member_mobile',
          align: 'center'
        },
        // {
        //   title: '状态',
        //   key: 'member_dine_state',
        //   align: 'center',
        //   render: (h, params) => {
        //     return h('div', {}, params.row.member_dine_state === '0' ? '不就餐' : '公司就餐')
        //   }
        // }
      ]
    }
  },
  methods: {
    getList () {
      this.loading = true
      this.listQuery.theme_id = this.plannedStatisticsModalData.data.theme_id
      queryTrainMemberThemePage(this.listQuery).then(res => {
        if (res.data.Code === 10000) {
          this.loading = false
          this.list = res.data.Result
          this.total = res.data.Total
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 详情
    handleDetailModal (row, type) {
      if (type === 'exam') {
        this.examModal = true
        this.curMemberExamArr = row
      } else {
        this.evaluateModal = true
        this.curEvaluateArr = row
      }
    },
    // 导出
    exportData () {
      exportThemeTemplate(this.listQuery).then(res => {
        if (res.data.Code === 10000) {
          window.open(res.data.fileUrl, '_blank')
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // tab切换
    changeTab (type) {
      if (type === 'noSignIn') {
        this.curTab = 'noSignIn'
        this.listQuery.member_sign_state = '0'
        this.listQuery.member_exam_state = ''
        this.listQuery.member_evaluate_state = ''
      } else if (type === 'noExam') {
        this.curTab = 'noExam'
        this.listQuery.member_exam_state = '0'
        this.listQuery.member_evaluate_state = ''
        this.listQuery.member_sign_state = ''
      } else if (type === 'noEvaluated') {
        this.curTab = 'noEvaluated'
        this.listQuery.member_evaluate_state = '0'
        this.listQuery.member_sign_state = ''
        this.listQuery.member_exam_state = ''
      } else if (type === 'all') {
        this.curTab = 'all'
        this.listQuery.member_evaluate_state = ''
        this.listQuery.member_sign_state = ''
        this.listQuery.member_exam_state = ''
      }
      this.getList()
    },
    modalShow (val) {
      if (val) {
        this.getList()
      } else {
        this.curTab = 'noSignIn'
        this.listQuery = {
          member_id: '',
          train_member_id: '',
          theme_id: '',
          member_apply_state: '',
          member_sign_state: '0',
          member_dine_state: '',
          member_evaluate_state: '',
          member_exam_state: '',
          pageSize: 10,
          pageIndex: 1
        }
      }
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    }
  }
}
</script>
<style lang="less" scoped>
.demo-drawer-footer {
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  background: #fff;
}
.table_headinfo {
  margin-bottom: 15px;
  color: #333;
}
</style>

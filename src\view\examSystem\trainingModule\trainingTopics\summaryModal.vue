<template>
  <div>
    <Drawer :title="summaryModalData.title" v-model="summaryModalData.modal" :data="summaryModalData.data" @on-visible-change="modalShow" :mask-closable="false" width="850">
      <h3 v-if="summaryModalData.modal" :style="{left: summaryModalData.title.length * 16+'px'}" class="theme_info">
        <span>报名人数: {{ summaryModalData.data.member_apply_num }}人</span>
        <span>签到人数: {{ summaryModalData.data.member_sign_num }}人</span>
        <span>考试人数: {{ summaryModalData.data.member_exam_num }}人</span>
        <span>参评人数: {{ summaryModalData.data.member_evaluate_num }}人</span>
      </h3>
      <div class="tab_btn">
        <Button :type="curTab === 'exam' ? 'primary' : 'default'" size="large" @click="changeTab('exam')">考试统计</Button>
        <Button :type="curTab === 'evaluate' ? 'primary' : 'default'" size="large" @click="changeTab('evaluate')">评价统计</Button>
      </div>
      <!-- 考试统计 -->
      <div v-show="curTab === 'exam'">
        <div v-for="(item, index) in examList" :key="index" class="detail_content">
          <div>
            <span style="margin-right: 20px;">{{ index + 1 }}.{{ item.theme_exam_title }}</span>
            <span v-if="item.theme_exam_title_type === '1'" style="color: #aaa;">单选题</span>
            <span v-else-if="item.theme_exam_title_type === '2'" style="color: #aaa;">多选题</span>
            <Button v-else-if="item.theme_exam_title_type === '3'" type="text" @click="showDetail('exam', item.analysisArr)">详情</Button>
          </div>
          <div v-if="item.theme_exam_title_type === '3'">
            <div v-for="(itm, idx) in item.analysisArr" :key="idx">{{ itm.member_exam_analysis }}</div>
          </div>
          <div v-else>
            <div v-for="(itm, idx) in item.options" :key="idx" class="option_class" :class="item.theme_exam_answer.includes(itm.exam_option_code) ? 'red' : ''">
              {{ itm.exam_option_code }}. {{ itm.exam_option_content }}
            </div>
          </div>
        </div>
      </div>
      <!-- 评价统计 -->
      <div v-show="curTab === 'evaluate'">
        <div v-for="(item, index) in evaluateList" :key="index" class="detail_content">
          <div>
            <span style="margin-right: 20px;">{{ index + 1 }}.{{ item.theme_evaluate_title }}</span>
            <Button type="text" @click="showDetail('evaluate', item.memberEvalArr)">详情</Button>
          </div>
          <p>平均分：{{ item.member_evaluate_avg }}</p>
        </div>
      </div>
      <div class="demo-drawer-footer">
        <Button style="margin-right: 8px" @click="summaryModalData.modal = false">取消</Button>
      </div>
    </Drawer>
    <!-- 问答题详情内容 -->
    <Modal v-model="qADetailModal" :closable="false" width="480" class="detail_modal">
      <Table v-if="qADetailModal" border :columns="examColumns" :data="examTableList"></Table>
      <div slot="footer">
        <Button type="primary" @click="cancel('exam')">取消</Button>
      </div>
    </Modal>
    <!-- 评价统计详情内容 -->
    <Modal v-model="evaluateDetailModal" :closable="false" width="480" class="detail_modal">
      <Table v-if="evaluateDetailModal" border :columns="evaluateColumns" :data="evaluateTableList"></Table>
      <div slot="footer">
        <Button type="primary" @click="cancel('evaluate')">取消</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import { queryTrainExamInfo, queryTrainEvaluateInfo } from '@/api/examSystem/trainingModule/trainingTopics'
export default {
  props: {
    summaryModalData: Object
  },
  data () {
    return {
      curTab: 'exam',
      examList: [],
      evaluateList: [],
      qADetailModal: false,
      examTableList: [],
      evaluateDetailModal: false,
      evaluateTableList: [],
      examColumns: [
        {
          title: '内容',
          key: 'member_exam_analysis',
          align: 'center'
        },
        {
          title: '姓名',
          key: 'member_name',
          align: 'center'
        },
        {
          title: '电话',
          key: 'member_mobile',
          align: 'center'
        }
      ],
      evaluateColumns: [
        {
          title: '姓名',
          key: 'member_name',
          align: 'center'
        },
        {
          title: '电话',
          key: 'member_mobile',
          align: 'center'
        },
        {
          title: '评分',
          key: 'member_evaluate_score',
          align: 'center'
        }
      ]
    }
  },
  methods: {
    modalShow (val) {
      if (val) {
        this.getExamList()
      } else {
        this.curTab = 'exam'
        this.examList = []
        this.evaluateList = []
        this.examTableList = []
        this.evaluateTableList = []
      }
    },
    // 详情
    showDetail (type, val) {
      if (type === 'exam') {
        this.qADetailModal = true
        this.examTableList = val
      } else {
        this.evaluateDetailModal = true
        this.evaluateTableList = val
      }
    },
    cancel (type) {
      if (type === 'exam') {
        this.qADetailModal = false
        this.examTableList = []
      } else {
        this.evaluateDetailModal = false
        this.evaluateTableList = []
      }
    },
    // tab切换
    changeTab (type) {
      if (type === 'evaluate') {
        this.curTab = 'evaluate'
        this.getEvaluateList()
      } else {
        this.curTab = 'exam'
        this.getExamList()
      }
    },
    // 获取考试统计
    getExamList () {
      queryTrainExamInfo({ theme_id: this.summaryModalData.data.theme_id }).then(res => {
        if (res.data.Code === 10000) {
          this.examList = res.data.examArr
        }
      })
    },
    // 获取评价统计
    getEvaluateList () {
      queryTrainEvaluateInfo({ theme_id: this.summaryModalData.data.theme_id }).then(res => {
        if (res.data.Code === 10000) {
          this.evaluateList = res.data.evaluateArr
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.demo-drawer-footer {
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  background: #fff;
}
.theme_info {
  position: absolute;
  top: 14px;
  font-weight: normal;
  span {
    margin-left: 25px;
  }
}
.tab_btn {
  margin-bottom: 10px;
  button:first-child {
    margin-right: 10px;
  }
}
.option_class {
  line-height: 23px;
  position: relative;
  &&.red {
    color: #d9001b;
  }
}
.detail_content {
  margin-bottom: 5px;
  font-size: 14px;
  button {
    color: #2d8cf0;
    padding: 0;
  }
  p {
    line-height: 33px;
  }
}
</style>
<style lang="less">
.detail_modal {
  .ivu-modal-body {
    padding: 16px;
    font-size: 12px;
    line-height: 1.5;
    max-height: 585px;
    overflow-y: scroll;
  }
}
</style>

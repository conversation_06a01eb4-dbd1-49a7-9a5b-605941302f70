<template>
  <Modal v-model="modalData.modal" :title="modalData.title" width="300" @on-visible-change="modalShowHide" :mask-closable="false" @on-cancel=
  "clearData">
    <Form ref="formValide" :model="formData" :label-width="65" style="margin-bottom: 20px;">
      <FormItem label="名称" prop="dept_name" :rules="{ required: true, message: '部门名称不能为空！', trigger: 'blur'}">
        <span><Input type='text' v-model='formData.dept_name'></Input></span>
      </FormItem>
      <FormItem label="分管领导">
        <Select v-model="formData.leader_account_id" prop="leader_account_id" filterable label-in-value @on-change="leaderChange">
          <Option v-for="item in unifiedList" :key="item.unified_account_id" :value="item.unified_account_id">{{ item.user_name }}</Option>
        </Select>
      </FormItem>
      <FormItem label="手机号码">
        <span><Input type='text' v-model='formData.leader_mobile' :disabled="true"></Input></span>
      </FormItem>
      <FormItem label="负责人">
        <Select v-model="formData.charge_account_id" prop="charge_account_id" filterable label-in-value @on-change="chargeChange">
          <Option v-for="item in unifiedList" :key="item.unified_account_id" :value="item.unified_account_id">{{ item.user_name }}</Option>
        </Select>
      </FormItem>
      <FormItem label="手机号码">
        <span><Input type='text' v-model='formData.charge_mobile' :disabled="true"></Input></span>
      </FormItem>
    </Form>
    <div slot="footer">
      <Button style="margin-right: 8px" @click="clearData">取消</Button>
      <Button type="primary" @click="addHandle">保存</Button>
    </div>
  </Modal>
</template>
<script>
import API from '@/api/jurisdictionManage/departManagement'
import { queryUnifiedAccountList } from '@/api/jurisdictionManage/userManagement'

export default ({
  props: {
    modalData: Object
  },
  data () {
    return {
      unifiedList: [], // 一体化用户数据列表
      formData: {
        dept_name: '',
        leader_account_id: '',
        leader_mobile: '',
        charge_account_id: '',
        charge_mobile: ''
      }
    }
  },
  methods: {
    // 添加部门
    addHandle () {
      if (this.modalData.type === 'add') {
        this.$refs['formValide'].validate((valid) => {
          if (valid) {
            API.addDepartManagePage({
              dept_name: this.formData.dept_name,
              leader_account_id: this.formData.leader_account_id,
              charge_account_id: this.formData.charge_account_id
            }).then(res => {
              if (res.data.Code === 10000) {
                this.$Message.success(res.data.Message)
                this.clearData()
                this.$emit('modalDataBack')
              } else {
                this.$Message.error(res.data.Message)
              }
            })
          }
        })
      }
      if (this.modalData.type === 'modify') {
        this.$refs['formValide'].validate((valid) => {
          if (valid) {
            API.updateDepartManagePage({
              dept_id: this.formData.dept_id,
              dept_name: this.formData.dept_name,
              leader_account_id: this.formData.leader_account_id,
              charge_account_id: this.formData.charge_account_id,
              order_num: this.formData.order_num
            }).then(res => {
              if (res.data.Code === 10000) {
                this.$Message.success(res.data.Message)
                this.clearData()
                this.$emit('modalDataBack')
              } else {
                this.$Message.warning(res.data.Message)
              }
            })
          }
        })
      }
    },
    clearData () {
      this.formData = {
        dept_name: '',
        leader_account_id: '',
        leader_mobile: '',
        charge_account_id: '',
        charge_mobile: ''
      }
      this.$refs['formValide'].resetFields()
      this.modalData.modal = false
    },
    leaderChange (obj) {
      if (obj) {
        this.formData.leader_account_id = obj.value
        let _curList = this.unifiedList.filter(item => item.unified_account_id === obj.value)
        this.formData.leader_mobile = _curList[0].unified_account
      }
    },
    chargeChange (obj) {
      if (obj) {
        this.formData.charge_account_id = obj.value
        let _curList = this.unifiedList.filter(item => item.unified_account_id === obj.value)
        this.formData.charge_mobile = _curList[0].unified_account
      }
    },
    // 获取一体化用户列表数据
    getBaseData () {
      queryUnifiedAccountList().then(res => {
        if (res.data.Code === 10000) {
          this.unifiedList = res.data.Result
        }
      })
    },
    // 弹窗显隐
    modalShowHide (val) {
      if (val) {
        this.formData = { ...this.formData, ...this.modalData.data }
        this.getBaseData()
      }
    }
  }
})
</script>

<template>
  <Modal v-model="modalData.modal" :mask-closable="false" :title="modalData.title" width="500" @on-visible-change="modalShowHide" ok-text="保存" @on-ok="saveMenuConfig">
    <Row>
      <Col span="6">
        <H3 style="margin-top: 6px; padding-left: 6px; cursor: pointer;" :style="curUser === idx ? 'background:#cecbd4;' : ''" v-for="(item,idx) in userList" :key="'user' + idx" @click="userChange(idx)">
          <span>{{ item.userName }}</span>
        </H3>
      </Col>
      <!-- <Col span="6">
        <CheckboxGroup v-if="userList.length > 0">
          <Checkbox v-for="(item, idx) in userList[curUser].menuTree" :key="'menu1' + idx">{{ item.name }}</Checkbox>
        </CheckboxGroup>
      </Col> -->
      <Col span="16" offset="2">
        <Tree v-if="userList.length > 0 && treeDataShow" :data="userList[curUser].menuTree" show-checkbox multiple @on-check-change="checkChange"></Tree>
        <!-- <Tree :data="data4" show-checkbox multiple></Tree> -->
      </Col>
    </Row>
  </Modal>
</template>
<script>

import { queryDeptAccountMenu, addMenuConfigAll } from '@/api/jurisdictionManage/userManagement'

export default {
  props: {
    modalData: Object
  },
  data () {
    return {
      parentIds: [],
      treeDataShow: true, // 为了渲染用
      curUser: 0, // 当前用户
      firstMenuIdx: 0, // 第一菜单序号
      secondMenuIdx: 0, // 第二菜单序号
      submitQuery: {}, // 提交参数列表
      submitMenuQuery: [], // 提交的选中菜单数据
      listQuery: {
        deptId: ''
      },
      userList: [] // 部门用户列表
    }
  },
  methods: {
    // 弹窗显示隐藏
    modalShowHide (val) {
      if (val) {
        this.listQuery.deptId = this.modalData.data.dept_id
        this.getStaffList()
      } else {
        this.curUser = 0
        this.submitQuery = {}
        this.submitMenuQuery = []
      }
    },
    checkChange () {
      this.treeDataShow = false
      this.$nextTick(() => {
        this.treeDataShow = true
      })
    },
    updateTreeData (treeData, checkedKeys) {
      // 遍历树的数据，更新勾选状态
      treeData.forEach(node => {
        if (node[checkedKeys]) {
          node.checked = true
        } else {
          node.checked = false
        }

        if (node.children) {
          this.updateTreeData(node.children, checkedKeys)
        }
      })
    },
    // 获取部门人员列表
    async getStaffList () {
      await queryDeptAccountMenu(this.listQuery).then(res => {
        if (res.data.Code === 10000) {
          this.userList = res.data.Result
          this.userList.forEach(item => {
            this.replaceKeys(item.menuTree)
          })
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 保存配置
    saveMenuConfig () {
      if (this.userList.length > 0) {
        Object.assign(this.submitQuery, {
          deptId: this.userList[0].deptId
        })
        this.userList.forEach((item, idx) => {
          this.getUserObj(item.unifiedAccountId, item)
        })
        if (this.parentIds.length > 0) { // 父节点去重，清空数据
          // this.parentIds = this.parentIds.filter((item, idx, self) => {
          //   return idx === self.findIndex(obj => {
          //     return obj.unifiedAccountId === item.unifiedAccountId && obj.menuId === item.menuId
          //   })
          // })
          this.parentIds = this.parentIds.filter(item => item.menuId !== '' && item.menuId !== undefined && item.menuId !== null)
          this.submitMenuQuery = [...this.submitMenuQuery, ...this.parentIds]
        }
        // 最后数组再去重
        this.submitMenuQuery = this.submitMenuQuery.filter((item, idx, self) => {
          return self.findIndex(obj => (
            obj.unifiedAccountId === item.unifiedAccountId && parseInt(obj.menuId) === parseInt(item.menuId)
          )) === idx
        })
        Object.assign(this.submitQuery, {
          configArray: JSON.stringify(this.submitMenuQuery)
        })
      }
      addMenuConfigAll(this.submitQuery).then(res => {
        if (res.data.Code === 10000) {
          this.$Message.success(res.data.Message)
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 用户切换
    userChange (idx) {
      this.curUser = idx
      // this.replaceKeys(this.userList[this.curUser].menuTree)
    },
    // 依次返回用户已选择数据内容
    getUserObj (unifiedAccountId, item) {
      this.getIsCheckObj(unifiedAccountId, item.menuTree)
    },
    // 遍历树结构找出选中元素对象
    getIsCheckObj (unifiedAccountId, item) {
      item.forEach(list => {
        if (list.checked) {
          let _parentIdsArr = list.idPath.split('>')
          let _parentIds = _parentIdsArr.map(item => {
            return {
              unifiedAccountId: unifiedAccountId,
              menuId: item
            }
          })
          _parentIds.splice(-2)
          this.parentIds = [...this.parentIds, ..._parentIds]
          this.submitMenuQuery.push({
            unifiedAccountId: unifiedAccountId,
            menuId: list.id
          })
        }
        if (list.children) {
          this.getIsCheckObj(unifiedAccountId, list.children)
        }
      })
    },
    // 针对后台与前端子节点未全选父节点勾选处理，遍历处理父节点勾选状态
    checkParentSelect (node) {
      if (!node.parent) {
        return // 如果节点没有父节点，直接返回
      }
      const parent = node.parent
      const hasUncheckedChild = parent.children.some(child => !child.checked)
      if (hasUncheckedChild) {
        parent.checked = false
      } else {
        parent.checked = true
      }
      this.checkParentSelect(parent) // 递归调用，更新更上层的父节点和祖父节点
    },
    checkNodeChildren (node) {
      if (!node.children || node.children.length === 0) {
        return node.enabled === 1 // 如果节点没有子节点，直接返回节点的 checked 属性
      }

      for (const child of node.children) {
        if (!this.checkNodeChildren(child)) {
          return false // 如果存在子节点的 checked 属性为 false，立即返回 false
        }
      }

      return true // 如果所有子节点的 checked 属性都为 true，返回 true
    },
    // 内容key值替换
    replaceKeys (tree) {
      tree.forEach(node => {
        if (node['name']) {
          node['title'] = node['name']
          delete node['name']
        }
        // node['checked'] = node['enabled']
        node['checked'] = this.checkNodeChildren(node) // node.checked === 1
        delete node['enabled']
        if (node.children) {
          this.replaceKeys(node.children)
        }
      })
    }
  }
}
</script>

<template>
  <Modal v-model="modalData.modal" :title="modalData.title" width="500" @on-visible-change="modalShowHide" :mask-closable="false">
    <Button class="add-part-btn" type="primary" @click="addPostModal">添加职务</Button>
    <Table class="table-list" border :loading="loading" ref="selection" :columns="columns" :data="postList"></Table>
    <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
      :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
    <div slot="footer">
      <Button type="primary" @click="backModal">返回</Button>
    </div>
    <EditPostModal :modalData="postDataObj" @postDataBack="getList"></EditPostModal>
  </Modal>
</template>
<script>
import API from '@/api/jurisdictionManage/departManagement'
import EditPostModal from './postEditModal.vue'

export default ({
  components: {
    EditPostModal
  },
  props: {
    modalData: Object
  },
  data () {
    return {
      loading: false,
      total: 0,
      listCurrent: 1,
      listQuery: {
        pageSize: 10,
        pageIndex: 1
      },
      postList: [], // 职务列表
      postDataObj: {
        modal: false,
        type: '',
        title: '',
        data: {}
      },
      columns: [
        {
          title: '职位',
          key: 'post_name',
          align: 'center'
        },
        {
          title: '操作',
          align: 'center',
          width: 180,
          render: (h, params) => {
            return h('div', [
              h('Button', {
                style: {
                  margin: '0 5px'
                },
                props: {
                  icon: 'md-brush',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.postDataObj = {
                      modal: true,
                      type: 'modify',
                      title: '职务编辑',
                      data: params.row
                    }
                  }
                }
              }, '编辑'),
              h('Button', {
                style: {
                  margin: '0 5px'
                },
                props: {
                  icon: 'md-trash',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.$Modal.confirm({
                      title: '提示',
                      content: '<p>确认删除该职位？</p>',
                      loading: true,
                      onOk: () => {
                        API.delPostManagePage({
                          post_id: params.row.post_id
                        }).then(res => {
                          this.loading = false
                          this.$Modal.remove()
                          if (res.data.Code === 10000) {
                            this.$Message.success(res.data.Message)
                            this.getList()
                          } else {
                            this.$Message.error(res.data.Message)
                          }
                        })
                      }
                    })
                  }
                }
              }, '删除')
            ])
          }
        }
      ]
    }
  },
  methods: {
    // 获取列表
    getList () {
      this.loading = true
      API.queryPostManagePage(this.listQuery).then(res => {
        this.loading = false
        if (res.data.Code === 10000) {
          this.postList = res.data.Result
          this.total = res.data.Total
        }
      })
    },
    // 添加职务
    addPostModal () {
      this.postDataObj = {
        modal: true,
        type: 'add',
        title: '新增职务',
        data: {
          dept_id: this.modalData.data.dept_id,
          dept_name: this.modalData.data.dept_name,
          post_name: ''
        }
      }
    },
    backModal () {
      this.modalData.modal = false
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    },
    modalShowHide (val) {
      if (val) {
        Object.assign(this.listQuery, {
          dept_id: this.modalData.data.dept_id
        })
        this.getList()
      }
    }
  }
})
</script>
<style lang="less">
  .add-part-btn {
    position: absolute;
    right: 16px;
  }
  .table-list {
    margin-top: 40px;
  }
</style>

<template>
  <div>
    <Card>
      <Button icon="md-add" @click="handleModal('create')" class="module_add_btn">模块新增</Button>
      <div v-show="moduleDataList.length === 0" class="null-data">暂无数据</div>
      <Row :gutter="40" class="data_list">
        <Col span="6">
        <div class="list" :style="curModule === idx ? 'background: #3F96F1;' : ''" v-for="(item, idx) in moduleDataList"
          @click="getFirstMenu(item, idx)" :key="idx">
          <b>{{ item.name }}</b>
          <p>
            <Button type="text" @click="handleModal('update', item)" class="update_btn">编辑</Button>
          </p>
        </div>
        </Col>
        <Col span="4">
        <!-- <Button class="menu_class" long type="primary" v-for="(item, idx) in firstMenuList" :key="'firstMenu' + idx" @click="getSecondMenu(item)">{{ item.name }}</Button> -->
        <div class="list" :style="curFirstMenu === idx ? 'background: #3F96F1;' : ''"
          v-for="(item, idx) in firstMenuList" @click="getSecondMenu(item, idx)" :key="'firstMenu' + idx">
          <b>{{ item.name }}</b>
          <p>
            <Button type="text" @click="firstMenuModify('modify', item)" class="update_btn">编辑</Button>
            <Button style="margin-left: 10px;" type="text" @click="firstMenuDel(item)" class="update_btn">删除</Button>
          </p>
        </div>
        <div>
          <Button class="menu_class" long type="primary" icon="md-add" @click="firstMenuModify('add')"></Button>
        </div>
        </Col>
        <Col span="4">
        <div class="list" v-for="(item, idx) in secondMenuList" :key="'firstMenu' + idx">
          <b>{{ item.name }}</b>
          <p>
            <Button type="text" @click="secondMenuModify('modify', item)" class="update_btn">编辑</Button>
            <Button style="margin-left: 10px;" type="text" @click="secondMenuDel(item)" class="update_btn">删除</Button>
          </p>
        </div>
        <div v-if="firstMenuList && firstMenuList.length > 0">
          <Button class="menu_class" long type="primary" icon="md-add" @click="secondMenuModify('add')"></Button>
        </div>
        </Col>
      </Row>
    </Card>
    <!-- 大栏目弹窗内容 -->
    <Modal v-model="moduleEditModal" :title="moduleEditModalTitle" :mask-closable="false" width="320">
      <div class="modal_list_div">
        <label>名称：</label>
        <Input v-model="listQuery.name" style="width: 245px" />
      </div>
      <div class="modal_list_div">
        <label>地址：</label>
        <Input v-model="listQuery.href" style="width: 245px" />
      </div>
      <div class="modal_list_div">
        <label>背景：</label>
        <Input v-model="listQuery.imageUrl" style="width: 245px" />
      </div>
      <div slot="footer">
        <Button type="primary" @click="cancel">取消</Button>
        <Button v-if="curType == 'create'" type="primary" @click="createData">保存</Button>
        <Button v-else type="primary" @click="updateData">保存</Button>
      </div>
    </Modal>
    <!-- 一级菜单弹窗内容 -->
    <Modal v-model="firstEditModal" :title="firstTitle" :mask-closable="false" width="420" ok-text="保存"
      @on-ok="handAddFirstMenu" @on-visible-change="firstModalShowHide">
      <Form ref="firstMenuRef" :model="firstMenuObj" :label-width="100" :rules="ruleFirstValidate">
        <FormItem label="菜单名称" prop="name">
          <Input v-model="firstMenuObj.name" placeholder="请输入菜单名称" />
        </FormItem>
        <FormItem label="菜单icon" prop="icon">
          <Input v-model="firstMenuObj.icon" placeholder="请输入菜单icon" />
        </FormItem>
        <FormItem label="是否有子菜单">
          <RadioGroup v-model="firstMenuObj.childFlag">
            <Radio :label="1">是</Radio>
            <Radio :label="0">否</Radio>
          </RadioGroup>
        </FormItem>
        <FormItem v-if="!firstMenuObj.childFlag" label="菜单地址">
          <Input v-model="firstMenuObj.href" placeholder="请输入菜单地址" />
        </FormItem>
        <FormItem v-if="!firstMenuObj.childFlag" label="组件路径">
          <Input v-model="firstMenuObj.component" placeholder="请输入组件路径" />
        </FormItem>
        <FormItem label="是否在菜单展示">
          <RadioGroup v-model="firstMenuObj.menuDisplay">
            <Radio :label="1">是</Radio>
            <Radio :label="0">否</Radio>
          </RadioGroup>
        </FormItem>
        <FormItem v-if="!firstMenuObj.childFlag" label="移动端展示">
          <RadioGroup v-model="firstMenuObj.mobileDisplay">
            <Radio :label="1">是</Radio>
            <Radio :label="0">否</Radio>
          </RadioGroup>
        </FormItem>
        <FormItem label="序号">
          <Input v-model="firstMenuObj.orderNum" placeholder="请输入序号" />
        </FormItem>
      </Form>
    </Modal>
    <!-- 二级菜单弹窗内容 -->
    <Modal v-model="secondEditModal" :title="secondTitle" :mask-closable="false" width="420" ok-text="保存"
      @on-ok="handAddSecondMenu" @on-visible-change="secondModalShowHide">
      <Form ref="secondMenuRef" :model="secondMenuObj" :label-width="100" :rules="ruleSecondValidate">
        <FormItem label="菜单名称" prop="name">
          <Input v-model="secondMenuObj.name" placeholder="请输入菜单名称" />
        </FormItem>
        <FormItem label="菜单icon" prop="icon">
          <Input v-model="secondMenuObj.icon" placeholder="请输入菜单icon" />
        </FormItem>
        <FormItem label="菜单地址">
          <Input v-model="secondMenuObj.href" placeholder="请输入菜单地址" />
        </FormItem>
        <FormItem label="组件路径">
          <Input v-model="secondMenuObj.component" placeholder="请输入组件路径" />
        </FormItem>
        <FormItem label="序号">
          <Input v-model="secondMenuObj.orderNum" placeholder="请输入序号" />
        </FormItem>
        <FormItem label="是否在菜单展示">
          <RadioGroup v-model="secondMenuObj.menuDisplay">
            <Radio :label="1">是</Radio>
            <Radio :label="0">否</Radio>
          </RadioGroup>
        </FormItem>
        <FormItem label="移动端展示">
          <RadioGroup v-model="secondMenuObj.mobileDisplay">
            <Radio :label="1">是</Radio>
            <Radio :label="0">否</Radio>
          </RadioGroup>
        </FormItem>
      </Form>
    </Modal>

    <!-- 智能体编辑弹窗 -->
    <Modal v-model="agentEditModal" title="智能体编辑" :mask-closable="false" width="420" ok-text="保存"
      @on-ok="handAgentEdit" @on-visible-change="agentModalShowHide">
      <Form ref="agentEditRef" :model="agentObj" :label-width="100" :rules="ruleAgentValidate">
        <FormItem label="智能体名称" prop="name">
          <Input v-model="agentObj.name" placeholder="请输入智能体名称" />
        </FormItem>
        <FormItem label="apiKey" prop="href">
          <Input v-model="agentObj.href" placeholder="请输入apiKey" />
        </FormItem>
        <FormItem label="icon" prop="icon">
          <Input v-model="agentObj.component.icon" placeholder="请输入icon" />
        </FormItem>
        <FormItem label="iconColor" prop="iconColor">
          <Input v-model="agentObj.component.iconColor" placeholder="请输入iconColor" />
        </FormItem>
        <FormItem label="分类" prop="category">
          <Input v-model="agentObj.component.category" placeholder="请输入分类" />
        </FormItem>
        <FormItem label="描述" prop="description">
          <Input v-model="agentObj.component.description" placeholder="请输入描述" />
        </FormItem>
        <FormItem label="是否推荐">
          <RadioGroup v-model="agentObj.component.featured">
            <Radio :label="'1'">是</Radio>
            <Radio :label="'0'">否</Radio>
          </RadioGroup>
        </FormItem>
        <FormItem label="功能">
          <Input v-model="agentObj.component.features" placeholder="请输入功能,多个用逗号隔开" />
        </FormItem>
        <FormItem label="序号">
          <Input v-model="agentObj.orderNum" placeholder="请输入序号" />
        </FormItem>
      </Form>
    </Modal>
  </div>
</template>
<script>
import API from '@/api/jurisdictionManage/moduleManagement'
export default {
  data() {
    return {
      curModule: 0, // 当前选中模块
      curFirstMenu: 0, // 当前选中一级菜单
      curType: '',
      moduleDataList: [],
      moduleEditModal: false,
      moduleEditModalTitle: '',
      firstType: '', // 第一菜单弹窗类型
      secondType: '', // 第二菜单弹窗类型
      agentType: '', // 智能体弹窗类型
      firstParentId: '', // 点击的主栏目id，用于后期数据提交使用
      secondParentId: '', // 点击的第一菜单id，用于后期数据提交使用
      firstEditModal: false, // 第一个弹窗显隐标识
      firstTitle: '一级菜单编辑', // 第一个弹窗标题
      ruleFirstValidate: {
        name: [{ required: true, message: '此处不能为空', trigger: 'blur' }],
        icon: [{ required: true, message: '此处不能为空', trigger: 'blur' }]
      },
      ruleSecondValidate: {
        name: [{ required: true, message: '此处不能为空', trigger: 'blur' }],
        icon: [{ required: true, message: '此处不能为空', trigger: 'blur' }]
      },
      ruleAgentValidate: {
        name: [{ required: true, message: '此处不能为空', trigger: 'blur' }],
        href: [{ required: true, message: '此处不能为空', trigger: 'blur' }]
      },
      firstMenuList: [],
      firstMenuObj: {
        name: '',
        icon: '',
        childFlag: 0,
        href: '',
        component: '',
        menuDisplay: 1,
        mobileDisplay: 0,
        orderNum: null
      }, // 一级菜单信息
      secondEditModal: false, // 第二个弹窗显隐标识
      agentEditModal: false, // 智能体编辑弹窗显隐标识
      secondTitle: '二级菜单编辑', // 第二个弹窗标题
      secondMenuList: [],
      secondMenuObj: {
        name: '',
        icon: '',
        childFlag: 0,
        href: '',
        component: '',
        menuDisplay: 1,
        mobileDisplay: 0,
        orderNum: null
      },
      listQuery: {
        name: '',
        href: '',
        imageUrl: '',
        module_id: ''
      },
      agentObj: {
        name: '',
        href: '',
        component: {
          icon: '',
          iconColor: '',
          category: '',
          description: '',
          featured: '0',
          features: '',
        },
        icon: '',
        childFlag: 0,
        menuDisplay: 1,
        mobileDisplay: 0,
        orderNum: null
      }
    }
  },
  computed: {
    isAgentEdit() {
      if (this.firstMenuList[this.curFirstMenu]) {
        return this.firstMenuList[this.curFirstMenu].href === 'agent'
      }
      return false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 获取模务列表
    getList() {
      API.queryMenuList({ parentId: 0 }).then(res => {
        if (res.data.Code === 10000) {
          this.moduleDataList = res.data.Result
          if (this.moduleDataList.length > 0) {
            this.getFirstMenu(this.moduleDataList[0], this.curModule)
          }
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 一级菜单显隐
    firstModalShowHide(val) {
      if (!val) {
        this.firstMenuObj = {
          name: '',
          icon: '',
          childFlag: 0,
          href: '',
          component: '',
          menuDisplay: 1,
          mobileDisplay: 0,
          orderNum: null
        }
      }
    },
    // 二级菜单显隐
    secondModalShowHide(val) {
      if (!val) {
        this.secondMenuObj = {
          name: '',
          icon: '',
          childFlag: 0,
          href: '',
          component: '',
          menuDisplay: 1,
          mobileDisplay: 0,
          orderNum: null
        }
      }
    },
    // 智能体显隐
    agentModalShowHide(val) {
      if (!val) {
        this.agentObj = {
          name: '',
          href: '',
          component: {
            icon: '',
            iconColor: '',
            category: '',
            description: '',
            featured: '0',
            features: '',
          },
          icon: '',
          childFlag: 0,
          menuDisplay: 1,
          mobileDisplay: 0,
          orderNum: null
        }
      }
    },
    // 获取一级菜单
    getFirstMenu(item, idx) {
      this.curModule = idx
      this.firstParentId = item.id
      this.firstMenuList = []
      this.secondMenuList = []
      this.curFirstMenu = 0
      API.queryMenuList({ parentId: item.id }).then(res => {
        if (res.data.Code === 10000) {
          this.firstMenuList = res.data.Result
          if (this.firstMenuList.length > 0) {
            this.getSecondMenu(this.firstMenuList[0], this.curFirstMenu)
          }
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 新增、编辑保存一级菜单
    async handAddFirstMenu() {
      if (this.firstType === 'add') {
        Object.assign(this.firstMenuObj, {
          parentId: this.firstParentId
        })
        await API.addMenu(this.firstMenuObj).then(res => {
          if (res.data.Code === 10000) {
            this.$Message.success(res.data.Message)
            this.getFirstMenu({ id: this.firstParentId }, this.curModule)
          } else {
            this.$Message.error(res.data.Message)
          }
        })
      }
      if (this.firstType === 'modify') {
        Object.assign(this.firstMenuObj, {
          children: [1]
        })
        await API.updateMenu(this.firstMenuObj).then(res => {
          if (res.data.Code === 10000) {
            this.$Message.success(res.data.Message)
            this.getFirstMenu({ id: this.firstParentId }, this.curModule)
          } else {
            this.$Message.error(res.data.Message)
          }
        })
      }
      this.firstMenuObj = {
        name: '',
        icon: '',
        childFlag: 0,
        href: '',
        component: '',
        menuDisplay: 1,
        mobileDisplay: 0,
        orderNum: null
      }
      this.$refs['firstMenuRef'].resetFields()
    },
    // 新增、编辑保存二级菜单
    async handAddSecondMenu() {
      Object.assign(this.secondMenuObj, {
        parentId: this.secondParentId
      })
      if (this.secondType === 'add') {
        await API.addMenu(this.secondMenuObj).then(res => {
          if (res.data.Code === 10000) {
            this.$Message.success(res.data.Message)
            this.getSecondMenu({ id: this.secondParentId }, this.curFirstMenu)
          } else {
            this.$Message.error(res.data.Message)
          }
        })
      }
      if (this.secondType === 'modify') {
        await API.updateMenu(this.secondMenuObj).then(res => {
          if (res.data.Code === 10000) {
            this.$Message.success(res.data.Message)
            this.getSecondMenu({ id: this.secondParentId }, this.curFirstMenu)
          } else {
            this.$Message.error(res.data.Message)
          }
        })
      }
      this.secondMenuObj = {
        name: '',
        icon: '',
        childFlag: 0,
        href: '',
        component: '',
        menuDisplay: 1,
        mobileDisplay: 0,
        orderNum: null
      }
      this.$refs['secondMenuRef'].resetFields()
    },
    // 新增、编辑保存智能体
    async handAgentEdit() {
      Object.assign(this.agentObj, {
        parentId: this.secondParentId
      })
      if (this.agentType === 'add') {
        await API.addMenu({...this.agentObj,component: JSON.stringify(this.agentObj.component)}).then(res => {
          if (res.data.Code === 10000) {
            this.$Message.success(res.data.Message)
            this.getSecondMenu({ id: this.secondParentId }, this.curFirstMenu)
          } else {
            this.$Message.error(res.data.Message)
          }
        })
      }
      if (this.agentType === 'modify') {
        await API.updateMenu({...this.agentObj,component: JSON.stringify(this.agentObj.component)}).then(res => {
          if (res.data.Code === 10000) {
            this.$Message.success(res.data.Message)
            this.getSecondMenu({ id: this.secondParentId }, this.curFirstMenu)
          } else {
            this.$Message.error(res.data.Message)
          }
        })
      }
      this.agentObj = {
        name: '',
        href: '',
        component: {
          icon: '',
          iconColor: '',
          category: '',
          description: '',
          featured: '0',
          features: '',
        },
        icon: '',
        childFlag: 0,
        menuDisplay: 1,
        mobileDisplay: 0,
        orderNum: null
      }
      this.$refs['agentEditRef'].resetFields()

    },
    // 获取二级菜单
    getSecondMenu(item, idx) {
      this.curFirstMenu = idx
      this.secondParentId = item.id
      this.secondMenuList = []
      API.queryMenuList({ parentId: item.id }).then(res => {
        if (res.data.Code === 10000) {
          this.secondMenuList = res.data.Result
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 开启模块编辑/新增弹窗
    handleModal(type, row) {
      this.curType = type
      this.moduleEditModal = true
      if (type === 'create') {
        this.moduleEditModalTitle = '模块新增'
      } else {
        this.moduleEditModalTitle = '模块编辑'
        this.listQuery = {
          menuId: row.id,
          name: row.name,
          href: row.href,
          imageUrl: row.imageUrl,
          module_id: row.module_id
        }
      }
    },
    // 保存新增
    createData() {
      if (this.listQuery.name === '') return this.$Message.error('模块名称不能为空！')
      Object.assign(this.listQuery, {
        parentId: 0,
        menuDisplay: 1
      })
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确认新增该模块？</p>',
        loading: true,
        onOk: () => {
          API.addMenu(this.listQuery).then(res => {
            if (res.data.Code === 10000) {
              this.loading = false
              this.$Modal.remove()
              this.$Message.success(res.data.Message)
              this.getList()
              this.cancel()
            } else {
              this.loading = false
              this.$Modal.remove()
              this.$Message.error(res.data.Message)
            }
          })
        }
      })
    },
    // 修改保存
    updateData() {
      if (this.listQuery.name === '') return this.$Message.error('模块名称不能为空！')
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确认修改模块信息？</p>',
        loading: true,
        onOk: () => {
          API.updateMenu(this.listQuery).then(res => {
            if (res.data.Code === 10000) {
              this.loading = false
              this.$Modal.remove()
              this.$Message.success(res.data.Message)
              this.getList()
              this.cancel()
            } else {
              this.loading = false
              this.$Modal.remove()
              this.$Message.error(res.data.Message)
            }
          })
        }
      })
    },
    // 一级菜单新增、编辑
    firstMenuModify(type, item) {
      this.firstType = type
      if (type === 'modify') {
        this.firstMenuObj = item
        Object.assign(this.firstMenuObj, {
          menuId: item.id
        })
      }
      this.firstEditModal = true
    },
    // 一级菜单删除
    firstMenuDel(item) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确定要删除该菜单？</p>',
        loading: true,
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          API.delMenu({ idPath: item.idPath }).then(res => {
            this.$Modal.remove()
            if (res.data.Code === 10000) {
              this.$Message.success(res.data.Message)
              this.getFirstMenu({ id: this.firstParentId }, this.curModule)
            } else {
              this.$Message.error(res.data.Message)
            }
          })
        }
      })
    },
    // 二级菜单新增、编辑
    secondMenuModify(type, item) {
      if (this.isAgentEdit) {
        this.agentType = type
        if (type === 'modify') {
          this.agentObj = {...item,
            component: JSON.parse(item.component)
          }
          Object.assign(this.agentObj, {
            menuId: item.id
          })
        }
        
        this.agentEditModal = true
      } else {
        this.secondType = type
        if (type === 'modify') {
          this.secondMenuObj = item
          Object.assign(this.secondMenuObj, {
            menuId: item.id
          })
        }
        this.secondEditModal = true
      }
    },
    // 二级菜单删除
    secondMenuDel(item) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确定要删除该菜单？</p>',
        loading: true,
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          API.delMenu({ idPath: item.idPath }).then(res => {
            this.$Modal.remove()
            if (res.data.Code === 10000) {
              this.getSecondMenu({ id: this.secondParentId }, this.curFirstMenu)
              this.$Message.success(res.data.Message)
            } else {
              this.$Message.error(res.data.Message)
            }
          })
        }
      })
    },
    // 关闭模块弹窗
    cancel() {
      this.listQuery = {
        name: '',
        href: '',
        imageUrl: '',
        menuId: ''
      }
      this.moduleEditModal = false
    }
  }
}
</script>
<style lang="less" scoped>
.null-data {
  padding: 15px 0;
  text-align: center;
}

.modal_list_div {
  margin-bottom: 20px;
}

.data_list {
  .list {
    padding: 10px 15px;
    border-radius: 5px;
    position: relative;
    margin-bottom: 15px;
    background-color: #EEF4FF;

    b {
      color: #000;
      font-size: 18px;
      display: block;
      margin-bottom: 10px;
    }

    .update_btn {
      color: #1943A9;
      padding: 0;
      font-size: 14px;
    }

    &::after {
      content: '';
      width: 28px;
      height: 28px;
      display: block;
      top: 33%;
      right: 20px;
      position: absolute;
      background: url(../../../assets/images/seaction.png) no-repeat center;
    }
  }
}

.module_add_btn {
  color: #fff;
  border: none;
  font-size: 16px;
  letter-spacing: 2px;
  margin-bottom: 25px;
  // padding: 8px 10px;
  background-color: #1943A9;
}

.menu_class {
  margin-bottom: 10px;
  font-size: 14px;
}
</style>

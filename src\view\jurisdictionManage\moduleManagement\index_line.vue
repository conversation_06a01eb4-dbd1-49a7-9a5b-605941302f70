<template>
  <div>
    <Card>
      <Button icon="md-add" @click="handleModal('create')" class="module_add_btn">模块新增</Button>
      <div v-show="moduleDataList.length === 0" class="null-data">暂无数据</div>
      <Row :gutter="40" class="data_list">
        <Col offset="1" span="6" class="list" v-for="(item, idx) in moduleDataList" :key="idx">
          <b>{{ item.module_name }}</b>
          <p>
            <Button type="text" @click="handleModal('update', item)" class="update_btn">编辑</Button>
          </p>
        </Col>
        <!-- <Col span="6">
          <div class="list" v-for="(item, idx) in moduleDataList" :key="idx">
            <b>{{ item.module_name }}</b>
            <p>
              <Button type="text" @click="handleModal('update', item)" class="update_btn">编辑</Button>
            </p>
          </div>
        </Col>
        <Col span="6">
          菜单列表
        </Col>
        <Col span="12">
          编辑子菜单信息
        </Col> -->
      </Row>
    </Card>
    <!-- 大栏目弹窗内容 -->
    <Modal v-model="moduleEditModal" :title="moduleEditModalTitle" :mask-closable="false" width="320">
      <div class="modaldiv">
        <label>名称：</label>
        <Input v-model="listQuery.module_name" style="width: 245px" />
      </div>
      <div slot="footer">
        <Button type="primary" @click="cancel">取消</Button>
        <Button v-if="curType=='create'" type="primary" @click="createData">保存</Button>
        <Button v-else type="primary" @click="updateData">保存</Button>
      </div>
    </Modal>
    <!-- 一级菜单弹窗内容 -->
    <Modal v-model="firstEditModal" :title="firstTitle" :mask-closeable="false" width="420">
      <Form :label-width="300">
        <FormItem label="菜单名称">
          <Input placeholder="请输入菜单名称" />
        </FormItem>
        <FormItem label="菜单icon">
          <Input placeholder="请输入菜单icon" />
        </FormItem>
        <FormItem label="是否有子菜单">
          <Radio>是</Radio>
          <Radio>否</Radio>
        </FormItem>
        <FormItem label="序号">
          <Input placeholder="请输入序号" />
        </FormItem>
      </Form>
    </Modal>
  </div>
</template>
<script>
import API from '@/api/jurisdictionManage/moduleManagement'
export default {
  data () {
    return {
      curType: '',
      moduleDataList: [],
      moduleEditModal: false,
      moduleEditModalTitle: '',
      firstEditModal: false, // 第一个弹窗显隐标识
      firstTitle: '', // 第一个弹窗标题
      secondEditModal: false, // 第二个弹窗显隐标识
      secondTitle: '', // 第二个弹窗标题
      listQuery: {
        module_name: '',
        module_id: ''
      }
    }
  },
  created () {
    this.getList()
  },
  methods: {
    // 获取职务列表
    getList () {
      API.querymoduleList().then(res => {
        if (res.data.Code === 10000) {
          this.moduleDataList = res.data.Result
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 开启模块编辑/新增弹窗
    handleModal (type, row) {
      this.curType = type
      this.moduleEditModal = true
      if (type === 'create') {
        this.moduleEditModalTitle = '模块新增'
      } else {
        this.moduleEditModalTitle = '模块编辑'
        this.listQuery = {
          module_name: row.module_name,
          module_id: row.module_id
        }
      }
    },
    // 保存新增
    createData () {
      if (this.listQuery.module_name === '') return this.$Message.error('模块名称不能为空！')
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确认新增该模块？</p>',
        loading: true,
        onOk: () => {
          API.addModuleData({ module_name: this.listQuery.module_name }).then(res => {
            if (res.data.Code === 10000) {
              this.loading = false
              this.$Modal.remove()
              this.$Message.success(res.data.Message)
              this.getList()
              this.cancel()
            } else {
              this.loading = false
              this.$Modal.remove()
              this.$Message.error(res.data.Message)
            }
          })
        }
      })
    },
    // 修改保存
    updateData () {
      if (this.listQuery.module_name === '') return this.$Message.error('模块名称不能为空！')
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确认修改模块名称？</p>',
        loading: true,
        onOk: () => {
          API.updateModuleData(this.listQuery).then(res => {
            if (res.data.Code === 10000) {
              this.loading = false
              this.$Modal.remove()
              this.$Message.success(res.data.Message)
              this.getList()
              this.cancel()
            } else {
              this.loading = false
              this.$Modal.remove()
              this.$Message.error(res.data.Message)
            }
          })
        }
      })
    },
    // 关闭模块弹窗
    cancel () {
      this.listQuery = {
        module_name: '',
        module_id: ''
      }
      this.moduleEditModal = false
    }
  }
}
</script>
<style lang="less" scoped>
.null-data {
  padding: 15px 0;
  text-align: center;
}
.data_list {
  .list {
    padding: 10px 15px;
    border-radius: 5px;
    position: relative;
    margin-bottom: 15px;
    background-color: #EEF4FF;
    b {
      color: #000;
      font-size: 18px;
      display: block;
      margin-bottom: 10px;
    }
    .update_btn {
      color: #1943A9;
      padding: 0;
      font-size: 14px;
    }
    &::after {
      content: '';
      width: 28px;
      height: 28px;
      display: block;
      top: 33%;
      right: 20px;
      position: absolute;
      background: url(../../../assets/images/seaction.png) no-repeat center;
    }
  }
}
.module_add_btn {
  color: #fff;
  border: none;
  font-size: 16px;
  letter-spacing: 2px;
  margin-bottom: 25px;
  // padding: 8px 10px;
  background-color: #1943A9;
}
</style>

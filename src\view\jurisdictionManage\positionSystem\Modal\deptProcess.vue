<template>
  <div>
    <Modal v-model="modalData.modal" :title="modalData.title" width="900" @on-visible-change="modalVisible" :mask-closable="false">
      <Select v-model="form_num" filterable class="template-area">
        <Option v-for="(itm, idx) in templateList" :key="idx" :value="itm.value">{{ itm.label }}</Option>
      </Select>
      <span v-for="(item, idx) in approveList" :key="item.flow_node_id">
        <Select v-show="idx === 0" v-model="item.idStr" multiple filterable class="member_area">
          <Option v-for="(itm, index) in memberList" :key="'id' + index" :value="itm.unified_account_id">{{ itm.user_name }}</Option>
        </Select>
        <Select v-show="idx > 0" v-model="item.idStr" multiple filterable class="member_area">
          <Option v-for="(itm, index) in allMemberList" :key="'allId' + index" :value="itm.unified_account_id">{{ itm.user_name }}</Option>
        </Select>
        <Icon v-if="idx < (approveList.length - 1)" type="md-arrow-round-forward" size="30" color="#57a3f3"/>
        <span v-if="idx === (approveList.length - 1)">
          <Button class="option_btn" type="primary" size="small" @click="addNode(item)" icon="md-add"></Button>
          <Button v-if="approveList.length > 1" class="option_btn" type="error" size="small" @click="delNode(item)" icon="md-remove"></Button>
        </span>
      </span>
      <div slot="footer">
        <Button @click="handleCancel">取消</Button>
        <Button @click="handleSave" type="primary">保存</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import API from '@/api/jurisdictionManage/deptProcess'

export default ({
  props: {
    modalData: Object
  },
  data () {
    return {
      form_num: 0, // 默认模板字符
      queryMember: {
        dept_id: '', // 部门id
        user_name: '', // 用户名
        never_used: 0 // 1保留其它节点未选过的；0或其他不过滤
      },
      approveList: [{}], // 流程列表
      memberList: [], // 部门成员列表
      allMemberList: [], // 所有成员列表
      templateList: [
        {
          value: 0,
          label: '通用模板'
        },
        {
          value: 1,
          label: '运营部模板'
        },
        {
          value: 2,
          label: '市场部模板'
        },
        {
          value: 3,
          label: '财务部模板'
        },
        {
          value: 4,
          label: '审计部模板'
        }
      ]
    }
  },
  methods: {
    // 获取成员列表
    getMemberList () {
      API.queryUnifiedNodeAccountList(this.queryMember).then(res => {
        if (res.data.Code === 10000) {
          this.memberList = res.data.Result
        }
      })
      API.queryUnifiedNodeAccountList().then(res => {
        if (res.data.Code === 10000) {
          this.allMemberList = res.data.Result
        }
      })
    },
    handleCancel () {
      this.modalData.modal = false
    },
    // 保存流程信息
    handleSave () {
      let _strArr = []
      this.approveList.forEach(item => {
        if (!item.idStr) return
        _strArr.push({ unified_account_ids: item.idStr.join() })
      })
      if (this.modalData.type === 'add') { // 新增流程
        API.addPerfDeptFlowAndDetails({
          dept_id: this.modalData.deptId,
          form_num: this.form_num,
          detailStr: JSON.stringify(_strArr)
        }).then(res => {
          if (res.data.Code === 10000) {
            this.$Message.success(res.data.Message)
            this.modalData.modal = false
            this.$emit('approveBack')
          } else {
            this.$Message.warning(res.data.Message)
          }
        })
      }
      if (this.modalData.type === 'modify') { // 修改流程
        API.updatePerfDeptFlowAndDetails({
          dept_flow_id: this.modalData.data.dept_flow_id,
          dept_id: this.modalData.deptId,
          form_num: this.form_num,
          detailStr: JSON.stringify(_strArr)
        }).then(res => {
          if (res.data.Code === 10000) {
            this.$Message.success(res.data.Message)
            this.modalData.modal = false
            this.$emit('approveBack')
          } else {
            this.$Message.warning(res.data.Message)
          }
        })
      }
    },
    // 添加节点
    addNode (item) {
      this.approveList.push({})
    },
    // 删除节点
    delNode (item) {
      this.approveList.pop()
    },
    // 第个模块人员绑定取出
    checkMember (item) {
      let _memberStr = ''
      if (item && item.accountList && item.accountList.length > 0) {
        _memberStr = item.accountList.map(list => list.user_name).join()
      }
      return _memberStr
    },
    modalVisible (val) {
      if (val) {
        if (this.modalData.type === 'add') {
          this.queryMember.never_used = 1
        } else {
          this.queryMember.never_used = 0
        }
        this.queryMember.dept_id = this.modalData.deptId
        this.getMemberList()
        this.form_num = this.modalData.data.form_num ? this.modalData.data.form_num : 0
        this.approveList = this.modalData.data.nodeList ? [...[], ...this.modalData.data.nodeList] : [{}]
        this.approveList.map(item => {
          if (!item.accountList) return
          item.idStr = item.accountList.map(list => list.unified_account_id)
        })
      }
    }
  }
})
</script>
<style scoped>
  .template-area {
    position: absolute;
    width: 120px;
    right: 40px;
    top: 10px;
  }
  .member_area {
    width: 200px;
    margin: 10px;
  }
  .option_btn {
    margin-right: 5px;
  }
</style>

<template>
  <div>
    <div class="dept_list" v-for="item in departList" :key="item.dept_id">
      <div>
        <h3 class="dept_name">{{ item.dept_name }}</h3>
        <Button class="dept_add" type="primary" size="small" @click="addProcessModal(item)">+</Button>
        <Button v-if="item.flowList.length > 0" style="margin-left: 10px;" @click="updateAddReduceRemark(item)">加减分说明</Button>
      </div>
      <div v-for="list in item.flowList" :key="list.dept_flow_id">
        <span v-for="(option, opidx) in list.nodeList" :key="option.flow_node_id">
          <Input class="member_area" type="textarea" :value="checkMember(option)" :readonly="true"/>
          <Icon v-if="opidx < (list.nodeList.length - 1)" type="md-arrow-round-forward" size="30" color="#57a3f3"/>
          <span v-if="opidx === (list.nodeList.length - 1)">
            <Button class="option_btn" type="primary" size="small" @click="modifyNode(item, list)">编辑</Button>
            <Button class="option_btn" type="error" size="small" @click="delApprove(list)">删除</Button>
          </span>
        </span>
      </div>
    </div>
    <DeptProcess :modalData="processModal" @approveBack="getDepartList"></DeptProcess>
    <AddReduceRemark :modalData="remarkModal" @remarkBackData="getDepartList"></AddReduceRemark>
  </div>
</template>
<script>
import API from '@/api/jurisdictionManage/deptProcess'
import DeptProcess from './Modal/deptProcess.vue'
import AddReduceRemark from './Modal/addReduceRemark.vue'

export default ({
  components: {
    DeptProcess,
    AddReduceRemark
  },
  data () {
    return {
      departList: [], // 部门列表
      processModal: {
        modal: false,
        title: '',
        type: '',
        deptId: '',
        data: {}
      },
      remarkModal: {
        modal: false,
        title: '',
        dept_id: '',
        dept_remark: ''
      }
    }
  },
  methods: {
    // 获取部门列表数据
    getDepartList () {
      API.queryPerfAllDeptFlowList({
        pageSize: 100,
        pageIndex: 1
      }).then(res => {
        if (res.data.Code === 10000) {
          this.departList = res.data.Result
        }
      })
    },
    // 修改加减分说明
    updateAddReduceRemark (item) {
      this.remarkModal = {
        modal: true,
        title: item.dept_name,
        dept_id: item.dept_id,
        dept_remark: item.dept_remark
      }
    },
    // 新增流程
    addProcessModal (item) {
      this.processModal = {
        modal: true,
        deptId: item.dept_id,
        title: item.dept_name + '流程配置',
        type: 'add',
        data: {}
      }
    },
    // 编辑流程
    modifyNode (item, list) {
      this.processModal = {
        modal: true,
        title: item.dept_name + '流程配置',
        type: 'modify',
        deptId: item.dept_id,
        data: list
      }
    },
    // 删除流程
    delApprove (item) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确认删除该审批流？</p>',
        loading: true,
        onOk: () => {
          API.delPerfDeptFlowAndDetails({
            dept_flow_id: item.dept_flow_id
          }).then(res => {
            if (res.data.Code === 10000) {
              this.$Message.success(res.data.Message)
              this.getDepartList()
            } else {
              this.$Message.warning(res.data.Message)
            }
            this.$Modal.remove()
          })
        }
      })
    },
    // 第个模块人员绑定取出
    checkMember (item) {
      let _memberStr = ''
      if (item && item.accountList && item.accountList.length > 0) {
        _memberStr = item.accountList.map(list => list.user_name).join()
      }
      return _memberStr
    }
  },
  created () {
    this.getDepartList()
  }
})
</script>
<style scoped>
  .dept_list {
    margin-top: 20px;
    margin-left: 20px;
  }
  .dept_name {
    display: inline-block;
  }
  .dept_add {
    width: 18px;
    height: 18px;
    font-size: 16px;
    margin-left: 10px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }
  .member_area {
    width: 200px;
    margin: 10px;
  }
  .option_btn {
    margin-right: 5px;
  }
</style>

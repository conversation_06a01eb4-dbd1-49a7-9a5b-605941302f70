<template>
  <div>
    <div class="txt_line">
      <span>自评时间：每月</span>
      <input class="txt_input" type="text" v-model="self_evaluate_start" />
      <span>日至</span>
      <input class="txt_input" type="text" v-model="self_evaluate_end" />
      <span>日</span>
    </div>
    <div class="txt_line">
      <span>复评时间：每月</span>
      <input class="txt_input" type="text" v-model="re_evaluate_start" />
      <span>日至</span>
      <input class="txt_input" type="text" v-model="re_evaluate_end" />
      <span>日</span>
    </div>
    <div class="txt_line">
      <span>申诉时间：每月</span>
      <input class="txt_input" type="text" v-model="appeal_evaluate_start" />
      <span>日至</span>
      <input class="txt_input" type="text" v-model="appeal_evaluate_end" />
      <span>日</span>
    </div>
    <div class="btn_area">
      <Button type="primary" @click="saveTimeSet">保存</Button>
    </div>
  </div>
</template>
<script>
import API from '@/api/jurisdictionManage/positionSystem'

export default ({
  data () {
    return {
      self_evaluate_start: '', // 自评起始时间
      self_evaluate_end: '', // 自评截止时间
      re_evaluate_start: '', // 复评起始时间
      re_evaluate_end: '', // 复评截止时间
      appeal_evaluate_start: '', // 申诉起始时间
      appeal_evaluate_end: '' // 申诉截止时间
    }
  },
  methods: {
    // 获取时间设置信息
    getRemindTime () {
      API.queryRemindTime().then(res => {
        if (res.data.Code === 10000) {
          this.self_evaluate_start = res.data.Result.self_evaluate_start
          this.self_evaluate_end = res.data.Result.self_evaluate_end
          this.re_evaluate_start = res.data.Result.re_evaluate_start
          this.re_evaluate_end = res.data.Result.re_evaluate_end
          this.appeal_evaluate_start = res.data.Result.appeal_evaluate_start
          this.appeal_evaluate_end = res.data.Result.appeal_evaluate_end
        }
      })
    },
    // 保存时间设置信息
    saveTimeSet () {
      API.saveRemindTime({
        self_evaluate_start: this.self_evaluate_start,
        self_evaluate_end: this.self_evaluate_end,
        re_evaluate_start: this.re_evaluate_start,
        re_evaluate_end: this.re_evaluate_end,
        appeal_evaluate_start: this.appeal_evaluate_start,
        appeal_evaluate_end: this.appeal_evaluate_end
      }).then(res => {
        if (res.data.Code === 10000) {
          this.$Message.success(res.data.Message)
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    }
  },
  created () {
    // this.getRemindTime()
  }
})
</script>
<style scoped>
  .txt_line {
    margin-left: 50px;
    margin-top: 15px;
  }
  .txt_input {
    width: 25px;
    margin: 0 5px;
    text-align: center;
  }
  .btn_area {
    margin-left: 200px;
    margin-top: 30px;
  }
</style>

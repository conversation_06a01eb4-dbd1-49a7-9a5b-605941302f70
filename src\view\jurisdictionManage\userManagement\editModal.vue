<template>
  <Modal v-model="modalData.modal" :title="modalData.title" width="300" @on-visible-change="modalShowHide" :mask-closable="false" @on-cancel="clearData">
    <Form ref="formValide" :model="formValide" :label-width="110" :rules="ruleValidate">
      <FormItem label="姓名" prop="user_name">
        <Input type='text' v-model='formValide.user_name'></Input>
      </FormItem>
      <FormItem label="一体化平台账号" prop="unified_account">
        <Input type='text' v-model='formValide.unified_account'></Input>
      </FormItem>
      <FormItem label="瀛海账号">
        <Select v-model="formValide.erp_staff_id" prop="erp_staff_id" filterable label-in-value @on-change="obj => obj ? formValide.erp_staff_id = obj.value : ''">
          <Option v-for="item in erpList" :key="item.staff_id" :value="item.staff_id">{{ item.staff_name }}</Option>
        </Select>
      </FormItem>
      <FormItem label="海运管家账号">
        <Select v-model="formValide.us_passport_id" prop="us_passport_id" filterable label-in-value @on-change="obj => obj ? formValide.us_passport_id = obj.value : ''">
          <Option v-for="item in accountList" :key="item.us_passport_id" :value="item.us_passport_id">{{ item.full_name }}</Option>
        </Select>
      </FormItem>
      <FormItem label="云之家id" prop="open_id">
        <Input type='text' v-model='formValide.open_id'></Input>
      </FormItem>
      <FormItem label="状态">
        <Select v-model="formValide.status_value" prop="status_value" filterable label-in-value @on-change="obj => obj ? formValide.status_value = obj.value : ''">
          <Option v-for="item in stateList" :key="item.value" :value="item.value">{{ item.name }}</Option>
        </Select>
      </FormItem>
    </Form>
    <div slot="footer">
      <Button style="margin-right: 8px" @click="clearData">取消</Button>
      <Button type="primary" @click="addHandle">保存</Button>
    </div>
  </Modal>
</template>
<script>
import { validateMobilePhone } from '@/assets/js/iViewValidate'
import { queryAllStaffList, queryAccountList, addUnifiedAccount, updateUnifiedAccount } from '@/api/jurisdictionManage/userManagement'

export default ({
  props: {
    modalData: Object
  },
  data () {
    return {
      erpList: [], // 瀛海数据列表
      accountList: [], // 海运管家数据列表
      formValide: {
        user_name: '',
        unified_account: '',
        open_id: '',
        erp_staff_id: '',
        us_passport_id: '',
        status_value: ''
      },
      ruleValidate: {
        user_name: [{ required: true, message: '姓名不能为空！', trigger: 'blur' }],
        unified_account: [
          { required: true, message: '账号不能为空！', trigger: 'blur' },
          { validator: validateMobilePhone, trigger: 'change' }
        ]
      },
      stateList: [
        {
          name: '离职',
          value: '0'
        },
        {
          name: '在职',
          value: '1'
        }
      ]
    }
  },
  methods: {
    // 保存数据
    addHandle () {
      if (this.modalData.type === 'add') {
        this.$refs['formValide'].validate((valid) => {
          if (valid) {
            addUnifiedAccount(this.formValide).then(res => {
              if (res.data.Code === 10000) {
                this.$emit('editBack')
                this.$Message.success(res.data.Message)
                this.clearData()
              } else {
                this.$Message.warning(res.data.Message)
              }
            })
          }
        })
      }
      if (this.modalData.type === 'modify') {
        this.$refs['formValide'].validate((valid) => {
          if (valid) {
            updateUnifiedAccount({
              unified_account_id: this.formValide.unified_account_id,
              user_name: this.formValide.user_name,
              unified_account: this.formValide.unified_account,
              open_id: this.formValide.open_id,
              erp_staff_id: this.formValide.erp_staff_id,
              us_passport_id: this.formValide.us_passport_id,
              status_value: this.formValide.status_value
            }).then(res => {
              if (res.data.Code === 10000) {
                this.$emit('editBack')
                this.$Message.success(res.data.Message)
                this.clearData()
              } else {
                this.$Message.warning(res.data.Message)
              }
            })
          }
        })
      }
    },
    // 获取下拉基础信息
    getBaseList () {
      // 获取瀛海数据列表
      queryAllStaffList().then(res => {
        if (res.data.Code === 10000) {
          this.erpList = res.data.Result
        }
      })
      // 获取海运管家数据列表
      queryAccountList().then(res => {
        if (res.data.Code === 10000) {
          this.accountList = res.data.Result
        }
      })
    },
    clearData () {
      this.formValide.user_name = ''
      this.formValide.unified_account = ''
      this.formValide.open_id = ''
      this.formValide.erp_staff_id = ''
      this.formValide.us_passport_id = ''
      this.formValide.status_value = ''
      this.$refs['formValide'].resetFields()
      this.modalData.modal = false
    },
    modalShowHide (val) {
      if (val) {
        this.formValide = { ...this.formValide, ...this.modalData.data }
        this.getBaseList()
      }
    }
  }
})
</script>

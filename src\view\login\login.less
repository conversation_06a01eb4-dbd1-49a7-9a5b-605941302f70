.vertical-center-modal{
    display: flex;
    align-items: center;
    justify-content: center;

    .ivu-modal{
        top: 0;
    }
}
.login{
    width: 100%;
    height: 100%;
    background-image: url(../../assets/images/login-bg.png);
    background-size: cover;
    background-position: bottom;
    position: relative;
    font-family: PingFangSC-Medium, PingFang SC;
    .ivu-modal-footer {
      display: none;
    }
    &-tip {
      width: 100%;
      height: 90%;
      padding: 20px;
      background-image: url(/img/login-tip.d3841499.png);
      background-size: contain;
      background-repeat: no-repeat;
      margin-left: 34px;
      margin-top: 20px;
    }
    &-head {
      display: flex;
      padding: 18px 59px;
      background: #fff;
      font-weight: bold;
      color: #161717;
      align-items: center;
      letter-spacing: 2px;
      font-family: PingFangSC-Medium, PingFang SC;
      .head-logo {
        margin-right: 10px;
      }
      .head-text {
        font-size: 24px;
        .heead-text-cnname {
          display: block;
        }
        .heead-text-enname {
          display: block;
          color: #7F8081;
          font-weight: 500;
          font-size: 14px;
        }
      }
      
      .head-point {
        color: #7F8081;
        margin: 0 17px;
        font-size: 24px;
      }
    }
    &-title {
        text-align: center;
        font-size: 24px;
        font-weight: 600;
        color: #252529;
        .sub-title {
            color: #A8A9AB;
            font-weight: 100;
            margin-top: 30px;
        }
    }
    &-btn {
        position: fixed;
        right: 30px;
        top: 40px;
    }
    &-search{
        width: 100%;
        max-width: 1200px;
        display: block;
        font-size: 40px;
        font-weight: 500;
        color: #fff;
        top: 50%;
        position: absolute;
        margin-top: -115px;
        left: 50%;
        margin-left: -600px;
        .search-title {
            letter-spacing: 4px;
            margin-bottom: 30px;
            // text-shadow: 5px 8px 12px rgba(10,10,10,0.5);
        }
        .search-subtitle {
            letter-spacing: 4px;
            margin-left: 250px;
            // text-shadow: 2px 4px 6px rgba(10,10,10,0.6);
        }
        .search-con {
            width: 630px;
            margin: 60px auto;
            input {
                font-size: 18px;
                height: 60px;
                line-height: 60px;
            }
            .ivu-input {
                border-radius: 30px;
            }
            .search-type {
                width: 120px;
                font-size: 20px;
                padding: 10px;
                color: #57a3f3;
            }
            .ivu-input-group {
                font-size: 16px;
            }
            .ivu-input-default {
                padding-left: 30px;
            }
            .ivu-input-search {
                position: absolute;
                height: 50px;
                width: 96px;
                line-height: 50px;
                right: 7px;
                margin-top: 5px;
                border-radius: 25px !important;
                border: none !important;
                &::before {
                    content: '';
                    display: none;
                }
            }
            .ivu-select-selected-value {
                font-size: 16px;
            }
        }
    }
    &-area {
        position: absolute;
        right: 500px;
    }
    &-con{
        width: 350px;
        position: fixed;
        top: 50%;
        margin-top: -189px;
        background: #fff;
        border-radius: 7px;
        padding: 25px 32px;
        &-header{
            font-size: 16px;
            font-weight: 300;
            text-align: center;
            padding: 30px 0;
        }
        .form-con{
            margin-top: 36px;
            padding: 10px 0 0;
        }
        .login-tip{
            font-size: 10px;
            text-align: center;
            color: #c3c3c3;
        }
    }
    &-footer {
      position: fixed;
      width: 100%;
      height: 138px;
      background: #fff;
      bottom: 0;
      .footer-text {
        font-family: PingFangSC-Medium, PingFang SC;
        font-size: 12px;
        font-weight: bold;
        color: #323334;
        text-align: center;
        margin-top: 33px;
        div {
          margin-bottom: 10px;
        }
      }
    }
}

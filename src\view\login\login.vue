<style lang="less">
  @import './login.less';
</style>

<template>
  <div class="login">
    <div class="login-head">
      <img class="head-logo" :src="logo" alt="">
      <span class="head-text">
        <span class="heead-text-cnname">兴通海运股份有限公司</span>
        <span class="heead-text-enname">XINGTONG SHIPPING CO., LTD.</span>
      </span>
      <span class="head-point">|</span>
      <span class="head-text">一体化管理平台</span>
    </div>
    <div class="login-search" v-if="this.$route.name === 'login'">
      <div class="search-title">国际一流</div>
      <div class="search-title">国内领先的</div>
      <div class="search-title">化工供应链综合服务商</div>
    </div>
    <div class="login-area">
      <div class="login-con">
        <div class="login-title">
          <div>登录</div>
        </div>
        <!-- <Card icon="log-in" title="欢迎登录" :bordered="false"> -->
        <div class="form-con">
          <login-form @on-success-valid="handleSubmit" @on-forget-pwd="handleForgetPwd"></login-form>
        </div>
        <p slot="footer"></p>
        <!-- </Card> -->
      </div>
    </div>
    <div class="login-footer">
      <div class="footer-text">
        <div>地址：福建省泉州市泉港区驿峰东路295号兴通海运大厦8-9楼</div>
        <div>Copyright © 2019 兴通海运股份有限公司 保留所有版权 闽ICP备********号-3</div>
      </div>
    </div>
    <Modal v-model="forgetModal" title="重置密码" :mask-closable="false" class-name="vertical-center-modal" @on-cancel="handleCancel">
      <Form ref="formValidate" label-position="right" :model="form" :label-width="100" :rules="ruleValidate">
        <FormItem label="账号：" prop="unified_account">
          <Input v-model="form.unified_account" placeholder="请输入账号信息"></Input>
        </FormItem>
        <FormItem label="新密码：" prop="password">
          <Input type="password" v-model="form.password" placeholder="请输入新密码"></Input>
        </FormItem>
        <FormItem label="确认密码：" prop="rePwd">
          <Input type="password" v-model="form.rePwd" placeholder="请确认新密码"></Input>
        </FormItem>
        <FormItem label="验证码：" prop="auth_code">
          <Input v-model="form.auth_code" placeholder="请输入验证码" style="width: 295px;"></Input>
          <span v-if="!isTimeShow" style="margin-left: 20px; cursor: pointer; color: #007DFF; float: right;" @click="sendMsgCode">发送验证码</span>
          <span v-else style="margin-left: 20px; color: #007DFF;">倒计 {{ (60 - timeIdx)}} 秒</span>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button type="primary" @click="handleCancel">取消</Button>
        <Button type="primary" @click="handleSave">保存</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import LoginForm from '_c/login-form'
import { mapActions } from 'vuex'
import logo from '@/assets/images/logo-boat.png'
import loginTip from '@/assets/images/login-tip.png'
import { findPwdSendCode, checkAuthCodeUpdatePwd } from '@/api/user.js'
import { queryDeptAccountMenu } from '@/api/jurisdictionManage/userManagement'

export default {
  components: {
    LoginForm
  },
  data () {
    return {
      forgetModal: false,
      logo,
      loginTip,
      form: {
        unified_account: '',
        password: '',
        rePwd: '',
        auth_code: ''
      },
      isTimeShow: false,
      timeIdx: 0,
      formTimeLen: 60,
      formTime: null,
      ruleValidate: {
        unified_account: [
          { required: true, message: '账号不能为空', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '密码不能为空', trigger: 'blur' }
        ],
        rePwd: [
          { required: true, message: '确认密码不能为空', trigger: 'blur' }
        ],
        auth_code: [
          { required: true, message: '验证码不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    ...mapActions([
      'handleLogin',
      'handlePhoneLogin',
      'handleLogOut'
    ]),
    getUrlKey (name) {
      // eslint-disable-next-line no-sparse-arrays
      return decodeURIComponent((new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(location.href) || [, ''])[1].replace(/\+/g, '%20')) || null
    },
    handleForgetPwd (modal) { // 忘记密码
      this.forgetModal = true
    },
    // 获取所有菜单数据
    getAllMenuList () {
      let list = JSON.parse(localStorage.getItem('mainMenu'))
      if (!list && list.length === 0) return
      let allMenuList = []
      list.forEach((item, idx) => {
        let _param = {
          deptId: JSON.parse(localStorage.getItem('userData')).dept_id,
          unifiedAccountId: JSON.parse(localStorage.getItem('userData')).unified_account_id,
          idPathLike: item.id_path,
          enabled: 1
        }
        queryDeptAccountMenu(_param).then(res => {
          if (res.data.Code === 10000) {
            localStorage.setItem('menuList', JSON.stringify(res.data.Result[0].menuTree[0].children))
            allMenuList.push(this.removeMenuDisplay(res.data.Result[0].menuTree)[0])
            if (idx === list.length - 1) {
              setTimeout(() => {
                localStorage.setItem('allMenuList', JSON.stringify(allMenuList))
              }, 500)
            }
          } else {
            this.$Message.error('暂无菜单权限，请联系管理员！')
          }
        })
      })
    },
    // 移除隐藏的数据
    removeMenuDisplay (data) {
      const newMenuData = []
      for (const item of data) {
        // if (parseInt(item.menuDisplay) === 1) {
          const newItem = { ...item }
          if (item.children && item.children.length > 0) {
            newItem.children = this.removeMenuDisplay(item.children)
          }
          newMenuData.push(newItem)
        // }
      }
      return newMenuData
    },
    // 发送短信验证码
    sendMsgCode () {
      if (this.form.unified_account === '') {
        this.$Message.warning('请填写账号信息再发送验证码')
        return
      }
      findPwdSendCode({ unified_account: this.form.unified_account }).then(res => {
        if (res.data.Code === 10000) {
          this.isTimeShow = true
          this.timeIdx = 0
          this.$Message.success(res.data.Message)
          this.formTime = setInterval(() => {
            this.timeIdx += 1
            if (this.timeIdx >= this.formTimeLen) {
              this.isTimeShow = false
              clearInterval(this.formTime)
            }
          }, 1000)
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    handleSave () { // 忘记密码弹窗  保存
      this.$refs['formValidate'].validate((valid) => {
        if (valid) {
          if (this.form.password !== this.form.rePwd) {
            this.$Message.warning('两个密码不一致，请核对！')
            return
          }
          checkAuthCodeUpdatePwd(this.form).then(res => {
            this.isTimeShow = false
            clearInterval(this.formTime)
            if (res.data.Code === 10000) {
              this.$Message.success(res.data.Message)
              this.forgetModal = false
              this.formReset()
            } else {
              this.$Message.error(res.data.Message)
            }
          })
        }
      })
    },
    handleCancel () { // 忘记密码弹窗  取消
      this.forgetModal = false
      this.formReset()
    },
    formReset () { // 忘记密码校验内容重置
      this.form = {
        unified_account: '',
        password: '',
        rePwd: '',
        auth_code: ''
      }
      this.$refs['formValidate'].resetFields()
    },
    handleSubmit ({ userName, password, way }) {
      this.handleLogin({ userName, password, way }).then(res => {
        if (res.Code === 10000) {
          if (res.UserData.status_value === '0') { // 离职的不走
            this.handleLogOut()
            this.$Message.error('您暂无权限，请联系管理员')
          } else {
            this.getAllMenuList()
            this.$router.push({
              name: this.$config.homeName
            })
          }
        } else {
          this.$Message.error(res.Message)
        }
      })
    }
  },
  created () {
    let _user = this.getUrlKey('user')
    let router = this.getUrlKey('router')
    if (_user && (document.referrer.includes('ierptest.xtshipping.com') || document.referrer.includes('erp.xtshipping.com') || router === 'tabIframe')) {
      this.handlePhoneLogin({ 'userName': _user }).then(res => {
        if (res.Code === 10000) {
          if (res.UserData.status_value === '0') { // 离职的不走
            this.handleLogOut()
            this.$Message.error('您暂无权限，请联系管理员')
          } else {
            this.getAllMenuList()
            if (router) {
              if (router.indexOf('/') > 0) {
                this.$router.push({
                  name: router.split('/')[0],
                  params: {
                    id: router.split('/')[1]
                  }
                })
              } else {
                if (router === 'erp') { // 跳转至瀛海登录
                  let _uuid = localStorage.getItem('uuid')
                  window.open('http://erp.xtshipping.net/#/login?' + _uuid + '&resourceflag=1', '_self')
                } else {
                  this.$router.push({
                    name: router
                  })
                }
              }
            } else {
              this.$router.push({
                name: this.$config.homeName
              })
            }
          }
        } else {
          this.$Message.error(res.Message)
        }
      })
    }
  }
}
</script>

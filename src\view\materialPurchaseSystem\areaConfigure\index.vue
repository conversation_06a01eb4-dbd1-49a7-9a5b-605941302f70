<template>
  <div>
    <Card>
      <Button icon="md-add" @click="createClassify('create')" class="material_add_btn">区域新增</Button>
      <div v-show="listData.length === 0" class="null-data">暂无数据</div>
      <Row :gutter="40" class="classify_list">
        <Col span="6" v-for="(item, idx) in listData" :key="idx">
          <div class="list">
            <b>{{ item.area_name }}</b>
            <p>
              <Button type="text" @click="createClassify('update', item)" class="update_btn">编辑</Button>
            </p>
          </div>
        </Col>
      </Row>
    </Card>
    <!-- 区域弹窗内容 -->
    <Modal v-model="modal" :title="modalTitle" :mask-closable="false" width="325">
      <div class="modaldiv">
        <label>名称：</label>
        <Input v-model="listQuery.area_name" style="width: 255px" />
      </div>
      <div slot="footer">
        <Button type="primary" @click="cancel">取消</Button>
        <Button v-if="curType=='create'" type="primary" @click="createData">保存</Button>
        <Button v-else type="primary" @click="updateData">保存</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import { queryAreaList, addArea, updateArea } from '@/api/materialPurchaseSystem/areaConfigure'
export default {
  data () {
    return {
      curType: '',
      listData: [],
      modal: false,
      modalTitle: '',
      listQuery: {
        area_name: '',
        unified_area_id: ''
      }
    }
  },
  created () {
    this.getList()
  },
  methods: {
    // 获取区域列表
    getList () {
      queryAreaList().then(res => {
        if (res.data.Code === 10000) {
          this.listData = res.data.Result
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 开启区域弹窗
    createClassify (type, row) {
      this.curType = type
      this.modal = true
      if (type === 'create') {
        this.modalTitle = '区域新增'
      } else {
        this.modalTitle = '区域编辑'
        this.listQuery = {
          area_name: row.area_name,
          unified_area_id: row.unified_area_id
        }
      }
    },
    // 保存新增
    createData () {
      if (this.listQuery.area_name === '') return this.$Message.error('区域名称不能为空！')
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确认新增区域？</p>',
        loading: true,
        onOk: () => {
          addArea({ area_name: this.listQuery.area_name }).then(res => {
            if (res.data.Code === 10000) {
              this.loading = false
              this.$Modal.remove()
              this.$Message.success(res.data.Message)
              this.getList()
              this.cancel()
            } else {
              this.loading = false
              this.$Modal.remove()
              this.$Message.error(res.data.Message)
            }
          })
        }
      })
    },
    // 修改保存
    updateData () {
      if (this.listQuery.area_name === '') return this.$Message.error('区域名称不能为空！')
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确认修改区域名称？</p>',
        loading: true,
        onOk: () => {
          updateArea(this.listQuery).then(res => {
            if (res.data.Code === 10000) {
              this.loading = false
              this.$Modal.remove()
              this.$Message.success(res.data.Message)
              this.getList()
              this.cancel()
            } else {
              this.loading = false
              this.$Modal.remove()
              this.$Message.error(res.data.Message)
            }
          })
        }
      })
    },
    // 关闭弹窗
    cancel () {
      this.listQuery = {
        area_name: '',
        unified_area_id: ''
      }
      this.modal = false
    }
  }
}
</script>
<style lang="less" scoped>
.null-data {
  padding: 15px 0;
  text-align: center;
}
.classify_list {
  .list {
    padding: 10px 15px;
    border-radius: 5px;
    position: relative;
    margin-bottom: 10px;
    background-color: #EEF4FF;
    b {
      color: #000;
      font-size: 18px;
      display: block;
    }
    .update_btn {
      color: #1943A9;
      padding: 0;
      font-size: 14px;
    }
    &::after {
      content: '';
      width: 28px;
      height: 28px;
      display: block;
      top: 33%;
      right: 20px;
      position: absolute;
      background: url(../../../assets/images/area_icon.png) no-repeat center;
    }
  }
}
.material_add_btn {
  color: #fff;
  border: none;
  font-size: 16px;
  letter-spacing: 2px;
  margin-bottom: 25px;
  padding: 8px 10px;
  background-color: #1943A9;
}
</style>

<template>
  <div>
    <Card>
      <div class="footer_btn">
        <Button @click="cancel" v-if="configureShow">取消</Button>
        <Button @click="configureShow = true" v-if="!configureShow">修改</Button>
        <Button @click="saveData" v-if="configureShow">保存</Button>
      </div>
      <Button class="configure_btn">数据来源 <Icon type="md-brush"></Icon></Button>
      <div v-show="listData.length === 0" class="null-data">暂无数据</div>
      <Row :gutter="60">
        <Col span="6" v-for="(item, idx) in orgList" :key="idx" class="set_list" :class="{'selected': item.is_open === '1'}">
          <div class="list">
            <span class="usefor">{{ item.departmentname }}</span>
            <Checkbox :value="item.is_open === '1'" class="update_btn" @on-change="changeOrgSelected(item.is_open, idx)" :disabled="!configureShow"></Checkbox>
          </div>
        </Col>
      </Row>
    </Card>
    <Card style="margin-top: 15px;">
      <Button class="configure_btn">用途配置 <Icon type="md-brush"></Icon></Button>
      <div v-show="listData.length === 0" class="null-data">暂无数据</div>
      <Row :gutter="60">
        <Col span="6" v-for="(item, idx) in listData" :key="idx" class="set_list" :class="{'selected': item.is_open === '1'}">
          <div class="list">
            <span class="usefor">{{ item.usefor }}</span>
            <Checkbox :value="item.is_open === '1'" class="update_btn" @on-change="changeSelected(item.is_open, idx)" :disabled="!configureShow"></Checkbox>
          </div>
        </Col>
      </Row>
    </Card>
  </div>
</template>
<script>
import { queryUseforConfigAll, configUseforAllSave, queryOrgConfigList, saveOrgConfigList, queryOrgConfigName } from '@/api/materialPurchaseSystem/dataConfigure'
export default {
  data () {
    return {
      loading: false,
      orgList: [], // 来源组织列表
      listData: [], // 用途配置列表
      configureShow: false
    }
  },
  created () {
    this.getList()
  },
  methods: {
    // 获取用途列表
    getList () {
      this.loading = true
      // 获取数据来源配置
      queryOrgConfigList().then(res => {
        if (res.data.Code === 10000) {
          this.orgList = res.data.Result
        }
      })
      // 获取用途配置数据
      queryUseforConfigAll().then(res => {
        if (res.data.Code === 10000) {
          this.loading = false
          this.configureShow = false
          this.listData = res.data.Result
        } else {
          this.loading = false
          this.$Message.error(res.data.Message)
        }
      })
    },
    changeOrgSelected (val, idx) {
      this.orgList[idx].is_open = val === '1' ? '0' : '1'
    },
    changeSelected (val, idx) {
      this.listData[idx].is_open = val === '1' ? '0' : '1'
    },
    resetOrgName () {
      queryOrgConfigName().then(res => {
        if (res.data.Code === 10000) {
          let _orgNameList = res.data.Result.map(item => { return item.departmentname })
          let _orgName = _orgNameList.join()
          localStorage.setItem('orgName', _orgName)
          this.$store.commit('setOrgName', _orgName)
        }
      })
    },
    // 保存配置
    saveData () {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确认保存配置？</p>',
        loading: true,
        onOk: () => {
          let data = JSON.stringify(this.listData)
          let orgData = JSON.stringify(this.orgList)
          // 保存数据来源
          saveOrgConfigList({ configArray: orgData }).then(res => {
            if (res.data.Code !== 10000) {
              this.$Message.error(res.data.Message)
            }
          })
          // 保存用途配置
          configUseforAllSave({ configArray: data }).then(res => {
            this.loading = false
            this.$Modal.remove()
            if (res.data.Code === 10000) {
              this.$Message.success(res.data.Message)
              this.resetOrgName()
              this.cancel()
            } else {
              this.$Message.error(res.data.Message)
            }
          })
        }
      })
    },
    // 取消数据配置
    cancel () {
      this.listData = []
      this.configureShow = false
      this.getList()
    }
  }
}
</script>
<style lang="less" scoped>
.footer_btn Button,
.configure_btn {
  color: #fff;
  border: none;
  font-size: 16px;
  margin-bottom: 25px;
  padding: 6px 25px;
  border-radius: 20px;
  background-color: #1943A9;
}
.footer_btn {
  position: absolute;
  right: 10px;
  text-align: right;
  margin-top: 10px;
  Button:first-child {
    margin-right: 15px;
  }
}
.set_list {
  .list {
    text-align: center;
    height: 70px;
    line-height: 70px;
    border-radius: 5px;
    margin-top: 20px;
    margin-bottom: 20px;
    position: relative;
    border: 1px solid #1F2633;
    .usefor {
      margin: auto;
      max-width: 150px;
      overflow: hidden;
      display: inline-block;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .update_btn {
      margin: 0;
      position: absolute;
      right: 23px;
    }
  }
}
.set_list.selected {
  .list {
    border: 1px solid #1943A9;
    background-color: #EEF4FF;
  }
}
</style>
<style lang="less">
.update_btn {
  .ivu-checkbox-inner {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    &::after {
      width: 6px;
      height: 9px;
      top: 4px;
      left: 6px;
    }
  }
}
.set_list.selected, .set_list.selected:hover {
  .ivu-checkbox-inner {
    border-color: #1943A9;
    background-color: #EEF4FF;
    &::after {
      border-color: #1943A9;
    }
  }
}
</style>

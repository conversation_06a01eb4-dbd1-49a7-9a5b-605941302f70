<template>
  <div>
    <Tabs type="card" v-model="tabId" class="tab_area">
      <TabPane label="月份分析">
        <MonthMaterialNum v-if="tabId === 0"></MonthMaterialNum>
      </TabPane>
      <TabPane label="日期分析">
        <DateMaterialNum v-if="tabId === 1"></DateMaterialNum>
      </TabPane>
    </Tabs>
  </div>
</template>
<script>
import MonthMaterialNum from './components/monthMaterialNum.vue'
import DateMaterialNum from './components/dateMaterialNum.vue'

export default {
  components: {
    MonthMaterialNum,
    DateMaterialNum
  },
  data () {
    return {
      tabId: 0
    }
  }
}
</script>
<style>
  .tab_area .ivu-tabs-bar {
    margin-bottom: 0 !important;
  }
</style>

<template>
  <div>
    <Card>
      <search @searchResults='searchResults' @selectOnChanged='selectOnChanged' :setSearch='setSearchData' @resetResults='resetResults'></search>
    </Card>
    <Card style="margin-top: 10px;">
      <chart-line style="height: 300px;" :unit="lineUnit" :value="priceLineData" :formatter="true" @clickBack="linkBack" :clickable="true" :color="lineColor" rotate="45" :text="curInventoryname + `价格走势`"/>
      <h3 class="text_con">
        {{ curInventoryname }}采购数据
        <Button type="text" @click="exportData">数据导出 <Icon type="ios-download-outline" size="18"></Icon></Button>
      </h3>
      <Table border :loading="loading" ref="selection" :columns="columns" :data="list" style="clear: both;"></Table>
      <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
        :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
    </Card>
    <!-- 备注弹窗内容 -->
    <Modal v-model="remarkModal" title="备注" width="320">
      <div class="modal_content">
        <div>
          <label>日期：</label>{{ modalParam.linkXAxisData }}
        </div>
        <div>
          <label>备注：</label>
          <Input type="textarea" v-model="modalParam.remark"></Input>
        </div>
      </div>
      <div slot="footer">
        <Button type="primary" @click="remarkModal = false">取消</Button>
        <Button type="primary" @click="updateData">保存</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import { ChartLine } from '_c/charts'
import search from '_c/search' // 查询组件
import API from '@/api/basicData'
import { queryMaterialList, queryMaterialPage, exportMaterial } from '@/api/materialPurchaseSystem/materialSinglePrice'
export default {
  components: {
    ChartLine,
    search
  },
  data () {
    return {
      lineUnit: '',
      curInventoryname: '',
      queryParam: {
        date_month_st: '',
        date_month_et: '',
        inventoryname: '', // 名称
        usefor: '',
        inventorystd: '',
        shipname: '',
        vendorname: ''
      },
      remarkModal: false,
      modalParam: {
        linkXAxisData: '',
        remark: '',
        code: '', // 入库单号
        inventorycode: '' // 存货编码
      },
      priceLineData: {
        xAxis: [],
        legend: [],
        smooth: 0,
        data: []
      },
      lineList: [],
      lineColor: ['#6699FF', '#E74823'],
      loading: false,
      total: 0,
      list: [],
      listCurrent: 1,
      listQuery: {
        pageSize: 10,
        pageIndex: 1
      },
      columns: [
        {
          title: '船舶',
          key: 'shipname',
          align: 'center',
          fixed: 'left',
          width: 90
        },
        {
          title: '入库日期',
          key: 'date',
          align: 'center',
          fixed: 'left',
          width: 100
        },
        {
          title: '入库单号',
          key: 'code',
          align: 'center',
          fixed: 'left',
          width: 130
        },
        {
          title: '存货编码',
          key: 'inventorycode',
          align: 'center',
          width: 85
        },
        {
          title: '规格型号',
          key: 'inventorystd',
          align: 'center',
          minWidth: 80
        },
        {
          title: '设备型号',
          key: 'unittype',
          align: 'center',
          minWidth: 80
        },
        {
          title: '单位',
          key: 'cmassunitname',
          align: 'center',
          width: 65
        },
        {
          title: '数量',
          key: 'quantity',
          align: 'center',
          minWidth: 80
        },
        {
          title: '本币无税金额(元)',
          key: 'price',
          align: 'center',
          minWidth: 80
        },
        {
          title: '本币无税总计(元)',
          key: 'iSum',
          align: 'center',
          minWidth: 80
        },
        {
          title: '设备生产厂家',
          key: 'producername',
          align: 'center',
          minWidth: 80
        },
        {
          title: '设备出厂编号',
          key: 'factorynumber',
          align: 'center',
          minWidth: 100
        },
        {
          title: '设备类别',
          key: 'factorytype',
          align: 'center',
          minWidth: 100
        },
        {
          title: '用途',
          key: 'usefor',
          align: 'center',
          minWidth: 100
        },
        {
          title: '供应商',
          key: 'vendorname',
          align: 'center',
          width: 150
        },
        {
          title: '部门',
          key: 'departmentname',
          align: 'center',
          width: 85
        },
        {
          title: '制单人',
          key: 'maker',
          align: 'center',
          width: 78
        }
      ],
      setSearchData: {
        date_month_st: {
          type: 'month',
          label: '时间',
          selected: '',
          width: 100,
          value: '',
          isdisable: false
        },
        date_month_et: {
          type: 'month_end',
          label: '-',
          selected: '',
          width: 100,
          value: '',
          isdisabled: false
        },
        usefor: {
          type: 'select',
          label: '用途',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 140,
          value: '',
          filterable: true,
          change: this.changeUsefor
        },
        inventoryname: {
          type: 'select',
          label: '名称',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 150,
          value: '',
          filterable: true,
          isdisabled: false,
          change: this.changeInventoryname
        },
        inventorystd: {
          type: 'select',
          label: '型号',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 150,
          value: '',
          filterable: true,
          isdisabled: false
        },
        shipname: {
          type: 'select',
          label: '船舶',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 130,
          value: '',
          filterable: true
        },
        vendorname: {
          type: 'select',
          label: '供应商',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 180,
          value: '',
          filterable: true
        }
      }
    }
  },
  beforeDestroy () {
    this.$store.commit('setSinglePriceParam', this.queryParam)
  },
  created () {
    this.getSysDate()
    this.getData()
  },
  methods: {
    // 获取价格走势
    getLineData () {
      this.queryParam.date_month_st = this.date_month_st
      this.queryParam.date_month_et = this.date_month_et
      this.priceLineData.xAxis = []
      this.priceLineData.data = []
      Object.assign(this.queryParam, {
        order_by: 1 // 1按照时间排序；其它按金额排序
      })
      queryMaterialList(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          this.lineList = res.data.Result
          res.data.Result.forEach(e => {
            this.priceLineData.xAxis.push(e.date + ',' + e.remark)
            this.priceLineData.data.push(e.price)
          })
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 获取价格列表
    getList () {
      this.queryParam.date_month_st = this.date_month_st
      this.queryParam.date_month_et = this.date_month_et
      this.loading = true
      Object.assign(this.listQuery, this.queryParam)
      queryMaterialPage(this.listQuery).then(res => {
        if (res.data.Code === 10000) {
          this.loading = false
          this.list = res.data.Result
          this.total = res.data.Total
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 统计图点击回调
    linkBack (val) {
      if (val.name === '平均值') return
      this.remarkModal = true
      this.modalParam.remark = this.lineList[val.dataIndex].remark
      this.modalParam.linkXAxisData = val.name
      this.modalParam.code = this.lineList[val.dataIndex].code
      this.modalParam.inventorycode = this.lineList[val.dataIndex].inventorycode
    },
    // 备注新增/修改保存
    updateData () {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确认保存备注内容吗？</p>',
        loading: true,
        onOk: () => {
          API.addOrUpdateAffiliate(this.modalParam).then(res => {
            if (res.data.Code === 10000) {
              this.loading = false
              this.$Modal.remove()
              this.remarkModal = false
              this.$Message.success(res.data.Message)
              this.getLineData()
              this.getList()
            } else {
              this.loading = false
              this.$Modal.remove()
              this.$Message.error(res.data.Message)
            }
          })
        }
      })
    },
    // 查询
    searchResults (e) {
      this.listCurrent = 1
      this.listQuery.pageIndex = 1
      this.queryParam.inventoryname = e.inventoryname
      this.queryParam.usefor = e.usefor
      this.queryParam.inventorystd = e.inventorystd
      this.queryParam.shipname = e.shipname
      this.queryParam.vendorname = e.vendorname
      this.curInventoryname = this.setSearchData.inventoryname.selected
      this.lineUnit = this.lineUnit
      if (this.setSearchData.usefor.selected === undefined || this.setSearchData.usefor.selected === '') {
        this.$Message.error('用途不能为空！')
      } else if (this.setSearchData.inventoryname.selected === undefined || this.setSearchData.inventoryname.selected === '') {
        this.$Message.error('名称不能为空！')
      } else {
        this.getLineData()
        this.getList()
      }
    },
    selectOnChanged (e) {
      if (e.flag === 'month_start') {
        this.date_month_st = e.key
      } else if (e.flag === 'month_end') {
        this.date_month_et = e.key
      }
    },
    // 重置
    async resetResults () {
      this.queryParam.inventorystd = ''
      this.queryParam.shipname = ''
      this.queryParam.vendorname = ''
      this.listCurrent = 1
      this.listQuery = {
        pageSize: 10,
        pageIndex: 1
      }
      this.setSearchData.inventorystd.selected = ''
      this.setSearchData.shipname.selected = ''
      this.setSearchData.vendorname.selected = ''
      this.$store.state.setState.singlePriceParam = {}
      await this.getSysDate()
      await this.getCurData()
      this.setSearchData.inventorystd.isdisabled = false
      await this.getLineData()
      await this.getList()
    },
    // 获取默认数据
    async getCurData () {
      let _curIdx = this.setSearchData.usefor.selectData.findIndex(item => { return item.value === '油料' })
      this.queryParam.usefor = this.setSearchData.usefor.selectData[_curIdx].value
      this.setSearchData.usefor.selected = this.setSearchData.usefor.selectData[_curIdx].value
      await this.changeUsefor(this.setSearchData.usefor)
      this.setSearchData.inventoryname.selected = this.setSearchData.inventoryname.selectData[1].value
      this.queryParam.inventoryname = this.setSearchData.inventoryname.selectData[1].value
      this.curInventoryname = this.setSearchData.inventoryname.selectData[1].value
    },
    async getData () {
      await this.getBaseData()
      await this.getCurData()
      this.lineUnit = this.setSearchData.inventoryname.selectData[1].cmassunitname + '/元'
      this.getLineData()
      this.getList()
    },
    // 数据导出
    exportData () {
      exportMaterial(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          window.open(res.data.fileUrl, '_blank')
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 根据用途获取关联名称
    async changeUsefor (e) {
      this.lineUnit = '元/吨'
      this.setSearchData.inventoryname.selectData = []
      this.setSearchData.inventoryname.selected = ''
      this.setSearchData.inventorystd.selectData = []
      this.setSearchData.inventorystd.selected = ''
      this.setSearchData.inventoryname.isdisabled = e.selected === undefined
      await API.materialInventoryname({ usefor: e.selected }).then(res => {
        if (res.data.Code === 10000) {
          res.data.Result.map(item => {
            this.setSearchData.inventoryname.selectData.push({
              value: item.inventoryname,
              label: item.inventoryname,
              cmassunitname: item.cmassunitname
            })
          })
        }
      })
    },
    // 根据用途名称获取关联型号
    changeInventoryname (e) {
      this.setSearchData.inventorystd.selectData = []
      this.setSearchData.inventorystd.selected = ''
      this.setSearchData.inventorystd.isdisabled = e.selected === undefined
      if (e.selected === undefined) return
      let curIdx = this.setSearchData.inventoryname.selectData.findIndex(item => { return item.value === e.selected })
      this.lineUnit = this.setSearchData.inventoryname.selectData[curIdx].cmassunitname + '/元'
      let data = {
        usefor: this.setSearchData.usefor.selected,
        inventoryname: e.selected
      }
      API.materialInventorystd(data).then(res => {
        if (res.data.Code === 10000) {
          res.data.Result.map(item => {
            this.setSearchData.inventorystd.selectData.push({
              value: item.inventorystd,
              label: item.inventorystd
            })
          })
        }
      })
    },
    async getBaseData () {
      API.materialWarehousename().then(res => { // 获取船舶
        if (res.data.Code === 10000) {
          res.data.Result.map(item => {
            this.setSearchData.shipname.selectData.push({
              value: item.shipname,
              label: item.shipname
            })
          })
        }
      })
      API.materialVendorname().then(res => { // 获取供应商
        if (res.data.Code === 10000) {
          res.data.Result.map(item => {
            this.setSearchData.vendorname.selectData.push({
              value: item.vendorname,
              label: item.vendorname
            })
          })
        }
      })
      await API.materialUsefor().then(res => { // 获取用途
        if (res.data.Code === 10000) {
          res.data.Result.map(item => {
            this.setSearchData.usefor.selectData.push({
              value: item.usefor,
              label: item.usefor,
              key: item.key
            })
          })
        }
      })
    },
    // 获取系统时间
    getSysDate () {
      if (Object.keys(this.$store.state.setState.singlePriceParam).length > 0) {
        // 获取存储的入参
        let storeQuery = this.$store.state.setState.singlePriceParam
        this.setSearchData.date_month_st.selected = storeQuery.date_month_st
        this.setSearchData.date_month_et.selected = storeQuery.date_month_et
        this.date_month_st = storeQuery.date_month_st
        this.date_month_et = storeQuery.date_month_et
      } else {
        API.getSysTime().then(res => {
          if (res.data.Code === 10000) {
            this.setSearchData.date_month_st.selected = res.data.now_date.substring(0, 5) + '01'
            this.setSearchData.date_month_et.selected = res.data.now_date.substring(0, 7)
            this.date_month_st = res.data.now_date.substring(0, 5) + '01'
            this.date_month_et = res.data.now_date.substring(0, 7)
          }
        })
      }
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    }
  }
}
</script>
<style lang="less" scoped>
.text_con {
  margin: 10px 0;
  button {
    float: right;
    color: #007DFF;
    font-size: 14px;
    font-weight: bold;
    margin-top: -5px;
    .ivu-icon {
      font-weight: bold;
    }
  }
}
.modal_content {
  font-size: 16px;
  > div:first-child {
    margin-bottom: 10px;
  }
  label {
    vertical-align: top;
  }
  .ivu-input-wrapper {
    width: 80%;
  }
}
</style>

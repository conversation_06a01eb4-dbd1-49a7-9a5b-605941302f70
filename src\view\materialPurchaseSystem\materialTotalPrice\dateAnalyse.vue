<template>
  <div> <!-- 时间分析 -->
    <Card>
      <search @searchResults='searchResults' @selectOnChanged='selectOnChanged' :setSearch='setSearchData' @resetResults='resetResults'></search>
    </Card>
    <Card style="margin-top: 10px">
      <Row>
        <Col span="14">
          <chart-line style="height: 300px;" unit="元" :value="priceLineData" @clickBack="lineBack" :clickable="true" :color="lineColor" rotate="45" text="采购总价"/>
        </Col>
        <Col span="10">
          <chart-pie style="height: 280px;" :value="pieData" :color="transColor" legendPosition="right" :center="pieCenter" legendType="scroll" text="采购总价占比"></chart-pie>
        </Col>
      </Row>
      <h3 class="text_con">
        {{ tableText }}
      </h3>
      <Row>
        <Col offset="2" span="9">
          <div class="export_btn">
            <Button type="text" @click="exportData">数据导出 <Icon type="ios-download-outline" size="18"></Icon></Button>
          </div>
          <Table border :loading="loading" :row-class-name="rowClassNameLeft" @on-row-click="timeClick" ref="selection" :columns="columns" :data="list" show-summary :summary-method="handleSummary" class="table_constyle"></Table>
        </Col>
        <Col offset="2" span="9">
          <div class="export_btn">
            <Button type="text" @click="exportDataSub">数据导出 <Icon type="ios-download-outline" size="18"></Icon></Button>
          </div>
          <Table border :loading="selectLoading" :row-class-name="rowClassNameRight" ref="selection" :columns="selectColumns" :data="selectList" size="small" @on-row-click="detailShow"></Table>
        </Col>
      </Row>
    </Card>
    <detailDrawer :modalData="modalData"></detailDrawer>
  </div>
</template>

<script>
import { ChartPie, ChartLine } from '_c/charts'
import search from '_c/search' // 查询组件
import API from '@/api/basicData'
import { queryDateAnalyse, queryUseforAnalyse, exportMonth, exportMonthSub } from '@/api/materialPurchaseSystem/materialTotalPrice'
import detailDrawer from './detailDrawer'
export default {
  components: {
    ChartPie,
    ChartLine,
    search,
    detailDrawer
  },
  data () {
    return {
      leftIndex: 0, // 左侧列表默认选中项
      tableText: '全部物料金额数据表',
      priceLineData: {
        xAxis: [],
        legend: [],
        smooth: 0,
        data: []
      },
      lineColor: ['#6699FF', '#E74823'],
      pieData: [],
      pieCenter: ['42%', '60%'],
      queryParam: {
        date_month_st: '',
        date_month_et: '',
        usefor: '',
        inventoryname: '',
        inventorystd: '',
        vendorname: '',
        shipname: ''
      },
      transColor: ['#5B8FF9', '#5AD8A6', '#5D7092', '#F6BD16', '#E8684A', '#6DC8EC', '#9270CA', '#FF9D4D', '#269A99', '#FF99C3', '#5B8FF9', '#BDD2FD'],
      loading: false,
      selectLoading: false,
      list: [],
      selectList: [],
      modalData: {
        modal: false,
        data: undefined,
        key: '',
        shipname: '',
        vendorname: [],
        date_month_st: '',
        date_month_et: ''
      },
      columns: [
        {
          title: '时间',
          key: 'data_month',
          align: 'center',
          fixed: 'left',
          width: 110,
          render: (h, params) => {
            return h('div', {}, params.row.data_month)
          }
        },
        {
          title: '总计(元)',
          key: 'cost_sum',
          align: 'center',
          fixed: 'right',
          minWidth: 100
        },
        {
          title: '百分比%',
          key: 'cost_sum_rate',
          align: 'center',
          fixed: 'right',
          minWidth: 100
        }
      ],
      selectColumns: [
        {
          title: '船舶',
          key: 'shipname',
          align: 'center',
          render: (h, params) => {
            let _that = this
            return h('div', {}, (!_that.queryParam.shipname || _that.queryParam.shipname === '') ? '所有船舶' : _that.queryParam.shipname)
          }
        },
        {
          title: '用途',
          key: 'usefor',
          align: 'center'
        },
        {
          title: '总计(元)',
          key: 'cost_sum',
          align: 'center'
        },
        {
          title: '百分比%',
          key: 'cost_sum_rate',
          align: 'center',
          width: 100
        }
      ],
      setSearchData: {
        date_month_st: {
          type: 'month',
          label: '时间',
          selected: '',
          width: 100,
          value: '',
          isdisable: false
        },
        date_month_et: {
          type: 'month_end',
          label: '-',
          selected: '',
          width: 100,
          value: '',
          isdisabled: false
        },
        usefor: {
          type: 'select',
          label: '种类',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 140,
          value: '',
          filterable: true,
          change: this.changeUsefor
        },
        inventoryname: {
          type: 'select',
          label: '名称',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 150,
          value: '',
          filterable: true,
          isdisabled: true,
          change: this.changeInventoryname
        },
        inventorystd: {
          type: 'select',
          label: '型号',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 150,
          value: '',
          filterable: true,
          isdisabled: true
        },
        shipname: {
          type: 'select',
          label: '船舶',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 130,
          value: '',
          filterable: true
        },
        vendorname: {
          type: 'select',
          label: '供应商',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 180,
          value: '',
          filterable: true
        }
      }
    }
  },
  created () {
    this.getSysDate()
    this.getBaseData()
  },
  beforeDestroy() {
    if (typeof this.setSearchData.date_month_st.selected === 'object') {
      let stYear = this.setSearchData.date_month_st.selected.getFullYear()
      let stMonth = this.setSearchData.date_month_st.selected.getMonth() + 1
      this.setSearchData.date_month_st.selected = stYear + '-' + (stMonth > 10 ? stMonth : '0' + stMonth)
    }
    if (typeof this.setSearchData.date_month_et.selected === 'object') {
      let etYear = this.setSearchData.date_month_et.selected.getFullYear()
      let etMonth = this.setSearchData.date_month_et.selected.getMonth() + 1
      this.setSearchData.date_month_et.selected = etYear + '-' + (etMonth > 10 ? etMonth : '0' + etMonth)
    }
    this.queryParam.date_month_st = this.setSearchData.date_month_st.selected
    this.queryParam.date_month_et = this.setSearchData.date_month_et.selected
    this.$store.commit('setDateAnalyse', this.queryParam)
  },
  methods: {
    // 获取列表
    getList () {
      this.queryParam.date_month_st = this.date_month_st
      this.queryParam.date_month_et = this.date_month_et
      this.loading = true
      this.priceLineData.xAxis = []
      this.priceLineData.data = []
      this.pieData = []
      queryDateAnalyse(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          this.loading = false
          this.list = res.data.Result
          res.data.Result.forEach(e => {
            this.priceLineData.xAxis.push(e.data_month)
            this.priceLineData.data.push(e.cost_sum)
            this.pieData.push({
              value: e.cost_sum,
              name: e.data_month
            })
          })
          if (this.list.length > 0) {
            Object.assign(this.queryParam, {
              date_month_st: this.list[0].data_month,
              date_month_et: this.list[1].data_month
            })
            this.getSelectList()
          }
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    getSelectList () {
      this.queryParam.date_month_st = this.date_month_st
      this.queryParam.date_month_et = this.date_month_et
      this.selectLoading = true
      queryUseforAnalyse(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          this.selectLoading = false
          this.selectList = res.data.Result
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 详情展示
    detailShow (item) {
      if (parseInt(item.cost_sum) === 0) {
        this.$Message.warning('暂无数据！')
        return
      }
      this.modalData = {
        modal: true,
        data: item,
        usefor: item.usefor,
        shipname: this.queryParam.shipname,
        vendorname: this.setSearchData.vendorname.selectData,
        date_month_st: this.queryParam.date_month_st,
        date_month_et: this.queryParam.date_month_et
      }
    },
    // 查询
    searchResults (e) {
      this.leftIndex = 0
      this.queryParam.vendorname = e.vendorname
      this.queryParam.usefor = e.usefor
      this.queryParam.inventoryname = e.inventoryname
      this.queryParam.inventorystd = e.inventorystd
      this.queryParam.shipname = e.shipname
      this.getList()
    },
    selectOnChanged (e) {
      if (e.flag === 'month_start') {
        this.date_month_st = e.key
      } else if (e.flag === 'month_end') {
        this.date_month_et = e.key
      }
    },
    // 重置
    async resetResults () {
      this.queryParam.vendorname = ''
      this.queryParam.usefor = ''
      this.queryParam.inventoryname = ''
      this.queryParam.inventorystd = ''
      this.queryParam.shipname = ''
      this.setSearchData.usefor.selected = ''
      this.setSearchData.inventoryname.selected = ''
      this.setSearchData.inventorystd.selected = ''
      this.setSearchData.vendorname.selected = ''
      this.setSearchData.shipname.selected = ''
      this.setSearchData.inventoryname.isdisabled = true
      this.setSearchData.inventorystd.isdisabled = true
      this.queryParam.shipname = ''
      this.tableText = '全部物料金额数据表'
      this.$store.state.setState.dateAnalyseParam = {}
      await this.getSysDate()
      await this.getList()
    },
    // 主表数据导出
    exportData () {
      this.queryParam.date_month_st = this.date_month_st
      this.queryParam.date_month_et = this.date_month_et
      exportMonth(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          window.open(res.data.fileUrl, '_blank')
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 附表数据导出
    exportDataSub () {
      this.queryParam.date_month_st = this.date_month_st
      this.queryParam.date_month_et = this.date_month_et
      exportMonthSub(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          window.open(res.data.fileUrl, '_blank')
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 柱状图点击回调
    lineBack (val) {
      if (this.selectLoading) return
      let _curIndex = this.list.findIndex(item => {
        return item.data_month === val.name
      })
      if (this.leftIndex === _curIndex) return
      this.leftIndex = _curIndex
      Object.assign(this.queryParam, {
        date_month_st: this.list[this.leftIndex].data_month,
        date_month_et: this.list[this.leftIndex].data_month
      })
      this.getSelectList()
    },
    // 获取系统时间
    async getSysDate () {
      if (Object.keys(this.$store.state.setState.dateAnalyseParam).length > 0) {
        // 获取存储的入参
        let storeQuery = this.$store.state.setState.dateAnalyseParam
        this.setSearchData.date_month_st.selected = storeQuery.date_month_st
        this.setSearchData.date_month_et.selected = storeQuery.date_month_et
        this.date_month_st = storeQuery.date_month_st
        this.date_month_et = storeQuery.date_month_et
      } else {
        await API.getSysTime().then(res => {
          if (res.data.Code === 10000) {
            this.setSearchData.date_month_st.selected = res.data.now_date.substring(0, 5) + '01'
            this.setSearchData.date_month_et.selected = res.data.now_date.substring(0, 7)
            this.date_month_st = res.data.now_date.substring(0, 5) + '01'
            this.date_month_et = res.data.now_date.substring(0, 7)
          }
        })
      }
    },
    handleSummary ({ columns, data }) {
      const sums = {}
      columns.forEach((column, index) => {
        const key = column.key
        if (index === 0) {
          sums[key] = {
            key,
            value: '总计'
          }
          return
        }
        if (index === columns.length - 1) {
          sums[key] = {
            key,
            value: '100'
          }
          return
        }
        const values = data.map(item => Number(item[key]))
        if (!values.every(value => isNaN(value))) {
          const v = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return prev + curr
            } else {
              return prev
            }
          }, 0)
          sums[key] = {
            key,
            value: v.toFixed(2)
          }
        } else {
          sums[key] = {
            key,
            value: ''
          }
        }
      })
      return sums
    },
    timeClick (item, idx) {
      if (this.selectLoading) return
      Object.assign(this.queryParam, {
        date_month_st: item.data_month,
        date_month_et: item.data_month
      })
      this.leftIndex = idx
      this.getSelectList()
    },
    // 左侧列表高光显示
    rowClassNameLeft (row, index) {
      if (index === this.leftIndex) {
        return 'select-table-info-row'
      }
      return ''
    },
    // 右侧列表高光显示
    rowClassNameRight (row, index) {
      if (this.queryParam.usefor && this.queryParam.usefor === row.usefor) {
        return 'select-table-info-row'
      }
      return ''
    },
    // 根据用途获取关联名称
    changeUsefor (e) {
      this.setSearchData.inventoryname.selectData = []
      this.setSearchData.inventoryname.selected = ''
      this.setSearchData.inventorystd.selectData = []
      this.setSearchData.inventorystd.selected = ''
      this.setSearchData.inventoryname.isdisabled = e.selected === undefined
      API.materialInventoryname({ usefor: e.selected }).then(res => {
        if (res.data.Code === 10000) {
          res.data.Result.map(item => {
            this.setSearchData.inventoryname.selectData.push({
              value: item.inventoryname,
              label: item.inventoryname
            })
          })
        }
      })
    },
    // 根据用途名称获取关联型号
    changeInventoryname (e) {
      this.setSearchData.inventorystd.selectData = []
      this.setSearchData.inventorystd.selected = ''
      this.setSearchData.inventorystd.isdisabled = e.selected === undefined
      if (e.selected === undefined) return
      let data = {
        usefor: this.setSearchData.usefor.selected,
        inventoryname: e.selected
      }
      API.materialInventorystd(data).then(res => {
        if (res.data.Code === 10000) {
          res.data.Result.map(item => {
            this.setSearchData.inventorystd.selectData.push({
              value: item.inventorystd,
              label: item.inventorystd
            })
          })
        }
      })
    },
    getBaseData () {
      API.materialWarehousename().then(res => { // 获取船舶
        if (res.data.Code === 10000) {
          res.data.Result.map(item => {
            this.setSearchData.shipname.selectData.push({
              value: item.shipname,
              label: item.shipname,
              key: item.key
            })
          })
        }
      })
      API.materialVendorname().then(res => { // 获取供应商
        if (res.data.Code === 10000) {
          res.data.Result.map(item => {
            this.setSearchData.vendorname.selectData.push({
              value: item.vendorname,
              label: item.vendorname
            })
          })
        }
      })
      API.materialUsefor().then(res => { // 获取用途
        if (res.data.Code === 10000) {
          res.data.Result.map((item, idx) => {
            this.setSearchData.usefor.selectData.push({
              value: item.usefor,
              label: item.usefor,
              key: item.key
            })
          })
          this.getList()
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
  .text_con {
    margin: 10px 0;
  }
  .export_btn {
    text-align: right;
    button {
      color: #007DFF;
      font-size: 14px;
      font-weight: bold;
      margin-top: -5px;
      .ivu-icon {
        font-weight: bold;
      }
    }
  }
  .table_constyle {
    clear: both;
  }
</style>
<style lang="less">
  .table_constyle {
    .ivu-table {
      overflow-x: auto;
      > div {
        overflow: visible !important;
      }
      .ivu-table-fixed div, .ivu-table-fixed-right div {
        margin-top: 0 !important;
      }
    }
  }
  .ivu-table .select-table-info-row td{
    background-color: #2db7f5;
    color: #fff;
  }
</style>

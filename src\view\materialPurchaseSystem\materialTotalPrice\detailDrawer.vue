<template>
  <Drawer v-model="modalData.modal"
    :title="title"
    width="1200"
    :mask-closable="false"
    @on-visible-change="modalShow">
    <h3 class="title_con" :style="'left:' + (title.length * 17) + 'px'">
      <span>{{ modalData.shipname }}</span>
      <span>{{ modalData.date_month_st }} ~ {{ modalData.date_month_et }}</span>
    </h3>
    <search @searchResults='searchResults' :setSearch='setSearchData' @resetResults='resetResults'></search>
    <Button type="text" @click="exportData" class="text_con">数据导出 <Icon type="ios-download-outline" size="18"></Icon></Button>
    <Table border :loading="loading" ref="selection" :columns="columns" :data="list"></Table>
    <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
      :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
  </Drawer>
</template>
<script>
import search from '_c/search' // 查询组件
import { materialInventoryname } from '@/api/basicData'
import { queryMaterialPage, exportMaterial } from '@/api/materialPurchaseSystem/materialSinglePrice'
export default {
  props: {
    modalData: Object
  },
  components: {
    search
  },
  data () {
    return {
      title: '',
      loading: false,
      list: [],
      total: null,
      listCurrent: 1,
      listQuery: {
        date_month_st: '',
        date_month_et: '',
        usefor: '',
        inventoryname: '',
        vendorname: '',
        shipname: '',
        pageSize: 10,
        pageIndex: 1
      },
      setSearchData: {
        inventoryname: {
          type: 'select',
          label: '名称',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 130,
          value: '',
          filterable: true
        },
        vendorname: {
          type: 'select',
          label: '供应商',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 180,
          value: '',
          filterable: true
        }
      },
      columns: [
        {
          title: '仓库',
          key: 'shipname',
          align: 'center',
          fixed: 'left',
          width: 90
        },
        {
          title: '船舶',
          key: 'xtgfboat',
          align: 'center',
          fixed: 'left',
          width: 90
        },
        {
          title: '入库日期',
          key: 'date',
          align: 'center',
          fixed: 'left',
          width: 100
        },
        {
          title: '存货名称',
          key: 'inventoryname',
          align: 'center',
          width: 135
        },
        {
          title: '规格型号',
          key: 'inventorystd',
          align: 'center',
          minWidth: 120
        },
        {
          title: '设备型号',
          key: 'unittype',
          align: 'center',
          minWidth: 100
        },
        {
          title: '单位',
          key: 'cmassunitname',
          align: 'center',
          width: 65
        },
        {
          title: '数量',
          key: 'quantity',
          align: 'center',
          minWidth: 100
        },
        {
          title: '原币含税单价(元)',
          key: 'price',
          align: 'center',
          minWidth: 150
        },
        {
          title: '原币价税合计(元)',
          key: 'iSum',
          align: 'center',
          minWidth: 150
        },
        {
          title: '设备生产厂家',
          key: 'producername',
          align: 'center',
          minWidth: 80
        },
        {
          title: '设备出厂编号',
          key: 'factorynumber',
          align: 'center',
          minWidth: 100
        },
        {
          title: '设备类别',
          key: 'factorytype',
          align: 'center',
          minWidth: 100
        },
        {
          title: '用途',
          key: 'usefor',
          align: 'center',
          minWidth: 100
        },
        {
          title: '供应商',
          key: 'vendorname',
          align: 'center',
          width: 150
        },
        {
          title: '部门',
          key: 'departmentname',
          align: 'center',
          width: 85
        },
        {
          title: '制单人',
          key: 'maker',
          align: 'center',
          width: 78
        },
        {
          title: '入库单号',
          key: 'code',
          align: 'center',
          width: 130
        },
        {
          title: '存货编码',
          key: 'inventorycode',
          align: 'center',
          width: 85
        },
      ]
    }
  },
  methods: {
    // 模态框展示
    modalShow (val) {
      if (val) {
        this.title = this.modalData.usefor + '采购明细'
        this.setSearchData.vendorname.selectData = this.modalData.vendorname
        this.listQuery.date_month_st = this.modalData.date_month_st
        this.listQuery.date_month_et = this.modalData.date_month_et
        this.listQuery.usefor = this.modalData.usefor
        this.listQuery.shipname = this.modalData.shipname
        materialInventoryname({ usefor: this.modalData.usefor }).then(res => {
          if (res.data.Code === 10000) {
            res.data.Result.map(item => {
              this.setSearchData.inventoryname.selectData.push({
                value: item.inventoryname,
                label: item.inventoryname
              })
            })
          }
        })
        this.getList()
      } else {
        this.title = ''
        this.resetResults()
      }
    },
    // 获取列表
    getList () {
      this.loading = true
      queryMaterialPage(this.listQuery).then(res => {
        if (res.data.Code === 10000) {
          this.loading = false
          this.list = res.data.Result
          this.total = res.data.Total
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 数据导出
    exportData () {
      exportMaterial(this.listQuery).then(res => {
        if (res.data.Code === 10000) {
          window.open(res.data.fileUrl, '_blank')
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 查询
    searchResults (e) {
      if (e.target) delete e.target
      this.listQuery.inventoryname = e.inventoryname
      this.listQuery.vendorname = e.vendorname
      this.listCurrent = 1
      this.listQuery.pageIndex = 1
      this.getList()
    },
    // 重置
    resetResults () {
      this.setSearchData.vendorname.selected = ''
      this.setSearchData.inventoryname.selected = ''
      this.listCurrent = 1
      this.listQuery.inventoryname = ''
      this.listQuery.vendorname = ''
      this.listQuery.pageIndex = 1
      this.getList()
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    }
  }
}
</script>
<style lang="less" scoped>
  .title_con {
    top: 14px;
    color: #666;
    position: absolute;
    span {
      margin-left: 15px;
    }
  }
  .filter-container {
    display: inline-block;
  }
  .text_con {
    float: right;
    color: #007DFF;
    font-size: 14px;
    font-weight: bold;
    margin-top: 5px;
    .ivu-icon {
      font-weight: bold;
    }
  }
  .ivu-table-wrapper {
    margin-top: 15px;
  }
</style>

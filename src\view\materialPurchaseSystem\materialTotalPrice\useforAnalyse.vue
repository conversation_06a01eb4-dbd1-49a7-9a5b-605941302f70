<template>
  <div> <!-- 用途分析 -->
    <Card>
      <search @searchResults='searchResults' @selectOnChanged='selectOnChanged' :setSearch='setSearchData' @resetResults='resetResults'></search>
    </Card>
    <Card style="margin-top: 10px">
      <Row>
        <Col span="13">
          <ChartBar style="height: 300px;" :value="barData" @clickBack="barBack" :clickable="true" text="物料采购示意图" rotate="45" unit="元"></ChartBar>
        </Col>
        <Col span="11">
          <chart-pie style="height: 320px;" :value="pieData" :color="transColor" legendPosition="right" :center="pieCenter" legendType="scroll" text="物料采购比例图"></chart-pie>
        </Col>
      </Row>
      <h3 class="text_con">
        {{ tableText }}
      </h3>
      <Row>
        <Col offset="2" span="9">
          <div class="export_btn">
            <Button type="text" @click="exportData">数据导出 <Icon type="ios-download-outline" size="18"></Icon></Button>
          </div>
          <Table border :loading="loading" :row-class-name="rowClassNameLeft" ref="selection" :columns="columns" :data="list" show-summary :summary-method="handleSummary" @on-row-click="usrforClick" size="small"></Table>
        </Col>
        <Col offset="2" span="9">
          <h3 class="export_btn">
            <Button type="text" @click="exportDataSub">数据导出 <Icon type="ios-download-outline" size="18"></Icon></Button>
          </h3>
          <Table border :loading="selectLoading" :row-class-name="rowClassNameRight" ref="selection" :columns="selectColumns" :data="selectList" size="small" @on-row-click="detailShow"></Table>
        </Col>
      </Row>
    </Card>
    <detailDrawer :modalData="modalData"></detailDrawer>
  </div>
</template>

<script>
import { ChartPie, ChartBar } from '_c/charts'
import search from '_c/search' // 查询组件
import API from '@/api/basicData'
import { queryUseforAnalyse, exportUsefor, exportShipfor, queryWarehousenameAnalyse } from '@/api/materialPurchaseSystem/materialTotalPrice'
import detailDrawer from './detailDrawer'
export default {
  components: {
    ChartPie,
    ChartBar,
    search,
    detailDrawer
  },
  data () {
    return {
      leftIndex: 0, // 左侧列表默认选中项
      tableText: '全部物料金额数据表',
      barData: {
        xAxis: [],
        data: []
      },
      pieData: [],
      pieCenter: ['40%', '60%'],
      queryParam: {
        date_month_st: '',
        date_month_et: '',
        shipname: '',
        vendorname: ''
      },
      transColor: ['#5B8FF9', '#5AD8A6', '#5D7092', '#F6BD16', '#E8684A', '#6DC8EC', '#9270CA', '#FF9D4D', '#269A99', '#FF99C3', '#5B8FF9', '#BDD2FD'],
      loading: false,
      selectLoading: false,
      list: [],
      selectList: [],
      modalData: {
        modal: false,
        data: undefined,
        key: '',
        shipname: '',
        vendorname: [],
        date_month_st: '',
        date_month_et: ''
      },
      allColumns: [], // 存储原columns所有数据
      columns: [
        {
          title: '用途',
          key: 'usefor',
          align: 'center'
        },
        {
          title: '总计(元)',
          key: 'cost_sum',
          align: 'center'
        },
        {
          title: '占总金额%',
          key: 'cost_sum_rate',
          align: 'center',
          width: 100
        }
      ],
      selectColumns: [
        {
          title: '船舶',
          key: 'shipname',
          align: 'center'
        },
        {
          title: '总计(元)',
          key: 'cost_sum',
          align: 'center'
        },
        {
          title: '百分比%',
          key: 'cost_sum_rate',
          align: 'center',
          width: 100
        }
      ], // 右侧船舶表
      setSearchData: {
        date_month_st: {
          type: 'month',
          label: '时间',
          selected: '',
          width: 100,
          value: '',
          isdisable: false
        },
        date_month_et: {
          type: 'month_end',
          label: '-',
          selected: '',
          width: 100,
          value: '',
          isdisabled: false
        },
        shipname: {
          type: 'select',
          label: '船舶',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 130,
          value: '',
          filterable: true
        },
        vendorname: {
          type: 'select',
          label: '供应商',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 180,
          value: '',
          filterable: true
        }
      }
    }
  },
  created () {
    this.getSysDate()
    this.getBaseData()
  },
  beforeDestroy () {
    this.$store.commit('setUseforParam', this.queryParam)
  },
  methods: {
    // 获取列表
    getList () {
      this.queryParam.date_month_st = this.date_month_st
      this.queryParam.date_month_et = this.date_month_et
      this.loading = true
      this.barData.xAxis = []
      this.barData.data = []
      this.pieData = []
      queryUseforAnalyse(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          this.loading = false
          this.$nextTick(() =>{
            this.list = res.data.Result
            res.data.Result.forEach(e => {
              this.barData.xAxis.push(e.usefor)
              this.barData.data.push(e.cost_sum)
              this.pieData.push({
                value: e.cost_sum,
                name: e.usefor
              })
            })
            if (this.list.length > 0) {
              Object.assign(this.queryParam, {
                usefor: this.list[0].usefor
              })
              this.getSelectList()
            }
          })
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 查询
    searchResults (e) {
      this.queryParam.shipname = e.shipname
      this.queryParam.vendorname = e.vendorname
      this.leftIndex = 0
      this.getList()
    },
    selectOnChanged (e) {
      if (e.flag === 'month_start') {
        this.date_month_st = e.key
      } else if (e.flag === 'month_end') {
        this.date_month_et = e.key
      }
    },
    // 重置
    async resetResults () {
      this.queryParam.shipname = ''
      this.queryParam.vendorname = ''
      this.setSearchData.shipname.selected = ''
      this.setSearchData.vendorname.selected = ''
      this.queryParam.usefor = ''
      this.leftIndex = 0
      this.tableText = '全部物料金额数据表'
      this.$store.state.setState.useforParam = {}
      await this.getSysDate()
      await this.getList()
    },
    // 主表数据导出
    exportData () {
      exportUsefor(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          window.open(res.data.fileUrl, '_blank')
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 附表数据导出
    exportDataSub () {
      exportShipfor(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          window.open(res.data.fileUrl, '_blank')
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 柱状图点击回调
    async barBack (idx) {
      if (this.selectLoading) return
      let _curIndex = parseInt(idx)
      if (this.leftIndex === _curIndex) return
      this.leftIndex = _curIndex
      Object.assign(this.queryParam, {
        usefor: this.list[this.leftIndex].usefor
      })
      await this.getSelectList()
    },
    // 获取系统时间
    async getSysDate () {
      if (Object.keys(this.$store.state.setState.useforParam).length > 0) {
        // 获取存储的入参
        let storeQuery = this.$store.state.setState.useforParam
        this.setSearchData.date_month_st.selected = storeQuery.date_month_st
        this.setSearchData.date_month_et.selected = storeQuery.date_month_et
        this.date_month_st = storeQuery.date_month_st
        this.date_month_et = storeQuery.date_month_et
      } else {
        await API.getSysTime().then(res => {
          if (res.data.Code === 10000) {
            this.setSearchData.date_month_st.selected = res.data.now_date.substring(0, 5) + '01'
            this.setSearchData.date_month_et.selected = res.data.now_date.substring(0, 7)
            this.date_month_st = res.data.now_date.substring(0, 5) + '01'
            this.date_month_et = res.data.now_date.substring(0, 7)
          }
        })
      }
    },
    getBaseData () {
      API.materialVendorname().then(res => { // 获取供应商
        if (res.data.Code === 10000) {
          res.data.Result.map(item => {
            this.setSearchData.vendorname.selectData.push({
              value: item.vendorname,
              label: item.vendorname
            })
          })
        }
      })
      API.materialWarehousename().then(res => { // 获取船舶
        if (res.data.Code === 10000) {
          res.data.Result.map((item, idx) => {
            this.setSearchData.shipname.selectData.push({
              value: item.shipname,
              label: item.shipname,
              key: item.key
            })
          })
          this.getList()
        }
      })
    },
    // 左侧列表高光显示
    rowClassNameLeft (row, index) {
      if (index === this.leftIndex) {
        return 'select-table-info-row'
      }
      return ''
    },
    // 右侧列表高光显示
    rowClassNameRight (row, index) {
      if (this.queryParam.shipname && this.queryParam.shipname === row.shipname) {
        return 'select-table-info-row'
      }
      return ''
    },
    // 左侧列表点击事件
    async usrforClick (item, idx) {
      if (this.selectLoading) return
      Object.assign(this.queryParam, {
        usefor: item.usefor
      })
      this.leftIndex = idx
      await this.getSelectList()
    },
    // 详情展示
    detailShow (item) {
      if (parseInt(item.cost_sum) === 0) {
        this.$Message.warning('暂无数据！')
        return
      }
      this.modalData = {
        modal: true,
        data: item,
        usefor: this.queryParam.usefor,
        shipname: item.shipname,
        vendorname: this.setSearchData.vendorname.selectData,
        date_month_st: this.queryParam.date_month_st,
        date_month_et: this.queryParam.date_month_et
      }
    },
    // 获取右侧列表
    async getSelectList () {
      this.selectLoading = true
      await queryWarehousenameAnalyse(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          this.selectLoading = false
          this.$nextTick(() => {
            this.selectList = res.data.Result
          })
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    handleSummary ({ columns, data }) {
      const sums = {}
      columns.forEach((column, index) => {
        const key = column.key
        if (index === 0) {
          sums[key] = {
            key,
            value: '总计'
          }
          return
        }
        if (index === columns.length - 1) {
          sums[key] = {
            key,
            value: '100'
          }
          return
        }
        const values = data.map(item => Number(item[key]))
        if (!values.every(value => isNaN(value))) {
          const v = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return prev + curr
            } else {
              return prev
            }
          }, 0)
          sums[key] = {
            key,
            value: v.toFixed(2)
          }
        } else {
          sums[key] = {
            key,
            value: ''
          }
        }
      })
      return sums
    }
  }
}
</script>
<style lang="less" scoped>
  .text_con {
    margin: 10px 0;
  }
  .export_btn {
    text-align: right;
    button {
      color: #007DFF;
      font-size: 14px;
      font-weight: bold;
      margin-top: -5px;
      .ivu-icon {
        font-weight: bold;
      }
    }
  }
  .table_constyle {
    clear: both;
  }
</style>
<style lang="less">
  .table_constyle {
    .ivu-table {
      overflow-x: auto;
      > div {
        overflow: visible !important;
      }
      .ivu-table-fixed div, .ivu-table-fixed-right div {
        margin-top: 0 !important;
      }
    }
  }
  .ivu-table .select-table-info-row td{
    background-color: #2db7f5;
    color: #fff;
  }
</style>

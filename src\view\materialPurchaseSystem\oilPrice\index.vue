<template>
  <div>
    <Tabs type="card" v-model="tabId">
      <TabPane label="月份分析">
        <monthAnalysis v-if="tabId === 0"></monthAnalysis>
      </TabPane>
      <TabPane label="日期分析">
        <dateAnalysis v-if="tabId === 1"></dateAnalysis>
      </TabPane>
      <TabPane label="区域分析">
        <areaAnalysis v-if="tabId === 2"></areaAnalysis>
      </TabPane>
      <TabPane label="季度分析">
        <quarterlyAnalysis v-if="tabId === 3"></quarterlyAnalysis>
      </TabPane>
    </Tabs>
  </div>
</template>

<script>
import monthAnalysis from './monthAnalysis.vue'
import dateAnalysis from './dateAnalysis.vue'
import areaAnalysis from './areaAnalysis.vue'
import quarterlyAnalysis from './quarterlyAnalysis.vue'

export default {
  components: {
    monthAnalysis,
    dateAnalysis,
    areaAnalysis,
    quarterlyAnalysis
  },
  data () {
    return {
      tabId: 0
    }
  }
}
</script>

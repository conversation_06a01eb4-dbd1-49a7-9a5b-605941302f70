<template>
  <div>
    <Card>
      <search @searchResults='searchResults' :setSearch='setSearchData' @resetResults='resetResults'></search>
      <div style="margin-top: 10px;">
        <label>时间：</label>
        <div class="date_selec">
          <DatePicker v-model="dateValue" type="year" confirm :editable="false" @on-change="changeDate" @on-clear="clearRightDate('dateValue')" :options="options1" style="width: 200px;z-index:100;"></DatePicker>
          <span>{{ curTimeValue }}</span>
        </div>
        <label> - </label>
        <div class="date_selec">
          <DatePicker v-model="compareValue" type="year" confirm :editable="false" @on-change="changeCompareDate" @on-clear="clearRightDate('compareValue')" :options="options2" style="width: 200px;z-index:100;"></DatePicker>
          <span>{{ compareTimeValue }}</span>
        </div>
      </div>
    </Card>
    <Card style="margin-top: 10px">
      <chart-line style="height: 300px;" unit="元/吨" :value="lineData" :color="lineColor" rotate="45" :showOnemarkLine="true" text="油料均价走势"/>
      <h3 class="text_con">
        油料采购数据表
        <Button type="text" @click="exportData">数据导出 <Icon type="ios-download-outline" size="18"></Icon></Button>
      </h3>
      <Table border :loading="loading" ref="selection" :columns="columns" :data="list" class="data_list"></Table>
       <!-- show-summary :summary-method="handleSummary" -->
    </Card>
  </div>
</template>

<script>
import '@/assets/menu_icon/iconfont.css'
import { ChartLine } from '_c/charts'
import search from '_c/search' // 查询组件
import API from '@/api/basicData'
import { queryAreaList } from '@/api/materialPurchaseSystem/areaConfigure'
import { queryOilListByQuarter, exportOilListByMonth, exportOilListByQuarter } from '@/api/materialPurchaseSystem/oilPrice'
export default {
  components: {
    ChartLine,
    search
  },
  data () {
    return {
      queryParam: {
        quarter_num_st: '', // 开始季度（必填，格式：年+季度，例如202201表示22年1季度）
        quarter_num_et: '', // 结束季度（必填，格式：年+季度，例如202203表示22年3季度）
        shipname: '',
        vendorname: '',
        inventoryname: '',
        unified_area_id: ''
      },
      lineData: {
        xAxis: [],
        legend: ['', '三方价格'],
        smooth: 0,
        symbol: ['emptyCircle', 'none'],
        data: [[], []]
      },
      curTimeValue: '',
      compareTimeValue: '',
      dateValue: '',
      compareValue: '',
      options1: {},
      options2: {},
      lineColor: ['#6699FF', '#E74823'],
      loading: false,
      list: [],
      columns: [],
      setSearchData: {
        inventoryname: {
          type: 'select',
          label: '名称',
          selectData: [
            {
              value: '轻油',
              label: '轻油'
            },
            {
              value: '重油',
              label: '重油'
            }
          ],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 115,
          value: '',
          filterable: true,
          isdisabled: false
        },
        unified_area_id: {
          type: 'select',
          label: '区域',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 115,
          value: '',
          filterable: true,
          isdisabled: false
        },
        shipname: {
          type: 'select',
          label: '船舶',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 115,
          value: '',
          filterable: true
        },
        vendorname: {
          type: 'select',
          label: '供应商',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 180,
          value: '',
          filterable: true
        }
      }
    }
  },
  created () {
    this.getOptions()
    this.getData()
  },
  methods: {
    // 获取列表
    getList () {
      this.loading = true
      this.list = []
      this.columns = []
      this.lineData.xAxis = []
      this.lineData.data = [[], []]
      queryOilListByQuarter(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          this.loading = false
          // this.list = res.data.Result
          this.lineData.legend[0] = this.queryParam.inventoryname
          if (!res.data.Result || res.data.Result.length === 0) return
          this.columns.push({
            title: '季度',
            key: 'title',
            width: 145,
            align: 'center',
            fixed: 'left'
          })
          res.data.Result.forEach((e, idx) => {
            let quarterStr = this.quarterToStr(e.quarter_num)
            this.lineData.xAxis.push(quarterStr)
            this.lineData.data[0].push(e.price_avg)
            this.lineData.data[1].push(e.third_price_avg + ',' + e.third_bottom_price + '~' + e.third_top_price)
            this.columns.push({
              title: quarterStr.split(',')[0],
              key: 'value' + idx,
              align: 'center',
              minWidth: 135
            })
          })
          this.columns.push({
            align: 'center',
            key: 'sumary',
            title: '合计',
            width: 135,
            fixed: 'right'
          })
          this.resetTableData(res.data.Result, this.list, 0, 'price_avg')
          this.resetTableData(res.data.Result, this.list, 1, 'increase_avg_amount')
          this.resetTableData(res.data.Result, this.list, 2, 'increase_avg_rate')
          this.resetTableData(res.data.Result, this.list, 3, 'third_price_avg')
          this.resetTableData(res.data.Result, this.list, 4, 'third_bottom_price', 'third_top_price')
          this.resetTableData(res.data.Result, this.list, 5, 'quantity_sum')
          this.resetTableData(res.data.Result, this.list, 6, 'cost_sum')
          this.list[0].sumary = (this.list[6].sumary / this.list[5].sumary).toFixed(2)
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    quarterToStr (str) {
      let _yearStr = str.substring(0, 4)
      let _quarterStr = str.substring(4, 6)
      let _quarterNum = parseInt(_quarterStr)
      let _backStr = ''
      if (_quarterNum === 1) {
        _backStr = _yearStr + '第一季度,'
      }
      if (_quarterNum === 2) {
        _backStr = _yearStr + '第二季度,'
      }
      if (_quarterNum === 3) {
        _backStr = _yearStr + '第三季度,'
      }
      if (_quarterNum === 4) {
        _backStr = _yearStr + '第四季度,'
      }
      return _backStr
    },
    resetTableData (arr, data, index, str1, str2) {
      switch (index) {
        case 0:
          data.push({
            title: this.queryParam.inventoryname + '均值(元/吨)'
          })
          break
        case 1:
          data.push({
            title: '增长量(元/吨)'
          })
          break
        case 2:
          data.push({
            title: '增长率(%)'
          })
          break
        case 3:
          data.push({
            title: '三方' + this.queryParam.inventoryname + '均值(元/吨)'
          })
          break
        case 4:
          data.push({
            title: '三方' + this.queryParam.inventoryname + '价格区间(元/吨)'
          })
          break
        case 5:
          data.push({
            title: '采购量(吨)'
          })
          break
        case 6:
          data.push({
            title: this.queryParam.inventoryname + '总金额(元)'
          })
          break
        default:
          break
      }
      let _curSum = 0
      let bottomSum = 0
      let topSum = 0
      let classObj = {}
      arr.forEach((item, idx) => {
        let valueIdx = `value${idx}`
        if (str2 !== undefined) {
          Object.assign(data[index], {
            ['value' + idx]: item[str1] + '~' + item[str2]
          })
        } else if (index === 1 || index === 2) {
          Object.assign(classObj, {
            ['value' + idx]: item[str1] < 0 ? 'downClass' : 'upClass'
          })
          Object.assign(data[index], {
            ['value' + idx]: item[str1] < 0 ? '↓' + item[str1] : '↑' + item[str1],
            cellClassName: classObj
          })
        } else {
          Object.assign(data[index], {
            ['value' + idx]: item[str1]
          })
        }
        if (index === 4) {
          bottomSum += parseFloat(data[index][valueIdx].split('~')[0])
          topSum += parseFloat(data[index][valueIdx].split('~')[1])
          _curSum = (bottomSum / arr.length).toFixed(2) + '~' + (topSum / arr.length).toFixed(2)
        } else {
          _curSum += parseFloat(data[index][valueIdx])
        }
      })
      let curSumary
      if (index === 1 || index === 2) {
        curSumary = '-'
      } else if (index === 5 || index === 6) {
        curSumary = _curSum.toFixed(2)
      } else if (index === 4) {
        curSumary = _curSum
      } else {
        curSumary = (_curSum / arr.length).toFixed(2)
      }
      Object.assign(data[index], {
        sumary: curSumary
      })
    },
    // 选择时间右侧月份触发
    changeDate (date) {
      if (date) {
        this.dateValue = ''
        this.curYearValue = date
      }
    },
    // 选择对比时间右侧月份触发
    changeCompareDate (date) {
      if (date !== '') {
        this.compareValue = ''
        this.curYearCompareValue = date
      }
    },
    getOptions () {
      let _that = this
      _that.options1 = {
        shortcuts: [
          {
            text: '第一季度',
            value () {
              let curYear = _that.curYearValue || new Date().getFullYear()
              _that.queryParam.quarter_num_st = curYear + '01'
              _that.curTimeValue = curYear + '第一季度'
            }
          },
          {
            text: '第二季度',
            value () {
              let curYear = _that.curYearValue || new Date().getFullYear()
              _that.queryParam.quarter_num_st = curYear + '02'
              _that.curTimeValue = curYear + '第二季度'
            }
          },
          {
            text: '第三季度',
            value () {
              let curYear = _that.curYearValue || new Date().getFullYear()
              _that.queryParam.quarter_num_st = curYear + '03'
              _that.curTimeValue = curYear + '第三季度'
            }
          },
          {
            text: '第四季度',
            value () {
              let curYear = _that.curYearValue || new Date().getFullYear()
              _that.queryParam.quarter_num_st = curYear + '04'
              _that.curTimeValue = curYear + '第四季度'
            }
          }
        ]
      }
      _that.options2 = {
        shortcuts: [
          {
            text: '第一季度',
            value () {
              let curYear = _that.curYearCompareValue || new Date().getFullYear()
              _that.queryParam.quarter_num_et = curYear + '01'
              _that.compareTimeValue = curYear + '第一季度'
            }
          },
          {
            text: '第二季度',
            value () {
              let curYear = _that.curYearCompareValue || new Date().getFullYear()
              _that.queryParam.quarter_num_et = curYear + '02'
              _that.compareTimeValue = curYear + '第二季度'
            }
          },
          {
            text: '第三季度',
            value () {
              let curYear = _that.curYearCompareValue || new Date().getFullYear()
              _that.queryParam.quarter_num_et = curYear + '03'
              _that.compareTimeValue = curYear + '第三季度'
            }
          },
          {
            text: '第四季度',
            value () {
              let curYear = _that.curYearCompareValue || new Date().getFullYear()
              _that.queryParam.quarter_num_et = curYear + '04'
              _that.compareTimeValue = curYear + '第四季度'
            }
          }
        ]
      }
    },
    // 查询
    searchResults (e) {
      this.queryParam.shipname = e.shipname
      this.queryParam.vendorname = e.vendorname
      this.queryParam.inventoryname = e.inventoryname
      this.queryParam.unified_area_id = e.unified_area_id
      delete e.target
      this.getList()
    },
    // 重置
    resetResults () {
      this.queryParam.shipname = ''
      this.queryParam.vendorname = ''
      this.queryParam.unified_area_id = ''
      this.setSearchData.shipname.selected = ''
      this.setSearchData.vendorname.selected = ''
      this.setSearchData.unified_area_id.selected = ''
      this.setSearchData.inventoryname.selected = this.setSearchData.inventoryname.selectData[1].value
      this.queryParam.inventoryname = this.setSearchData.inventoryname.selectData[1].value
      this.getList()
    },
    // 数据导出
    exportData () {
      exportOilListByQuarter(this.queryParam).then(res => {
        if (res.data.Code === 10000) {
          window.open(res.data.fileUrl, '_blank')
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    getData () {
      API.materialWarehousename().then(res => { // 获取船舶
        if (res.data.Code === 10000) {
          res.data.Result.map(item => {
            this.setSearchData.shipname.selectData.push({
              value: item.shipname,
              label: item.shipname
            })
          })
        }
      })
      API.materialVendorname().then(res => { // 获取供应商
        if (res.data.Code === 10000) {
          res.data.Result.map(item => {
            this.setSearchData.vendorname.selectData.push({
              value: item.vendorname,
              label: item.vendorname
            })
          })
        }
      })
      // 获取区域
      queryAreaList().then(res => {
        if (res.data.Code === 10000) {
          res.data.Result.map(item => {
            this.setSearchData.unified_area_id.selectData.push({
              value: item.unified_area_id,
              label: item.area_name
            })
          })
        }
      })
      this.queryParam.inventoryname = this.setSearchData.inventoryname.selectData[1].value
      this.setSearchData.inventoryname.selected = this.setSearchData.inventoryname.selectData[1].value
      // API.materialInventoryname({ usefor: '油料' }).then(res => { // 获取名称 暂时默认（2023.02.10），由于金蝶带回多个数据
      //   if (res.data.Code === 10000) {
      //     res.data.Result.map(item => {
      //       this.setSearchData.inventoryname.selectData.push({
      //         value: item.inventoryname,
      //         label: item.inventoryname
      //       })
      //     })
      //     this.queryParam.inventoryname = res.data.Result[1].inventoryname
      //     this.setSearchData.inventoryname.selected = res.data.Result[1].inventoryname
      //     // this.getList()
      //   }
      // })
    }
  }
}
</script>
<style lang="less" scoped>
  .text_con {
    margin: 10px 0;
    button {
      float: right;
      color: #007DFF;
      font-size: 14px;
      font-weight: bold;
      margin-top: -5px;
      .ivu-icon {
        font-weight: bold;
      }
    }
  }
</style>
<style lang="less" scoped>
.data_list {
  .upClass {
    color: red;
  }
  .downClass {
    color: green;
  }
}
.date_selec {
    position: relative;
    display: inline;
    span {
      position: absolute;
      top: 0;
      left: 10px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 86%;
    }
  }
</style>

<template>
  <Drawer :title="modalData.title" :mask-closable="false" v-model="modalData.modal" :data="modalData.data" @on-visible-change="modalShow" width="1000">
    <DatePicker type="date" v-model="release_date" style="width: 200px;margin-bottom: 20px;"></DatePicker>
    <Row :gutter="16" style="text-align: center;">
      <Col span="3">&nbsp;</Col>
      <Col span="3">区域</Col>
      <Col span="3">最低价</Col>
      <Col span="3">最高价</Col>
      <Col span="3">平均价</Col>
      <Col span="3">上次平均价</Col>
      <Col span="3">涨跌幅(%)</Col>
    </Row>
    <Form ref="formDynamic" :model="formDynamic">
      <div v-for="(item, index) in formDynamic.addBatchArray" :key="index" class="oil_prices">
        <Row :gutter="16">
          <Col span="3" style="text-align: right;">{{ item.product_model }} :</Col>
          <Col span="3">
            <FormItem :prop="'addBatchArray.' + index + '.area_name'">
              <Input v-model="item.area_name" disabled></Input>
            </FormItem>
          </Col>
          <Col span="3">
            <FormItem :prop="'addBatchArray.' + index + '.bottom_price'" :rules="{ required: true, message: '此处不能为空！', trigger: 'blur'}">
              <Input v-model="item.bottom_price" type="number" @on-change="getMiddlePrice"></Input>
            </FormItem>
          </Col>
          <Col span="3">
            <FormItem :prop="'addBatchArray.' + index + '.top_price'" :rules="{ required: true, message: '此处不能为空！', trigger: 'blur'}">
              <Input v-model="item.top_price" type="number" @on-change="getMiddlePrice"></Input>
            </FormItem>
          </Col>
          <Col span="3">
            <FormItem :prop="'addBatchArray.' + index + '.middle_price'">
              <Input v-model="item.middle_price" disabled></Input>
            </FormItem>
          </Col>
          <Col span="3">
            <FormItem :prop="'addBatchArray.' + index + '.last_middle_price'" :rules="{ required: true, message: '此处不能为空！', trigger: 'blur'}">
              <Input v-model="item.last_middle_price" type="number" @on-change="getMiddlePrice"></Input>
            </FormItem>
          </Col>
          <Col span="3">
            <FormItem :prop="'addBatchArray.' + index + '.rose_rate'">
              <Input v-model="item.rose_rate" disabled></Input>
            </FormItem>
          </Col>
        </Row>
      </div>
    </Form>
    <div class="demo-drawer-footer">
      <Button style="margin-right: 8px" @click="modalData.modal = false">取消</Button>
      <Button type="primary" v-if="modalData.type === 'create'" @click="createData">保存</Button>
      <Button type="primary" v-else @click="updateData">保存</Button>
    </div>
  </Drawer>
</template>
<script>
import { getSysTime } from '@/api/basicData'
import { addOilPrice, updateOilPrice } from '@/api/materialPurchaseSystem/trilateralOilPrice'
export default {
  props: {
    modalData: Object
  },
  data () {
    return {
      release_date: '',
      formDynamic: {
        addBatchArray: [
          {
            product_model: '船用180CST',
            product_model_code: '25464',
            area_name: '华东',
            bottom_price: '',
            top_price: '',
            middle_price: '',
            rose_rate: '',
            release_date: ''
          }, {
            product_model: '0#',
            product_model_code: '25574',
            area_name: '华东',
            bottom_price: '',
            top_price: '',
            middle_price: '',
            rose_rate: '',
            release_date: ''
          }
        ]
      }
    }
  },
  methods: {
    // 计算平均价/涨跌幅
    getMiddlePrice () {
      this.formDynamic.addBatchArray.map(item => {
        if (item.bottom_price !== '' && item.top_price !== '') {
          item.middle_price = (parseInt(item.bottom_price) + parseInt(item.top_price)) / 2
          if (item.last_middle_price !== '') {
            item.rose_rate = (((parseInt(item.middle_price) - parseInt(item.last_middle_price)) / parseInt(item.last_middle_price)) * 100).toFixed(2)
          }
        }
      })
    },
    // 新增保存
    createData () {
      this.$refs['formDynamic'].validate((valid) => {
        if (valid) {
          this.$Modal.confirm({
            title: '提示',
            content: '<p>确认新增三方价格？</p>',
            loading: true,
            onOk: () => {
              this.formDynamic.addBatchArray.map(item => {
                delete item.last_middle_price
              })
              let data = JSON.stringify(this.formDynamic.addBatchArray)
              addOilPrice({ addBatchArray: data }).then(res => {
                if (res.data.Code === 10000) {
                  this.loading = false
                  this.$Modal.remove()
                  this.modalData.modal = false
                  this.$Message.success(res.data.Message)
                  this.$emit('callback')
                } else {
                  this.loading = false
                  this.$Modal.remove()
                  this.$Message.error(res.data.Message)
                }
              })
            }
          })
        }
      })
    },
    // 编辑保存
    updateData () {
      this.$refs['formDynamic'].validate((valid) => {
        if (valid) {
          this.$Modal.confirm({
            title: '提示',
            content: '<p>确认修改三方价格？</p>',
            loading: true,
            onOk: () => {
              this.formDynamic.addBatchArray.map(item => {
                delete item.last_middle_price
              })
              updateOilPrice(this.formDynamic.addBatchArray[0]).then(res => {
                if (res.data.Code === 10000) {
                  this.loading = false
                  this.$Modal.remove()
                  this.modalData.modal = false
                  this.$Message.success(res.data.Message)
                  this.$emit('callback')
                } else {
                  this.loading = false
                  this.$Modal.remove()
                  this.$Message.error(res.data.Message)
                }
              })
            }
          })
        }
      })
    },
    // 模态框展示
    modalShow (val) {
      if (val) {
        if (this.modalData.type === 'create') {
          getSysTime().then(res => {
            if (res.data.Code === 10000) {
              this.release_date = res.data.now_date
              this.formDynamic.addBatchArray.map(item => {
                item.release_date = res.data.now_date
              })
            }
          })
        } else {
          this.release_date = this.modalData.data.release_date
          this.formDynamic.addBatchArray = [{
            oil_price_id: this.modalData.data.oil_price_id,
            product_model: this.modalData.data.product_model,
            product_model_code: this.modalData.data.product_model_code,
            area_name: this.modalData.data.area_name,
            bottom_price: this.modalData.data.bottom_price,
            top_price: this.modalData.data.top_price,
            middle_price: this.modalData.data.middle_price,
            rose_rate: this.modalData.data.rose_rate,
            release_date: this.modalData.data.release_date
          }]
        }
      } else {
        this.$refs['formDynamic'].resetFields()
        this.formDynamic = {
          addBatchArray: [
            {
              product_model: '船用180CST',
              product_model_code: '25464',
              area_name: '华东',
              bottom_price: '',
              top_price: '',
              middle_price: '',
              rose_rate: '',
              release_date: ''
            }, {
              product_model: '0#',
              product_model_code: '25574',
              area_name: '华东',
              bottom_price: '',
              top_price: '',
              middle_price: '',
              rose_rate: '',
              release_date: ''
            }
          ]
        }
      }
    }
  }
}
</script>
<style lang="less" scoped>
.demo-drawer-footer {
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  background: #fff;
}
.oil_prices {
  margin: 10px 0 40px;
  line-height: 30px;
}
</style>

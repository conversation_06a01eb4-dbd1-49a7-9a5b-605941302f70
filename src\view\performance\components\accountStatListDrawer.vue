<template>
  <Drawer :title="modalData.title"
    v-model="modalData.modal"
    :data="modalData.data"
    @on-visible-change="modalShow"
    width="850">
    <search @searchResults='searchResults' :setSearch='searchData' @resetResults='resetResults'></search>
    <Table border :loading="listLoading" ref="selection" :columns="columns" :data="list"></Table>
    <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
      :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
    <div class="demo-drawer-footer">
      <Button type="primary" @click="modalData.modal = false">返回</Button>
    </div>
  </Drawer>
</template>
<script>
import search from '_c/search'
import { querySysPerformancePage } from '@/api/performance'
import { queryUnifiedAccountList } from '@/api/jurisdictionManage/userManagement'
import { queryDepartManageList } from '@/api/jurisdictionManage/departManagement'
export default {
  props: {
    modalData: Object
  },
  components: {
    search,
  },
  data () {
    return {
      listLoading: false,
      list: [],
      total: 0,
      listCurrent: 1,
      listQuery: {
        pageSize: 10,
        pageIndex: 1,
        belong_month: '',
        dept_id: '',
        is_finish: '',
        dept_ids: '',
        is_personal: '',
        unified_account_id: ''
      },
      searchData: {
        unified_account_id: {
          type: 'select',
          label: '姓名',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 135,
          value: '',
          filterable: true
        },
        dept_id: {
          type: 'select',
          label: '部门',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 135,
          value: '',
          filterable: true
        },
        is_finish: {
          type: 'select',
          label: '状态',
          selectData: [{
            value: '0',
            label: '拟稿'
          },
          {
            value: '1',
            label: '审核中'
          },
          {
            value: '2',
            label: '已完成'
          },
          {
            value: '-1',
            label: '未起草'
          }],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 135,
          value: '',
          filterable: true
        }
      },
      columns: [
        {
          title: '姓名',
          key: 'user_name',
          align: 'center'
        },
        {
          title: '部门',
          key: 'dept_name',
          align: 'center'
        },
        {
          title: '自评',
          key: 'self_evaluate_score',
          align: 'center'
        },
        {
          title: '最终得分',
          key: 'final_score',
          align: 'center'
        },
        {
          title: '状态',
          key: 'is_finish',
          align: 'center',
          minWidth: 80,
          render: (h, params) => {
            let statusStr = ''
            if (params.row.is_finish === '0') statusStr = '拟稿'
            if (params.row.is_finish === '1') statusStr = '审核中'
            if (params.row.is_finish === '2') statusStr = '已完成'
            if (params.row.is_finish === '-1') statusStr = '未起草'
            return h('div', {}, statusStr)
          }
        },
        {
          title: '操作',
          key: '',
          align: 'center',
          minWidth: 80,
          render: (h, params) => {
            return h('div', [
              h('Button', {
                style: {
                  margin: '0 5px'
                },
                props: {
                  icon: 'md-eye',
                  size: 'small',
                  disabled: params.row.is_finish === '-1'
                },
                on: {
                  click: () => {
                    this.handleDetail(params.row)
                  }
                }
              }, '查看')
            ])
          }
        }
      ]
    }
  },
  methods: {
    modalShow (val) {
      if (val) {
        this.listQuery.belong_month = this.modalData.data.belong_month
        this.listQuery.is_finish = this.modalData.data.is_finish
        this.listQuery.dept_id = this.modalData.data.dept_id
        this.listQuery.dept_ids = this.modalData.data.dept_ids
        this.listQuery.is_personal = this.modalData.data.is_personal
        this.searchData.is_finish.selected = this.modalData.data.is_finish
        this.getAccountList()
        this.getDeptList()
        this.getList()
      } else {
        this.listCurrent = 1
        this.listQuery = {
          pageSize: 10,
          pageIndex: 1,
          belong_month: '',
          dept_id: '',
          unified_account_id: '',
          is_finish: '',
          dept_ids: '',
          is_personal: ''
        }
        this.searchData.dept_id.selected = ''
        this.searchData.is_finish.selected = ''
        this.searchData.unified_account_id.selected = ''
      }
    },
    // 获取列表
    getList () {
      this.listLoading = true
      querySysPerformancePage(this.listQuery).then(res => {
        if (res.data.Code === 10000) {
          this.listLoading = false
          this.list = res.data.Result
          this.total = res.data.Total
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 获取成员列表数据
    getAccountList () {
      queryUnifiedAccountList().then(res => {
        if (res.data.Code === 10000) {
          this.searchData.unified_account_id.selectData = res.data.Result.map(item => {
            return {
              value: item.unified_account_id,
              label: item.user_name
            }
          })
        }
      })
    },
    // 获取部门列表数据
    getDeptList () {
      queryDepartManageList().then(res => {
        if (res.data.Code === 10000) {
          this.searchData.dept_id.selectData = res.data.Result.map(item => {
            return {
              value: item.dept_id,
              label: item.dept_name
            }
          })
        }
      })
    },
    handleDetail (item) {
      localStorage.setItem('newVersion', (item.form_json.newVersion ? item.form_json.newVersion : false))
      localStorage.setItem('formType', item.form_json.formType)
      this.$router.push({
        name: 'performanceDetail',
        params: {
          id: 'detail&id=' + item.form_id
        }
      })
    },
    // 查询
    searchResults (e) {
      this.listCurrent = 1
      this.listQuery.pageIndex = 1
      this.listQuery.dept_id = e.dept_id
      this.listQuery.is_finish = e.is_finish
      this.listQuery.unified_account_id = e.unified_account_id
      this.getList()
    },
    // 重置
    resetResults () {
      this.listCurrent = 1
      this.listQuery = {
        pageSize: 10,
        pageIndex: 1,
        belong_month: this.modalData.data.belong_month,
        dept_id: '',
        unified_account_id: '',
        is_finish: this.modalData.data.is_finish,
        dept_ids: this.modalData.data.dept_ids,
        is_personal: this.modalData.data.is_personal
      }
      this.searchData.dept_id.selected = ''
      this.searchData.is_finish.selected = this.modalData.data.is_finish
      this.searchData.unified_account_id.selected = ''
      this.getList()
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    }
  }
}
</script>
<style lang="less" scoped>
.demo-drawer-footer {
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  background: #fff;
}
</style>

<template>
  <Card>
    <Tabs type="card" v-model="tabId" @on-click="tabChange">
      <TabPane label="待办">
        <search @searchResults='searchResults' :setSearch='remainSearchData'  @selectOnChanged='selectOnChanged' @resetResults='resetResults'></search>
        <Table :loading="tableLoading" :data="dataList" :columns="curColumns"></Table>
        <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="listQuery.pageSize" :current.sync="listQuery.pageIndex"
        :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
      </TabPane>
      <TabPane label="已办">
        <search @searchResults='searchResults' :setSearch='doneSearchData'  @selectOnChanged='selectOnChanged' @resetResults='resetResults'></search>
        <Table :loading="tableLoading" :data="dataList" :columns="doneColumns"></Table>
        <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="listQuery.pageSize" :current.sync="listQuery.pageIndex"
        :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
      </TabPane>
      <Button type="primary" size="small" slot="extra" @click="addPerformance">新增</Button>
    </Tabs>
  </Card>
</template>
<script>
import search from '_c/search' // 查询组件
import API from '@/api/performance'

export default ({
  components: {
    search
  },
  data () {
    return {
      tabId: 0,
      total: 0,
      tableLoading: false,
      next_unified_account_id: '', // 下一个节点id
      listQuery: {
        pageSize: 10,
        pageIndex: 1,
        node_state: 0, // 处理状态（0待办；1已办）
        current_unified_account_id: JSON.parse(localStorage.getItem('userData')).unified_account_id,
        belong_month: '',
        is_finish: '', // 表单状态（0拟稿，1审核中，2完结）
        is_draft: 1 // 是否为起草人列表（1仅查询起草人相关数据；0查询审批相关数据）
      },
      remainSearchData: {
        date: {
          type: 'month',
          label: '绩效月份',
          selected: '',
          width: 135,
          value: '',
          isdisable: false
        }
      },
      doneSearchData: {
        date: {
          type: 'month',
          label: '绩效月份',
          selected: '',
          width: 135,
          value: '',
          isdisable: false
        },
        status: {
          type: 'select',
          label: '状态',
          selectData: [{
            value: 1,
            label: '审核中'
          },
          {
            value: 2,
            label: '已完成'
          }],
          selected: '',
          placeholder: '请选择',
          change: this.statusChange,
          selectName: '',
          width: 135,
          value: '',
          filterable: true
        }
      },
      curColumns: [
        {
          title: '绩效月份',
          key: 'belong_month',
          align: 'center',
          minWidth: 80
        },
        {
          title: '自评',
          key: 'self_evaluate_score',
          align: 'center',
          minWidth: 80
        },
        {
          title: '考核成绩',
          key: 'final_score',
          align: 'center',
          minWidth: 80
        },
        {
          title: '操作',
          key: 'date',
          align: 'center',
          minWidth: 80,
          render: (h, params) => {
            return h('div', [
              h('Button', {
                style: {
                  margin: '0 5px'
                },
                props: {
                  icon: 'md-brush',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleModify(params.row)
                  }
                }
              }, '修改'),
              h('Button', {
                style: {
                  margin: '0 5px'
                },
                props: {
                  icon: 'md-cloud-upload',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleSubmit(params.row)
                  }
                }
              }, '提交')
            ])
          }
        }
      ],
      doneColumns: [
        {
          title: '绩效月份',
          key: 'belong_month',
          align: 'center',
          minWidth: 80
        },
        {
          title: '自评',
          key: 'self_evaluate_score',
          align: 'center',
          minWidth: 80
        },
        {
          title: '考核成绩',
          key: 'final_score',
          align: 'center',
          minWidth: 80,
          render: (h, params) => {
            let _score = 0
            if (params.row.is_finish === '2') {
              _score = params.row.final_score
            }
            return h('div', {}, _score)
          }
        },
        {
          title: '状态',
          key: 'node_state',
          align: 'center',
          minWidth: 80,
          render: (h, params) => {
            let statusStr = ''
            if (params.row.is_finish === '0') statusStr = '拟稿'
            if (params.row.is_finish === '1') statusStr = '审核中'
            if (params.row.is_finish === '2') statusStr = '已完成'
            return h('div', {}, statusStr)
          }
        },
        {
          title: '操作',
          key: 'date',
          align: 'center',
          minWidth: 80,
          render: (h, params) => {
            return h('div', [
              h('Button', {
                style: {
                  margin: '0 5px'
                },
                props: {
                  icon: 'md-eye',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleDetail(params.row)
                  }
                }
              }, '查看')
            ])
          }
        }
      ],
      dataList: [] // 列表
    }
  },
  methods: {
    // 获取列表数据
    getList () {
      this.tableLoading = true
      API.queryPerformancePage(this.listQuery).then(res => {
        this.tableLoading = false
        if (res.data.Code === 10000) {
          this.dataList = res.data.Result
          this.total = res.data.Total
        }
      })
    },
    // 新增绩效详情
    addPerformance () {
      localStorage.setItem('newVersion', true)
      this.$router.push({
        name: 'performanceDetail',
        params: {
          id: 'add'
        }
      })
    },
    handleModify (item) { // 待办事项修改
      localStorage.setItem('newVersion', (item.form_json.newVersion ? item.form_json.newVersion : false))
      this.$router.push({
        name: 'performanceDetail',
        params: {
          id: 'modify&id=' + item.form_id
        }
      })
    },
    handleSubmit (item) { // 待办事项提交
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确认要提交该流程？</p>',
        loading: true,
        onOk: () => {
          this.resetJsonData(item.form_json)
          API.submitPerfForm({
            form_id: item.form_id,
            draft_unified_account_id: JSON.parse(localStorage.getItem('userData')).unified_account_id,
            current_unified_account_id: JSON.parse(localStorage.getItem('userData')).unified_account_id,
            next_unified_account_id: this.next_unified_account_id,
            dept_flow_id: item.dept_flow_id,
            belong_month: item.belong_month,
            month_bonus: item.month_bonus ? parseFloat(item.month_bonus) : 0,
            extra_bonus: item.extra_bonus ? parseFloat(item.extra_bonus) : 0,
            extra_bonus_bak: item.extra_bonus_bak ? item.extra_bonus_bak : '',
            execute_code: 1,
            form_json: JSON.stringify(item.form_json)
          }).then(res => {
            if (res.data.Code === 10000) {
              this.loading = false
              this.$Modal.remove()
              this.getList()
            } else {
              this.$Message.warning(res.data.Message)
            }
          })
        }
      })
    },
    // 提交数据调整上级分数与自评分数一致
    resetJsonData (json) {
      json.curCommonList.map(item => {
        item.supRate = item.selfRate
        item.supQuality = item.selfQuality
      })
      json.curKeyList.map(item => {
        item.supRate = item.selfRate
        item.supQuality = item.selfQuality
      })
      json.examObj.planExamList.map(item => {
        item.supScore = item.selfScore
      })
      json.examObj.curExamList.map(item => {
        item.supScore = item.selfScore
      })
    },
    // 查看详情
    handleDetail (item) {
      localStorage.setItem('newVersion', (item.form_json.newVersion ? item.form_json.newVersion : false))
      this.$router.push({
        name: 'performanceDetail',
        params: {
          id: 'detail&id=' + item.form_id
        }
      })
    },
    tabChange () {
      this.listQuery = {
        pageSize: 10,
        pageIndex: 1,
        node_state: this.tabId, // 处理状态（0待办；1已办）
        current_unified_account_id: JSON.parse(localStorage.getItem('userData')).unified_account_id,
        belong_month: '',
        is_finish: '', // 表单状态（0拟稿，1审核中，2完结）
        is_draft: 1 // 是否为起草人列表（1仅查询起草人相关数据；0查询审批相关数据）
      }
      this.dataList = []
      this.getList()
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    },
    selectOnChanged (e) {
      if (e.flag === 'month_start') {
        this.listQuery.belong_month = e.key
      }
    },
    statusChange (e) {
      this.listQuery.is_finish = e.selected
    },
    // 查询
    searchResults (e) {
      this.getList()
    },
    // 重置
    resetResults () {
      this.listQuery.belong_month = ''
      this.listQuery.is_finish = ''
      this.remainSearchData.date.selected = ''
      this.doneSearchData.date.selected = ''
      this.doneSearchData.status.selected = ''
      this.getList()
    }
  },
  created () {
    let _sysUser = JSON.parse(localStorage.getItem('userData')).user_name
    let _flowObj = JSON.parse(localStorage.getItem('userFlow'))
    if (_flowObj.flowList && _flowObj.flowList.length > 0) {
      let _sysIdx = _flowObj.flowList.findIndex(item => item.user_name === _sysUser)
      if (_sysIdx < (_flowObj.flowList.length - 1)) {
        this.next_unified_account_id = _flowObj.flowList[_sysIdx + 1].unified_account_id
      } else {
        this.next_unified_account_id = ''
      }
    }
    this.getList()
  }
})
</script>

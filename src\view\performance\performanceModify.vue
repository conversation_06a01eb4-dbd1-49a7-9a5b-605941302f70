<template>
  <div>
    <TEMP1 ref="temp1" v-if="(formType === '0' && !newVersion)"></TEMP1>
    <TEMP2 ref="temp2" v-if="formType === '1' && !newVersion"></TEMP2>
    <TEMP3 ref="temp3" v-if="formType === '2' && !newVersion"></TEMP3>
    <TEMP4 ref="temp4" v-if="formType === '0' && newVersion"></TEMP4>
    <TEMP5 ref="temp5" v-if="formType === '1' && newVersion"></TEMP5>
    <TEMP6 ref="temp6" v-if="formType === '2' && newVersion"></TEMP6>
    <TEMP7 ref="temp7" v-if="formType === '3'"></TEMP7>
    <TEMP8 ref="temp8" v-if="formType === '4'"></TEMP8>
  </div>
</template>
<script>
import TEMP1 from './performanceTemp/temp1.vue' // 通用模板
import TEMP2 from './performanceTemp/temp2.vue' // 业务模板
import TEMP3 from './performanceTemp/temp3.vue' // 市场部模板
import TEMP4 from './performanceTemp/temp4.vue' // 通用模板（版本2)
import TEMP5 from './performanceTemp/temp5.vue' // 业务模板(版本2)
import TEMP6 from './performanceTemp/temp6.vue' // 市场部模板(版本2)
import TEMP7 from './performanceTemp/temp7.vue' // 财务部模板
import TEMP8 from './performanceTemp/temp8.vue' // 审计部模板


export default ({
  components: {
    TEMP1,
    TEMP2,
    TEMP3,
    TEMP4,
    TEMP5,
    TEMP6,
    TEMP7,
    TEMP8
  },
  computed: {
    newVersion () {
      let version = !!((localStorage.getItem('newVersion') && localStorage.getItem('newVersion') === 'true'))
      return version
    }
  },
  data () {
    return {
      formType: '',
      type: '' // 编辑类型
    }
  },
  methods: {

  },
  created () {
    if (localStorage.getItem('formType')) { // 绩效档案存储模板类型
      this.formType = localStorage.getItem('formType')
    } else {
      let _flowObj = JSON.parse(localStorage.getItem('userFlow'))
      this.formType = _flowObj.form_num
    }
  },
  beforeDestroy () {
    // 移除便于判断是从绩效档案还是成员操作进来的formType传参
    localStorage.removeItem('formType')
  }
})
</script>

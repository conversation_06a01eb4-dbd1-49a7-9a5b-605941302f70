<template>
  <div>
    <Upload
      action=""
      :show-upload-list="false"
      :before-upload="handleFileUpload"
      style="margin-bottom:5px;"
      v-if="type !== 'approve' && type !== 'detail'">
      <Button v-if="type !== 'modalType'" icon="ios-cloud-upload-outline">上传文件</Button>
      <div v-else class="upload-cent">
        <Button type="primary" style="margin-right: 6px;">上传附件</Button>
        <span>支持上传 jpg、 jpeg、 png、 gif、 pdf、 doc、 docx、 xls、 xlsx、 zip、 rar、 eml<br>格式的文件，单个文件大小不能超过15MB</span>
      </div>
    </Upload>
    <ul class="file-list upload-list">
      <li v-for="(item, index) in fileDataList" :key="index">
        <a :href="item.download_url" target="_blank">{{ item.name }}</a>
        <Icon v-if="type !== 'approve' && type !== 'detail'" size="18" type="ios-trash-outline" @click="handleDelete(item.id, index)" />
        <Icon type="ios-eye-outline" size="18" @click="handleEye(item)" />
      </li>
    </ul>
  </div>
</template>

<script>
import { fileUpload, deleteFile } from '@/api/basicData'
export default {
  props: {
    type: String,
    fileDataList: Array
  },
  data () {
    return {
      fileType: '',
      formDataList: []
    }
  },
  methods: {
    // 删除附件
    handleDelete (d, idx) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确认删除该附件？</p>',
        loading: true,
        onOk: () => {
          deleteFile({ id: d }).then(e => {
            if (e.data.Code === 10000) {
              this.loading = false
              this.$Modal.remove()
              this.$Message.success(e.data.Message)
              this.fileDataList.splice(idx, 1)
              this.formDataList.splice(idx, 1)
            } else {
              this.loading = false
              this.$Modal.remove()
              this.$Message.error(e.data.Message)
            }
          })
        }
      })
    },
    // 预览
    handleEye (e) {
      let imgType = ['bmp','jpg','jpeg','png','tif','gif','pcx','tga','exif','fpx','svg','psd','cdr','pcd','dxf','ufo','eps','ai','raw','WMF','webp','avif','apng']
      if (imgType.includes(e.file_type)) {
        this.fileType = 'img'
        window.open(e.download_url, '_blank')
      } else {
        this.fileType = 'file'
        window.open(e.wps_url, '_blank')
      }
    },
    handleFileUpload (file) {
      this.fileDataList.push({
        id: '',
        name: file.name,
        download_url: URL.createObjectURL(file)
      })
      this.formDataList.push(file)
      let formData = new FormData()
      let through = false // 判断是否需要上传文件
      this.formDataList = this.formDataList.filter(item => {
        if (item.type !== undefined) {
          through = true
          formData.append('file', item)
        }
        return item.type === undefined
      })
      if (through) {
        fileUpload(formData).then(e => {
          if (e.data.Code === 10000) {
            this.fileDataList[this.fileDataList.length - 1].id = e.data.wps_id
            this.fileDataList[this.fileDataList.length - 1].wps_url = e.data.wps_url
            this.fileDataList[this.fileDataList.length - 1].file_type = e.data.file_type
            this.$emit('getFileId', e.data.wps_id)
            this.$Message.success('附件上传成功！')
          } else {
            this.$Message.error(e.data.Message)
          }
        })
      }
      return false
    }
  }
}
</script>

<style lang="less" scoped>
.file-list{
  li{
    list-style-type: none;
    padding: 5px 28px 5px 5px;
    position: relative;
    span{
      color: #2D8cF0;
      cursor: pointer;
    }
    i{
      margin-right: 4px;
    }
    .ivu-icon-ios-close{
      cursor: pointer;
      display: none;
      font-size: 20px;
      position: absolute;
      right: 5px;
      top: 4px;
      margin: 0;
    }
    &:hover{
      background: #f0f0f0;
      .ivu-icon-ios-close{
        display: inline-block;
      }
    }
  }
}
.imgShowClose{
  font-size: 30px;
  position: absolute;
  right: -15px;
  top: -15px;
  cursor: pointer;
}
.upload-list li > i {
  margin-left: 5px;
  cursor: pointer;
}
.upload-cent span {
  vertical-align: top;
  display: inline-block;
}
</style>

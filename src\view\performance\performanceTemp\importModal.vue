<template>
  <Modal v-model="modalData.modal" :title="modalData.title" width="625" @on-visible-change="modalShowHide" :mask-closable="false">
    <search @searchResults='searchResults' :setSearch='doneSearchData' @selectOnChanged='selectOnChanged' @resetResults='resetResults'></search>
    <Table :loading="tableLoading" :data="dataList" :columns="doneColumns" highlight-row @on-row-click="tableClick" @on-row-dblclick="tableDbClick"></Table>
    <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="listQuery.pageSize" :current.sync="listQuery.pageIndex"
    :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
    <div slot="footer">
      <Button style="margin-right: 8px" @click="closeModal">取消</Button>
      <Button type="primary" @click="importHandle">导入</Button>
    </div>
  </Modal>
</template>
<script>
import search from '_c/search' // 查询组件
import API from '@/api/performance'

export default ({
  props: {
    modalData: Object
  },
  components: {
    search
  },
  data () {
    return {
      commitObj: {},
      total: 0,
      tableLoading: false,
      listQuery: {
        pageSize: 10,
        pageIndex: 1,
        node_state: 1, // 处理状态（0待办；1已办）
        current_unified_account_id: JSON.parse(localStorage.getItem('userData')).unified_account_id,
        belong_month: '',
        is_finish: '', // 表单状态（0拟稿，1审核中，2完结）
        is_draft: 1 // 是否为起草人列表（1仅查询起草人相关数据；0查询审批相关数据）
      },
      doneSearchData: {
        date: {
          type: 'month',
          label: '绩效月份',
          selected: '',
          width: 135,
          value: '',
          isdisable: false
        },
        status: {
          type: 'select',
          label: '状态',
          selectData: [{
            value: 1,
            label: '审核中'
          },
          {
            value: 2,
            label: '已完成'
          }],
          selected: '',
          placeholder: '请选择',
          change: this.statusChange,
          selectName: '',
          width: 135,
          value: '',
          filterable: true
        }
      },
      doneColumns: [
        {
          title: '绩效月份',
          key: 'belong_month',
          align: 'center',
          minWidth: 80
        },
        {
          title: '自评成绩',
          key: 'self_evaluate_score',
          align: 'center',
          minWidth: 80
        },
        {
          title: '复评成绩',
          key: 're_evaluate_score',
          align: 'center',
          minWidth: 80
        },
        {
          title: '最终成绩',
          key: 'final_score',
          align: 'center',
          minWidth: 80
        }
      ],
      dataList: [] // 列表
    }
  },
  methods: {
    getList () {
      this.tableLoading = true
      API.queryPerformancePage(this.listQuery).then(res => {
        this.tableLoading = false
        if (res.data.Code === 10000) {
          this.dataList = res.data.Result
          this.total = res.data.Total
        } else {
          this.$Message.error(resd.data.Message)
        }
      })
    },
    // 表格选中
    tableClick (list) {
      this.commitObj = list
    },
    // 表格双击
    tableDbClick (list) {
      this.commitObj = list
      this.importHandle()
    },
    importHandle () {
      this.$emit('ImportBack', this.commitObj)
      this.closeModal()
    },
    closeModal () {
      this.modalData.modal = false
    },
    modalShowHide (val) {
      if (val) {
        this.getList()
      } else {
        this.closeModal()
      }
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    },
    selectOnChanged (e) {
      if (e.flag === 'month_start') {
        this.listQuery.belong_month = e.key
      }
    },
    statusChange (e) {
      this.listQuery.is_finish = e.selected
    },
    // 查询
    searchResults (e) {
      this.getList()
    },
    // 重置
    resetResults () {
      this.listQuery.belong_month = ''
      this.listQuery.is_finish = ''
      this.remainSearchData.date.selected = ''
      this.doneSearchData.date.selected = ''
      this.doneSearchData.status.selected = ''
      this.getList()
    }
  }
})
</script>

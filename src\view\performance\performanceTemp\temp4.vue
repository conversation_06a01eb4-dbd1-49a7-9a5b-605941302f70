<template>
  <div>
    <div class="picker-area">
      <div class="picker-list">
        <span>绩效月份：</span>
        <DatePicker type="month" v-if="type !== 'detail' && type !=='approve'" placeholder="请选择月份" style="width: 100px" format="yyyy-MM" :value="belong_month" @on-change="monthChange"></DatePicker>
        <span v-else style="font-weight: 100;">{{ belong_month }}</span>
      </div>
      <div class="picker-list">
        <span>模板：</span>
        {{ formTempName }}
      </div>
      <div class="back_btn" v-if="type === 'detail'">
        <Button @click="handleCancel">返回</Button>
      </div>
      <div class="back_btn" v-if="type === 'add' || type === 'modify'">
        <Button type="primary" @click="handleInport">导入</Button>
      </div>
    </div>
    <div class="approve-top">
      <div v-for="(item, idx) in flowObj.flowList" :key="item.unified_account_id">
        <span v-if="flowObj.flowList.length > 1" class="approve-top-pre" :class="flowColor(idx)">{{ idx > 0 ? '审' : '自'}}</span>
        <span class="approve-top-name">{{ item.user_name }}</span>
        <Icon v-if="idx < (flowObj.flowList.length - 1)" class="approve-top-arrow" type="md-arrow-forward" color="#797979" size="20" />
      </div>
    </div>
    <div class="approve-steps">
      <!-- <Button v-if="type === 'add' || type === 'modify' || type === 'approve'" type="primary" icon="md-cloud-upload" style="position: absolute; right: -12px; top: -62px;" @click="handleJustSave">保存</Button> -->
      <Tooltip max-width="200" v-for="(item, idx) in flowHisList" :key="item.flow_history_id" placement="left" theme="light">
        <div class="step-list" :class="idx === (flowHisList.length - 1) ? 'step-cur' : ''">{{ item.user_name.substring(0, 1) }}</div>
        <div slot="content">
          <span v-if="item.execute_code !== '1'">
            <span style="color: #57a3f3;">{{ item.user_name }}：</span>
            <span>{{ item.insert_time }}</span>
            <span style="margin-left: 5px; color: #57a3f3;">{{ item.execute_name }}</span>
            <span style="margin-left: 5px; color: red;">{{ item.execute_bak }}</span>
          </span>
          <span v-else>
            <span style="color: #57a3f3;">{{ item.user_name }}：</span>
            <span>{{ item.insert_time }}</span>
            <span style="margin-left: 5px; color: #57a3f3;">{{ item.execute_name }}</span>
          </span>
        </div>
      </Tooltip>
    </div>
    <h3>
      一、本月个人关键业绩指标KPI完成情况（分值合计85分）
      <Tooltip max-width="500">
        <Icon type="ios-alert" color="red" size="16"/>
        <div slot="content" v-html="topExplainTxt"></div>
      </Tooltip>
      <span style="margin-left: 15px; color: red; font-size: 16px;">(注：ctrl + s可保存草稿)</span>
    </h3>
    <div class="table_scroll" :style="(type === 'detail' || type === 'approve') ? 'width: calc(100% - 40px)' : 'width: calc(100% - 60px)'">
      <table class="table table-bordered" style="width: 1800px;">
        <!-- 表头开始 -->
        <tr>
          <th rowspan="2" style="width: 50px;">序号</th>
          <th rowspan="2" style="width: 130px;">工作属性</th>
          <th rowspan="2" style="min-width: 200px;">主要工作事项</th>
          <th rowspan="2" style="min-width: 200px;">工作目标与要求</th>
          <th rowspan="2" style="min-width: 200px;">交付成果或完成情况描述</th>
          <th rowspan="2" style="width: 80px;">配分(85分)</th>
          <th colspan="2">完成率(60%)</th>
          <th colspan="2">质量评价(40%)</th>
          <th colspan="2">单项得分</th>
          <th rowspan="2">备注</th>
        </tr>
        <tr>
          <th style="width: 70px;">自评</th>
          <th style="width: 70px;">上级</th>
          <th style="width: 70px;">自评</th>
          <th style="width: 70px;">上级</th>
          <th style="width: 60px;">自评</th>
          <th style="width: 60px;">上级</th>
        </tr>
        <!-- 表头结束 -->

        <!-- 常规性工作开始 -->
        <tr v-for="(item, idx) in curKeyList" :key="'key' + idx">
          <td class="center">{{ idx + 1 }}</td>
          <td v-if="idx === 0" :rowspan="curKeyList.length" class="center">常规性工作</td>
          <td>
            <Input type="textarea" :autosize="{minRows: 1,maxRows: 5}" v-if="type === 'add' || type === 'modify'" class="list-input" v-model="item.content" />
            <span v-else>{{ item.content }}</span>
          </td>
          <td>
            <Input type="textarea" :autosize="{minRows: 1,maxRows: 5}" v-if="type === 'add' || type === 'modify'" class="list-input" v-model="item.target" />
            <span v-else>{{ item.target }}</span>
          </td>
          <td>
            <Input type="textarea" :autosize="{minRows: 1,maxRows: 5}" v-if="type === 'add' || type === 'modify'" class="list-input" v-model="item.result" />
            <span v-else>{{ item.result }}</span>
          </td>
          <td class="center">
            <InputNumber style="width: 50px; height: 26px;" v-if="type === 'add' || type === 'modify'" class="list-input" :max="100" :min="0" size="small" v-model="item.score"></InputNumber>
            <span v-else>{{ item.score }}</span>
          </td>
          <td class="center">
            <InputNumber style="width: 50px; height: 26px;" v-if="type === 'add' || type === 'modify'" class="list-input" :max="100000" :min="0" size="small" v-model="item.selfRate"></InputNumber>
            <span v-else>{{ item.selfRate }}</span>
          </td>
          <td class="center">
            <InputNumber style="width: 50px; height: 26px;" v-if="type === 'approve'" class="list-input" :max="100000" :min="0" size="small" v-model="item.supRate"></InputNumber>
            <span v-else>{{ item.supRate }}</span>
          </td>
          <td class="center">
            <InputNumber style="width: 50px; height: 26px;" v-if="type === 'add' || type === 'modify'" class="list-input" :max="100" :min="0" size="small" v-model="item.selfQuality"></InputNumber>
            <span v-else>{{ item.selfQuality }}</span>
          </td>
          <td class="center">
            <InputNumber style="width: 50px; height: 26px;" v-if="type === 'approve'" class="list-input" :max="100" :min="0" size="small" v-model="item.supQuality"></InputNumber>
            <span v-else>{{ item.supQuality }}</span>
          </td>
          <td class="center">
            <span>{{ selfItemScore(item) }}</span>
          </td>
          <td class="center">
            <span>{{ supItemScore(item) }}</span>
          </td>
          <td class="center">
            <Input style="min-width: 100px;" type="textarea" :autosize="{minRows: 1,maxRows: 5}" v-if="type === 'add' || type === 'modify' || type === 'approve'" class="list-input" v-model="item.remark" />
            <span style="min-width: 100px;" v-else>{{ item.remark }}</span>
          </td>
          <Button class="temp1_add_btn" size="small" type="primary" v-if="idx === (curKeyList.length - 1) && type !== 'detail' && type !== 'approve'" @click="addKeyList">+</Button>
          <Button class="remove_btn" size="small" type="error" v-if="curKeyList.length > 1 && type !== 'detail' && type !== 'approve'" @click="removeKeyList(idx)">-</Button>
        </tr>
        <!-- 常规性工作结束 -->

        <!-- 阶段性工作开始 -->
        <tr v-for="(item, idx) in curCommonList" :key="'common' + idx">
          <td class="center">{{ curKeyList.length + idx + 1 }}</td>
          <td v-if="idx === 0" :rowspan="curCommonList.length" class="center">阶段性工作</td>
          <td>
            <Input type="textarea" :autosize="{minRows: 1,maxRows: 5}" v-if="type === 'add' || type === 'modify'" class="list-input" v-model="item.content" />
            <span v-else>{{ item.content }}</span>
          </td>
          <td>
            <Input type="textarea" :autosize="{minRows: 1,maxRows: 5}" v-if="type === 'add' || type === 'modify'" class="list-input" v-model="item.target" />
            <span v-else>{{ item.target }}</span>
          </td>
          <td>
            <Input type="textarea" :autosize="{minRows: 1,maxRows: 5}" v-if="type === 'add' || type === 'modify'" class="list-input" v-model="item.result" />
            <span v-else>{{ item.result }}</span>
          </td>
          <td class="center">
            <InputNumber style="width: 50px; height: 26px;" v-if="type === 'add' || type === 'modify'" class="list-input" :max="100" :min="0" size="small" v-model="item.score"></InputNumber>
            <span v-else>{{ item.score }}</span>
          </td>
          <td class="center">
            <InputNumber style="width: 50px; height: 26px;" v-if="type === 'add' || type === 'modify'" class="list-input" :max="100000" :min="0" size="small" v-model="item.selfRate"></InputNumber>
            <span v-else>{{ item.selfRate }}</span>
          </td>
          <td class="center">
            <InputNumber style="width: 50px; height: 26px;" v-if="type === 'approve'" class="list-input" :max="100000" :min="0" size="small" v-model="item.supRate"></InputNumber>
            <span v-else>{{ item.supRate }}</span>
          </td>
          <td class="center">
            <InputNumber style="width: 50px; height: 26px;" v-if="type === 'add' || type === 'modify'" class="list-input" :max="100" :min="0" size="small" v-model="item.selfQuality"></InputNumber>
            <span v-else>{{ item.selfQuality }}</span>
          </td>
          <td class="center">
            <InputNumber style="width: 50px; height: 26px;" v-if="type === 'approve'" class="list-input" :max="100" :min="0" size="small" v-model="item.supQuality"></InputNumber>
            <span v-else>{{ item.supQuality }}</span>
          </td>
          <td class="center">
            <span>{{ selfItemScore(item) }}</span>
          </td>
          <td class="center">
            <span>{{ supItemScore(item) }}</span>
          </td>
          <td class="center">
            <Input style="min-width: 100px;" type="textarea" :autosize="{minRows: 1,maxRows: 5}" v-if="type === 'add' || type === 'modify' || type === 'approve'" class="list-input" v-model="item.remark" />
            <span style="min-width: 100px;" v-else>{{ item.remark }}</span>
          </td>
          <Button class="temp1_add_btn" size="small" type="primary" v-if="idx === (curCommonList.length - 1) && type !== 'detail' && type !== 'approve'" @click="addCommonList">+</Button>
          <Button class="remove_btn" size="small" type="error" v-if="curCommonList.length > 1 && type !== 'detail' && type !== 'approve'" @click="removeCommonList(idx)">-</Button>
        </tr>
        <!-- 阶段性工作结束 -->
        <!-- 合计开始 -->
        <tr>
          <td class="center">{{ curKeyList.length + curCommonList.length + 1 }}</td>
          <td class="center">小计</td>
          <td></td>
          <td></td>
          <td class="center">合计</td>
          <td class="center">{{ selfTotalScore() }}</td>
          <td class="center">{{ selfTotalRate() }}</td>
          <td class="center">{{ supTotalRate() }}</td>
          <td class="center">{{ selfTotalQuality() }}</td>
          <td class="center">{{ supTotalQuality() }}</td>
          <td class="center">{{ selfTotalScoreT() }}</td>
          <td class="center">{{ supTotalScoreT() }}</td>
          <td></td>
        </tr>
        <tr>
          <td class="center">{{ curKeyList.length + curCommonList.length + 2 }}</td>
          <td style="width: 135px;">上月计划调整说明</td>
          <td colspan="11">
            <Input type="textarea" :autosize="{minRows: 1,maxRows: 5}" v-if="type === 'add' || type === 'modify'" class="list-input" v-model="summaryObj.preMonthDesc" />
            <span v-else>{{ summaryObj.preMonthDesc }}</span>
          </td>
        </tr>
        <!-- <tr>
          <td colspan="13" class="center"><h3>二、加减分项目（非必填项）</h3></td>
        </tr> -->
        <tr v-for="(item, idx) in extraArr" :key="'extra'+idx">
          <td v-if="idx === 0" colspan="2" :rowspan="extraArr.length + reduceArr.length">
            加减分项目（非必填项）
            <Tooltip max-width="500" placement="right">
              <Icon type="md-help-circle" size="14"/>
              <div slot="content" v-html="addRemoveTxt"></div>
            </Tooltip>
          </td>
          <td v-if="idx === 0" class="center" :rowspan="extraArr.length">加分项目</td>
          <td colspan="7">
            <Input type="textarea" :autosize="{minRows: 1,maxRows: 5}" v-if="type === 'add' || type === 'modify' || type === 'approve'" class="list-input" v-model="item.content" />
            <span v-else>{{ item.content }}</span>
          </td>
          <td>
            <InputNumber :step="0.5" style="width: 50px; height: 26px;" v-if="type === 'add' || type === 'modify'" class="list-input" :max="100" :min="0" size="small" v-model="item.selfScore"></InputNumber>
            <span v-else>{{ item.selfScore }}</span>
          </td>
          <td>
            <InputNumber :step="0.5" style="width: 50px; height: 26px;" v-if="type === 'approve'" class="list-input" :max="100" :min="0" size="small" v-model="item.supScore"></InputNumber>
            <span v-else>{{ item.supScore }}</span>
          </td>
          <td>
            <Input style="min-width: 100px;" type="textarea" :autosize="{minRows: 1,maxRows: 5}" v-if="type === 'approve'" class="list-input" v-model="item.remark" />
            <span style="min-width: 100px;" v-else>{{ item.remark }}</span>
          </td>
        </tr>
        <tr v-for="(item, idx) in reduceArr" :key="'reduce' + idx">
          <td v-if="idx === 0" class="center" :rowspan="reduceArr.length">减分项目</td>
          <td colspan="7">
            <Input type="textarea" :autosize="{minRows: 1,maxRows: 5}" v-if="type === 'add' || type === 'modify' || type === 'approve'" class="list-input" v-model="item.content" />
            <span v-else>{{ item.content }}</span>
          </td>
          <td>
            <InputNumber :step="0.5" style="width: 50px; height: 26px;" v-if="type === 'add' || type === 'modify'" class="list-input" :max="0" :min="-100" size="small" v-model="item.selfScore"></InputNumber>
            <span v-else>{{ item.selfScore }}</span>
          </td>
          <td>
            <InputNumber :step="0.5" style="width: 50px; height: 26px;" v-if="type === 'approve'" class="list-input" :max="0" :min="-100" size="small" v-model="item.supScore"></InputNumber>
            <span v-else>{{ item.supScore }}</span>
          </td>
          <td>
            <Input style="min-width: 100px;" type="textarea" :autosize="{minRows: 1,maxRows: 5}" v-if="type === 'approve'" class="list-input" v-model="item.remark" />
            <span style="min-width: 100px;" v-else>{{ item.remark }}</span>
          </td>
        </tr>
        <tr>
          <td colspan="9">
            <!-- <span v-html="addRemoveTxt"></span> -->
          </td>
          <td>小计</td>
          <td>{{ selfSubTotal() }}</td>
          <td>{{ supSubTotal() }}</td>
          <td></td>
        </tr>
      </table>
    </div>
    <h3 style="margin-top: 10px;">二、下月工作计划（分值合计5分）</h3>
    <table class="table table-bordered" :style="(type === 'detail' || type === 'approve') ? 'width: calc(100% - 40px)' : 'width: calc(100% - 60px)'">
      <tr>
        <th style="width: 50px;">序号</th>
        <th style="width: 145px;">工作属性</th>
        <th>主要工作事项</th>
        <th>工作目标与要求</th>
        <th style="width: 80px;">配分(85分)</th>
        <th style="width: 120px;">计划起止时间</th>
        <th>备注</th>
      </tr>
      <tr v-for="(item, idx) in planKeyList" :key="'key' + idx">
        <td class="center">{{ idx + 1 }}</td>
        <td v-if="idx === 0" :rowspan="planKeyList.length" class="center">常规性工作</td>
        <td>
          <Input type="textarea" :autosize="{minRows: 1,maxRows: 5}" v-if="type === 'add' || type === 'modify' || type === 'approve'" class="list-input" v-model="item.content" />
          <span v-else>{{ item.content }}</span>
        </td>
        <td>
          <Input type="textarea" :autosize="{minRows: 1,maxRows: 5}" v-if="type === 'add' || type === 'modify' || type === 'approve'" class="list-input" v-model="item.result" />
          <span v-else>{{ item.result }}</span>
        </td>
        <td class="center">
          <InputNumber :step="0.5" style="width: 50px; height: 26px;" v-if="type === 'add' || type === 'modify' || type === 'approve'" class="list-input" :max="100" :min="0" size="small" v-model="item.selfScore"></InputNumber>
          <span v-else>{{ (!item.selfScore || item.selfScore === '') ? 0 : item.selfScore }}</span>
        </td>
        <td class="center">
          <Input v-if="type === 'add' || type === 'modify' || type === 'approve'" class="list-input" v-model="item.time" />
          <span v-else>{{ item.time }}</span>
        </td>
        <td class="center">
          <Input style="min-width: 100px;" type="textarea" :autosize="{minRows: 1,maxRows: 5}" v-if="type === 'add' || type === 'modify' || type === 'approve'" class="list-input" v-model="item.remark" />
          <span style="min-width: 100px;" v-else>{{ item.remark }}</span>
        </td>
        <Button class="remove_btn" size="small" type="error" v-if="planKeyList.length > 1 && type !== 'detail'" @click="removePlanKeyList(idx)">-</Button>
        <Button class="temp1_add_btn" size="small" type="primary" v-if="idx === (planKeyList.length - 1) && type !== 'detail'" @click="addPlanKeyList">+</Button>
      </tr>
      <tr v-for="(item, idx) in planCommonList" :key="'common' + idx">
        <td class="center">{{ curKeyList.length + idx + 1 }}</td>
        <td v-if="idx === 0" :rowspan="planCommonList.length" class="center">阶段性工作</td>
        <td>
          <Input type="textarea" :autosize="{minRows: 1,maxRows: 5}" v-if="type === 'add' || type === 'modify' || type === 'approve'" class="list-input" v-model="item.content" />
          <span v-else>{{ item.content }}</span>
        </td>
        <td>
          <Input type="textarea" :autosize="{minRows: 1,maxRows: 5}" v-if="type === 'add' || type === 'modify' || type === 'approve'" class="list-input" v-model="item.result" />
          <span v-else>{{ item.result }}</span>
        </td>
        <td class="center">
          <InputNumber :step="0.5" style="width: 50px; height: 26px;" v-if="type === 'add' || type === 'modify' || type === 'approve'" class="list-input" :max="100" :min="0" size="small" v-model="item.selfScore"></InputNumber>
          <span v-else>{{ item.selfScore }}</span>
        </td>
        <td class="center">
          <Input v-if="type === 'add' || type === 'modify' || type === 'approve'" class="list-input" v-model="item.time" />
          <span v-else>{{ item.time }}</span>
        </td>
        <td class="center">
          <Input type="textarea" :autosize="{minRows: 1,maxRows: 5}" v-if="type === 'add' || type === 'modify' || type === 'approve'" class="list-input" v-model="item.remark" />
          <span v-else>{{ item.remark }}</span>
        </td>
        <Button class="temp1_add_btn" size="small" type="primary" v-if="idx === (planCommonList.length - 1) && type !== 'detail'" @click="addPlanCommonList">+</Button>
        <Button class="remove_btn" size="small" type="error" v-if="planCommonList.length > 1 && type !== 'detail'" @click="removePlanCommonList(idx)">-</Button>
      </tr>
      <tr>
        <td colspan="3"></td>
        <td class="center">合计</td>
        <td class="center">{{ planWorkTotalScore() }}</td>
        <td colspan="2"></td>
      </tr>
    </table>
    <h3 style="margin-top: 10px;">三、月度绩效考核总体评价</h3>
    <table class="table table-bordered" :style="(type === 'detail' || type === 'approve') ? 'width: calc(100% - 40px)' : 'width: calc(100% - 60px)'">
      <tr>
        <th style="width: 100px;">类别</th>
        <th>内容</th>
        <th style="width: 60px;">配分</th>
        <th style="width: 70px;">自评得分</th>
        <th style="width: 70px;">上级评分</th>
        <th colspan="4">备注</th>
      </tr>
      <tr v-for="(item, idx) in examObj.curExamList" :key="'curExam' + idx">
        <td class="center" v-if="idx === 0">本月KPI</td>
        <td style="width: 220px;" v-if="idx === 0">本月个人关键业绩指标KPI完成情况</td>
        <td class="center" v-if="idx === 1">下月计划</td>
        <td style="width: 220px;" v-if="idx === 1">下月重点事项计划 </td>
        <td class="center" v-if="idx === 2">加减分</td>
        <td style="width: 220px;" v-if="idx === 2">加减分项目</td>
        <td class="center">
          <span v-if="idx === 0">85</span>
          <span v-if="idx === 1">5</span>
          <span v-if="idx === 2">10</span>
        </td>
        <td class="center">
          <InputNumber style="width: 50px; height: 26px;" v-if="idx === 1 && type !== 'detail'" class="list-input" :max="100" :min="0" size="small" v-model="item.selfScore"></InputNumber>
          <span v-else>{{ item.selfScore }}</span>
        </td>
        <td class="center">
          <InputNumber style="width: 50px; height: 26px;" v-if="idx === 1 && type === 'approve' && type !== 'detail'" class="list-input" :max="100" :min="0" size="small" v-model="item.supScore"></InputNumber>
          <span v-else>{{ item.supScore }}</span>
        </td>
        <td colspan="4">
          <Input type="textarea" :autosize="{minRows: 1,maxRows: 5}" v-if="type === 'add' || type === 'modify' || type === 'approve'" class="list-input" v-model="item.remark" />
          <span v-else>{{ item.remark }}</span>
        </td>
      </tr>
      <!-- <tr v-for="(item, idx) in examObj.planExamList" :key="'planExam' + idx">
        <td v-if="idx === 0" :rowspan="examObj.planExamList.length" class="center">{{ item.title }}</td>
        <td>{{ item.content }}</td>
        <td class="center">
          <span>{{ item.score }}</span>
        </td>
        <td class="center">
          <InputNumber style="width: 50px; height: 26px;" v-if="type === 'add' || type === 'modify'" class="list-input" :max="100" :min="0" size="small" v-model="item.selfScore"></InputNumber>
          <span v-else>{{ item.selfScore }}</span>
        </td>
        <td class="center">
          <InputNumber style="width: 50px; height: 26px;" v-if="type === 'approve'" class="list-input" :max="100" :min="0" size="small" v-model="item.supScore"></InputNumber>
          <span v-else>{{ item.supScore }}</span>
        </td>
        <td colspan="4">
          <Input type="textarea" :autosize="{minRows: 1,maxRows: 5}" v-if="type === 'add' || type === 'modify' || type === 'approve'" class="list-input" v-model="item.remark" />
          <span v-else>{{ item.remark }}</span>
        </td>
      </tr> -->
      <tr>
        <td colspan="2" class="center">小计</td>
        <td class="center">100</td>
        <td class="center">{{ monthSelfScore() }}</td>
        <td class="center">{{ monthSupScore() }}</td>
        <td colspan="3"></td>
      </tr>
      <tr>
        <td class="center">分管领导批阅</td>
        <td colspan="7">分管领导根据实际表现对绩效得分进行最终审核，可酌情予以加分或扣分</td>
      </tr>
      <tr>
        <td colspan="1" class="center">加分</td>
        <td style="width: 120px;">
          <InputNumber :step="0.5" style="width: 100px; height: 26px;" v-if="isLastApprove && type !== 'detail'" class="list-input" :max="100" :min="0" size="small" v-model="leaderObj.addScore"></InputNumber>
          <span v-else>{{ leaderObj.addScore }}</span>
        </td>
        <td class="center">减分</td>
        <td>
          <InputNumber :step="0.5" style="width: 80px; height: 26px;" v-if="isLastApprove && type !== 'detail'" class="list-input" :max="0" :min="-100" size="small" v-model="leaderObj.reduceScore"></InputNumber>
          <span v-else>{{ leaderObj.reduceScore }}</span>
        </td>
        <td class="center">批语</td>
        <td>
          <Input type="textarea" :autosize="{minRows: 1,maxRows: 5}" v-if="isLastApprove && type !== 'detail'" class="list-input" v-model="leaderObj.leaderComment" />
          <span v-else>{{ leaderObj.leaderComment }}</span>
        </td>
        <td style="width: 100px;" class="center">最终得分</td>
        <td style="width: 80px;">
          {{ totalScore() }}
        </td>
      </tr>
      <tr>
        <td colspan="8" v-html="explainTxt"></td>
      </tr>
    </table>
    <h3>四、附件</h3>
    <fileUpload ref="fileUploadComponent" :fileDataList="fileDataList" :type="type" @getFileId="getFileId"></fileUpload>
    <div style="height: 60px"></div>
    <div class="btn-area" v-if="type === 'add' || type === 'modify'">
      <Button @click="handleCancel">返回</Button>
      <Button type="primary" @click="handleJustSave">保存</Button>
      <Button type="primary" @click="handleSave">提交</Button>
    </div>
    <div class="btn-area" v-if="type === 'approve'">
      <Button @click="handleCancel">返回</Button>
      <Button @click="handleApprove(-1)">退回修改</Button>
      <Button type="primary" @click="handleApprove(1)">提交</Button>
    </div>
    <ImportModal :modalData="importData" @ImportBack="importBackData"></ImportModal>
  </div>
</template>
<script>
import API from '@/api/performance'
import fileUpload from './fileUpload'
import ImportModal from './importModal'

export default ({
  components: {
    fileUpload,
    ImportModal
  },
  data () {
    return {
      importData: {
        modal: false,
        title: '导入绩效信息'
      },
      wps_ids: '',
      fileDataList: [],
      isLastApprove: false, // 本流程最后一个审批人
      isFirstMember: false, // 本流程第一个提交人
      type: '',
      form_id: '',
      sysUser: '',
      belong_month: '', // 月份
      self_evaluate_score: '', // 自评总分
      re_evaluate_score: '', // 复评部分
      final_score: '', // 最终得分
      formTempName: '通用模板', // 模板名称
      sysIdx: null, // 当前人员基于流程位置
      approveIdx: 0, // 审批流程位置
      isComplete: false, // 流程是否審批完成
      next_unified_account_id: '', // 下一个审批用户
      detailObj: {}, // 详情数据
      leaderObj: { // 领导评分内容
        addScore: 0, // 加分
        reduceScore: 0, // 减分
        leaderComment: '' // 领导评语
      },
      flowObj: [], // 流程人员列表
      flowHisList: [], // 流程流转记录
      addRemoveTxt: '', // 加减分说明内容
      extraArr: [ // 加分项内容
        {
          content: '',
          selfScore: 0,
          supScore: 0,
          remark: ''
        }
      ],
      reduceArr: [ // 减分项内容
        {
          content: '',
          selfScore: 0,
          supScore: 0,
          remark: ''
        }
      ],
      extraObj: { // 加分项内容
        content: '',
        selfScore: 0,
        supScore: 0,
        remark: ''
      },
      reduceObj: { // 减分项内容
        content: '',
        selfScore: 0,
        supScore: 0,
        remark: ''
      },
      summaryObj: { // 本月小结
        preMonthDesc: '', // 上月计划说明
        inproveDesc: '', // 改进事项
        inproveSug: '' // 改进建议
      },
      curKeyList: [
        {
          title: '常规性工作',
          content: '',
          target: '',
          result: '',
          score: 0,
          selfRate: 0,
          supRate: 0,
          selfQuality: 0,
          supQuality: 0,
          selfScore: 0,
          supScore: 0,
          remark: ''
        },
        {
          title: '常规性工作',
          content: '',
          target: '',
          result: '',
          score: 0,
          selfRate: 0,
          supRate: 0,
          selfQuality: 0,
          supQuality: 0,
          selfScore: 0,
          supScore: 0,
          remark: ''
        },
        {
          title: '常规性工作',
          content: '',
          target: '',
          result: '',
          score: 0,
          selfRate: 0,
          supRate: 0,
          selfQuality: 0,
          supQuality: 0,
          selfScore: 0,
          supScore: 0,
          remark: ''
        }
      ],
      curCommonList: [
        {
          title: '阶段性工作',
          content: '',
          target: '',
          result: '',
          score: 0,
          selfRate: 0,
          supRate: 0,
          selfQuality: 0,
          supQuality: 0,
          selfScore: 0,
          supScore: 0,
          remark: ''
        },
        {
          title: '阶段性工作',
          content: '',
          target: '',
          result: '',
          score: 0,
          selfRate: 0,
          supRate: 0,
          selfQuality: 0,
          supQuality: 0,
          selfScore: 0,
          supScore: 0,
          remark: ''
        }
      ],
      planKeyList: [
        {
          title: '常规性工作',
          content: '',
          result: '',
          time: '',
          selfScore: 0,
          remark: ''
        },
        {
          title: '常规性工作',
          content: '',
          result: '',
          time: '',
          selfScore: 0,
          remark: ''
        }
      ],
      planCommonList: [
        {
          title: '阶段性工作',
          content: '',
          result: '',
          time: '',
          selfScore: 0,
          remark: ''
        },
        {
          title: '阶段性工作',
          content: '',
          result: '',
          time: '',
          selfScore: 0,
          remark: ''
        }
      ],
      topExplainTxt: '说明：<br>1、KPI分为定量指标和定性指标，原则上为5-8个；每个指标根据当月工作侧重点，独立配分，配分为5的倍数，最小5分，最大不超过30分，定性指标原则上累计不超过30分；<br>2、定量指标一般为3-5个，依据当月达成率评分，达成率=（实际值/目标值）*100%；<br>3、定性指标一般为1-3个，依据评分标准评分，单指标满分100分。评分标准：A:优秀[91-100]， B:良好[81-90]，C:一般[61-80]， D:较差[0-60]；<br>4、单项得分=配分*（完成率*60%+质量评价*40%)。',
      explainTxt: '说明：1、月度绩效考核表由【个人关键业绩指标】、【加减分项目】、【下月工作计划】构成，其中加减分项目为非必填项。<br>2、绩效考核系数=1+（当月绩效考核得分-85）×0.02。<br>3、当年度累计三个月月度绩效考核系数低于0.7，或者累计两个月月度绩效考核系数低于0.6，公司可视其综合表现，降职降薪。',
      examObj: {
        totalScore: 100,
        curExamList: [
          {
            title: '本月KPI',
            content: '本月个人关键业绩指标KPI完成情况',
            score: 85,
            selfScore: 0,
            supScore: 0,
            remark: ''
          },
          {
            title: '下月计划',
            content: '下月重点事项计划',
            score: 5,
            selfScore: 0,
            supScore: 0,
            remark: ''
          },
          {
            title: '加减分',
            content: '加减分项目',
            score: 10,
            selfScore: 0,
            supScore: 0,
            remark: ''
          }
        ],
        planExamList: [
          {
            title: '下月计划',
            content: '下月重点事项计划',
            score: 5,
            selfScore: 0,
            supScore: 0,
            remark: ''
          },
          {
            title: '下月计划',
            content: '下月一般事项计划',
            score: 3,
            selfScore: 0,
            supScore: 0,
            remark: ''
          }
        ]
      }
    }
  },
  methods: {
    getFormDetail () { // 获取详情
      // 获取表格详情
      API.getPerfFormInfo({ form_id: this.form_id }).then(res => {
        if (res.data.Code === 10000) {
          let _formObj = res.data.Result[0].form_json
          if (res.data.Result[0].is_finish === '2') {
            this.isComplete = true
          }
          this.formTempName = '通用模板'
          this.belong_month = res.data.Result[0].belong_month
          if (JSON.stringify(_formObj) === '{}') return
          this.detailObj = res.data.Result[0]
          this.curKeyList = _formObj.curKeyList
          this.curCommonList = _formObj.curCommonList
          this.extraArr = _formObj.extraArr
          this.reduceArr = _formObj.reduceArr
          this.summaryObj = _formObj.summaryObj
          this.planKeyList = _formObj.planKeyList
          this.planCommonList = _formObj.planCommonList
          this.examObj = _formObj.examObj
          this.fileDataList = this.detailObj.files_array
          if (_formObj.leaderObj) {
            this.leaderObj = _formObj.leaderObj
          }
          this.flowObj.flowList = _formObj.flowList
          this.getAddReduceRemark(_formObj.flowList[0].unified_account_id) // 获取加减分文案
          // 获取流转内容
          API.queryPerfFlowHistoryList({
            form_id: this.form_id
          }).then(res => {
            if (res.data.Code === 10000) {
              this.flowHisList = res.data.Result
              if (this.flowHisList.length > 0) {
                this.approveIdx = this.flowObj.flowList.findIndex(item => item.unified_account_id === this.flowHisList[this.flowHisList.length - 1].unified_account_id)
              }
            }
          })
          if (this.flowObj.flowList && this.flowObj.flowList.length > 0) {
            // this.flowObj.flowList.reverse() // 数组倒序
            this.sysIdx = this.flowObj.flowList.findIndex(item => item.user_name === this.sysUser)
            if (this.sysIdx < (this.flowObj.flowList.length - 1)) {
              this.next_unified_account_id = this.flowObj.flowList[this.sysIdx + 1].unified_account_id
            } else {
              this.next_unified_account_id = ''
            }
            if (_formObj.flowList[_formObj.flowList.length - 1].unified_account_id === JSON.parse(localStorage.getItem('userData')).unified_account_id) {
              this.isLastApprove = true
            } else {
              this.isLastApprove = false
            }
            if (_formObj.flowList[0].unified_account_id === JSON.parse(localStorage.getItem('userData')).unified_account_id) {
              this.isFirstMember = true
            } else {
              this.isFirstMember = false
              if (this.type === 'approve' && (res.data.Result[0].final_score === '0' || res.data.Result[0].final_score === '')) { // 当是审批情况下且最后得分为0  复制自评人分数给复评人
                this.resetJsonData()
              }
            }
          }
        }
      })
    },
    handleInport () { // 导入
      this.importData.modal = true
    },
    getFileId () {
      this.wps_ids = this.fileDataList.map(item => {
        return item.id
      }).join()
    },
    // 导入返回数据
    importBackData (Obj) {
      if (JSON.stringify(Obj) === '{}') return
      let _formObj = Obj.form_json
      this.detailObj = Obj
      this.curKeyList = _formObj.curKeyList
      this.curCommonList = _formObj.curCommonList
      this.extraArr = [_formObj.extraArr[0]]
      this.reduceArr = _formObj.reduceArr
      this.extraArr[0].remark = '' // 上级备注置空
      this.reduceArr[0].remark = '' // 上级备注置空
      this.summaryObj = _formObj.summaryObj
      this.planKeyList = _formObj.planKeyList
      this.planCommonList = _formObj.planCommonList
      this.examObj = _formObj.examObj
      this.backResetSupData()
      // if (_formObj.leaderObj) {
      //   this.leaderObj = _formObj.leaderObj
      // }
      this.formTempName = '通用模板'
    },
    // 返回后重置上级评分
    backResetSupData () {
      this.curKeyList.map(item => {
        item.supScore = 0
        item.supQuality = 0
        item.supRate = 0
      })
      this.curCommonList.map(item => {
        item.supScore = 0
        item.supQuality = 0
        item.supRate = 0
      })
      this.extraArr.map(item => {
        item.supScore = 0
      })
      this.reduceArr.map(item => {
        item.supScore = 0
      })
      this.examObj.curExamList.map(item => {
        item.supScore = 0
      })
      this.examObj.planExamList.map(item => {
        item.supScore = 0
      })
    },
    flowColor (idx) { // 顶部流程标题颜色
      let _backStr = 'back-flow-color'
      if (this.isComplete) {
        _backStr = 'pre-flow-color'
      } else {
        if (this.approveIdx === idx) {
          _backStr = 'cur-flow-color'
        }
        if (this.approveIdx > idx) {
          _backStr = 'pre-flow-color'
        }
        if (this.approveIdx < idx) {
          _backStr = 'back-flow-color'
        }
      }
      return _backStr
    },
    addKeyList () { // 添加重点事项
      this.curKeyList.push({
        title: '常规性工作',
        content: '',
        result: '',
        score: 0,
        selfRate: 0,
        supRate: 0,
        selfQuality: 0,
        supQuality: 0,
        selfScore: 0,
        supScore: 0,
        remark: ''
      })
    },
    removeKeyList (idx) { // 删除重点事项
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确定要删除本条数据？</p>',
        loading: true,
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          this.curKeyList.splice(idx, 1)
          this.$Modal.remove()
        }
      })
    },
    addCommonList () { // 添加阶段性工作
      this.curCommonList.push({
        title: '阶段性工作',
        content: '',
        result: '',
        score: 0,
        selfRate: 0,
        supRate: 0,
        selfQuality: 0,
        supQuality: 0,
        selfScore: 0,
        supScore: 0,
        remark: ''
      })
    },
    removeCommonList (idx) { // 移除阶段性工作
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确定要删除本条数据？</p>',
        loading: true,
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          this.curCommonList.splice(idx, 1)
          this.$Modal.remove()
        }
      })
    },
    addExtraList () {
      this.extraArr.push({
        content: '',
        selfScore: 0,
        supScore: 0,
        remark: ''
      })
    },
    removeExtraList (idx) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确定要删除本条数据？</p>',
        loading: true,
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          this.extraArr.splice(idx, 1)
          this.$Modal.remove()
        }
      })
    },
    addReduceList () {
      this.reduceArr.push({
        content: '',
        selfScore: 0,
        supScore: 0,
        remark: ''
      })
    },
    removeReduceList (idx) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确定要删除本条数据？</p>',
        loading: true,
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          this.reduceArr.splice(idx, 1)
          this.$Modal.remove()
        }
      })
    },
    addPlanKeyList () { // 添加计划重点事项
      this.planKeyList.push({
        title: '常规性工作',
        content: '',
        result: '',
        time: '',
        selfScore: 0,
        remark: ''
      })
    },
    removePlanKeyList (idx) { // 移除计划重点事项
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确定要删除本条数据？</p>',
        loading: true,
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          this.planKeyList.splice(idx, 1)
          this.$Modal.remove()
        }
      })
    },
    addPlanCommonList () { // 添加计划一般事项
      this.planCommonList.push({
        title: '阶段性工作',
        content: '',
        result: '',
        time: '',
        selfScore: 0,
        remark: ''
      })
    },
    removePlanCommonList (idx) { // 移除计划一般事项
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确定要删除本条数据？</p>',
        loading: true,
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          this.planCommonList.splice(idx, 1)
          this.$Modal.remove()
        }
      })
    },
    // 计划配分统计
    planWorkTotalScore () {
      let keyTotal = 0
      let commonTotal = 0
      this.planKeyList.map(item => {
        keyTotal += (!item.selfScore || isNaN(item.selfScore) || item.selfScore === '') ? 0 : parseFloat(item.selfScore)
      })
      this.planCommonList.map(item => {
        commonTotal += (!item.selfScore || isNaN(item.selfScore) || item.selfScore === '') ? 0 : parseFloat(item.selfScore)
      })
      return (keyTotal + commonTotal)
    },
    selfTotalScore () { // 配分合计
      let keyTotal = 0
      let commonTotal = 0
      this.curKeyList.map(item => {
        keyTotal += (!item.score || isNaN(item.score) || item.score === '') ? 0 : parseFloat(item.score)
      })
      this.curCommonList.map(item => {
        commonTotal += (!item.score || isNaN(item.score) || item.score === '') ? 0 : parseFloat(item.score)
      })
      return (keyTotal + commonTotal)
    },
    selfTotalRate () { // 自评完成率统计
      let keyRate = 0
      let commonRate = 0
      this.curKeyList.map(item => {
        keyRate += (!item.selfRate || isNaN(item.selfRate) || item.selfRate === '') ? 0 : parseFloat(item.selfRate)
      })
      this.curCommonList.map(item => {
        commonRate += (!item.selfRate || isNaN(item.selfRate) || item.selfRate === '') ? 0 : parseFloat(item.selfRate)
      })
      let _dataLen = this.curKeyList.length + this.curCommonList.length
      let backData = (keyRate + commonRate) / _dataLen
      return backData.toFixed(1) + '%'
    },
    supTotalRate () { // 上级完成率统计
      let keyRate = 0
      let commonRate = 0
      this.curKeyList.map(item => {
        keyRate += (!item.supRate || isNaN(item.supRate) || item.supRate === '') ? 0 : parseFloat(item.supRate)
      })
      this.curCommonList.map(item => {
        commonRate += (!item.supRate || isNaN(item.supRate) || item.supRate === '') ? 0 : parseFloat(item.supRate)
      })
      let _dataLen = this.curKeyList.length + this.curCommonList.length
      let backData = (keyRate + commonRate) / _dataLen
      return backData.toFixed(1) + '%'
    },
    selfTotalQuality () { // 自评质量统计
      let keyQuality = 0
      let commonQuality = 0
      this.curKeyList.map(item => {
        keyQuality += (!item.selfQuality || isNaN(item.selfQuality) || item.selfQuality === '') ? 0 : parseFloat(item.selfQuality)
      })
      this.curCommonList.map(item => {
        commonQuality += (!item.selfQuality || isNaN(item.selfQuality) || item.selfQuality === '') ? 0 : parseFloat(item.selfQuality)
      })
      let _dataLen = this.curKeyList.length + this.curCommonList.length
      let backData = (keyQuality + commonQuality) / _dataLen
      return backData.toFixed(1) + '%'
    },
    supTotalQuality () { // 上级质量统计
      let keyQuality = 0
      let commonQuality = 0
      this.curKeyList.map(item => {
        keyQuality += (!item.supQuality || isNaN(item.supQuality) || item.supQuality === '') ? 0 : parseFloat(item.supQuality)
      })
      this.curCommonList.map(item => {
        commonQuality += (!item.supQuality || isNaN(item.supQuality) || item.supQuality === '') ? 0 : parseFloat(item.supQuality)
      })
      let _dataLen = this.curKeyList.length + this.curCommonList.length
      let backData = (keyQuality + commonQuality) / _dataLen
      return backData.toFixed(1) + '%'
    },
    selfTotalScoreT () { // 自评总得分统计
      let keyTotal = 0
      let commonTotal = 0
      this.curKeyList.map(item => {
        keyTotal += (!item.selfScore || isNaN(item.selfScore) || item.selfScore === '') ? 0 : parseFloat(item.selfScore)
      })
      this.curCommonList.map(item => {
        commonTotal += (!item.selfScore || isNaN(item.selfScore) || item.selfScore === '') ? 0 : parseFloat(item.selfScore)
      })
      return (keyTotal + commonTotal).toFixed(2)
    },
    supTotalScoreT () { // 上级总得分统计
      let keyTotal = 0
      let commonTotal = 0
      this.curKeyList.map(item => {
        keyTotal += (!item.supScore || isNaN(item.supScore) || item.supScore === '') ? 0 : parseFloat(item.supScore)
      })
      this.curCommonList.map(item => {
        commonTotal += (!item.supScore || isNaN(item.supScore) || item.supScore === '') ? 0 : parseFloat(item.supScore)
      })
      return (keyTotal + commonTotal).toFixed(2)
    },
    selfSubTotal () { // 自评加减分小计
      let keyTotal = 0
      let commonTotal = 0
      let extraSelfScore = 0
      let reduceSelfScore = 0
      this.curKeyList.map(item => {
        keyTotal += (!item.selfScore || isNaN(item.selfScore) || item.selfScore === '') ? 0 : parseFloat(item.selfScore)
      })
      this.curCommonList.map(item => {
        commonTotal += (!item.selfScore || isNaN(item.selfScore) || item.selfScore === '') ? 0 : parseFloat(item.selfScore)
      })
      this.extraArr.map(item => {
        extraSelfScore += (!item.selfScore || item.selfScore === null || isNaN(item.selfScore) || item.selfScore === '') ? 0 : parseFloat(item.selfScore)
      })
      this.reduceArr.map(item => {
        reduceSelfScore += (!item.selfScore || item.selfScore === null ||isNaN(item.selfScore) || item.selfScore === '') ? 0 : parseFloat(item.selfScore)
      })
      // let extraSelfScore = (isNaN(this.extraObj.selfScore) || this.extraObj.selfScore === '') ? 0 : parseFloat(this.extraObj.selfScore)
      // let reduceSelfScore = (isNaN(this.reduceObj.selfScore) || this.reduceObj.selfScore === '') ? 0 : parseFloat(this.reduceObj.selfScore)
      let backScore = keyTotal + commonTotal
      this.examObj.curExamList[0].selfScore = parseFloat(backScore.toFixed(2))
      this.examObj.curExamList[2].selfScore = parseFloat(extraSelfScore + reduceSelfScore)
      return parseFloat(extraSelfScore + reduceSelfScore)
    },
    supSubTotal () { // 上级小计
      let keyTotal = 0
      let commonTotal = 0
      let extraSupScore = 0
      let reduceSupScore = 0
      this.curKeyList.map(item => {
        keyTotal += (item.supScore === null || isNaN(item.supScore) || item.supScore === '') ? 0 : parseFloat(item.supScore)
      })
      this.curCommonList.map(item => {
        commonTotal += (item.supScore === null || isNaN(item.supScore) || item.supScore === '') ? 0 : parseFloat(item.supScore)
      })
      this.extraArr.map(item => {
        extraSupScore += (item.supScore === null || isNaN(item.supScore) || item.supScore === '') ? 0 : parseFloat(item.supScore)
      })
      this.reduceArr.map(item => {
        reduceSupScore += (item.supScore === null || isNaN(item.supScore) || item.supScore === '') ? 0 : parseFloat(item.supScore)
      })
      // let extraSupScore = (isNaN(this.extraObj.supScore) || this.extraObj.supScore === '') ? 0 : parseFloat(this.extraObj.supScore)
      // let reduceSupScore = (isNaN(this.reduceObj.supScore) || this.reduceObj.supScore === '') ? 0 : parseFloat(this.reduceObj.supScore)
      this.examObj.curExamList[0].supScore = parseFloat((keyTotal + commonTotal).toFixed(2))
      this.examObj.curExamList[2].supScore = parseFloat(extraSupScore + reduceSupScore)
      return parseFloat(extraSupScore + reduceSupScore).toFixed(2)
    },
    selfItemScore (item) { // 本月总结重点项目自评单项得分
      let _score = (item.score === '' ? 0 : parseInt(item.score)) * ((item.selfRate === '' ? 0 : parseInt(item.selfRate)) / 100 * 0.6 + (item.selfQuality === '' ? 0 : parseInt(item.selfQuality)) / 100 * 0.4)
      if (isNaN(_score)) {
        return 0
      }
      Object.assign(item, {
        selfScore: _score.toFixed(2)
      })
      return parseFloat(_score.toFixed(2))
    },
    supItemScore (item) { // 本月总结重点项目上级单项得分
      let _score = (item.score === '' ? 0 : parseInt(item.score)) * ((item.supRate === '' ? 0 : parseInt(item.supRate)) / 100 * 0.6 + (item.supQuality === '' ? 0 : parseInt(item.supQuality)) / 100 * 0.4)
      if (isNaN(_score)) {
        return 0
      }
      Object.assign(item, {
        supScore: _score.toFixed(2)
      })
      return parseFloat(_score.toFixed(2))
    },
    monthTotalScore () { // 月度考核总配分
      let _curScore = 0
      // let _planScore = 0
      this.examObj.curExamList.map(item => {
        _curScore += (!item.score || isNaN(item.score) || item.score === '') ? 0 : parseFloat(item.score)
      })
      // this.examObj.planExamList.map(item => {
      //   _planScore += (isNaN(item.score) || item.score === '') ? 0 : parseFloat(item.score)
      // })
      return parseFloat(_curScore)
    },
    monthSelfScore () { // 月度考核自评得分
      let _curScore = 0
      // let _planScore = 0
      this.examObj.curExamList.map(item => {
        _curScore += (!item.selfScore || isNaN(item.selfScore) || item.selfScore === '') ? 0 : parseFloat(item.selfScore)
      })
      // this.examObj.planExamList.map(item => {
      //   _planScore += (isNaN(item.selfScore) || item.selfScore === '') ? 0 : parseFloat(item.selfScore)
      // })
      this.self_evaluate_score = parseFloat(_curScore)
      return parseFloat(_curScore)
    },
    monthSupScore () { // 月度考核上级评分
      let _curScore = 0
      // let _planScore = 0
      this.examObj.curExamList.map(item => {
        _curScore += (!item.supScore || isNaN(item.supScore) || item.supScore === '') ? 0 : parseFloat(item.supScore)
      })
      // this.examObj.planExamList.map(item => {
      //   _planScore += (isNaN(item.supScore) || item.supScore === '') ? 0 : parseFloat(item.supScore)
      // })
      this.re_evaluate_score = _curScore
      return parseFloat(_curScore)
    },
    totalScore () { // 月度考核上级评分
      let leaderAddScore = (isNaN(this.leaderObj.addScore) || this.leaderObj.addScore === '' || this.leaderObj.addScore === null) ? 0 : parseFloat(this.leaderObj.addScore)
      let leaderReduceScore = (isNaN(this.leaderObj.reduceScore) || this.leaderObj.reduceScore === '' || this.leaderObj.reduceScore === null) ? 0 : parseFloat(this.leaderObj.reduceScore)
      this.final_score = (this.re_evaluate_score + leaderAddScore + leaderReduceScore)
      return parseFloat((this.re_evaluate_score + leaderAddScore + leaderReduceScore))
    },
    monthChange (date) {
      this.belong_month = date
    },
    // 仅保存草稿
    handleJustSave () {
      if (this.type === 'approve') return
      if (this.belong_month === '') {
        this.$Message.warning('请选择绩效考核时间再提交！')
        return
      }
      let _formJson = {
        formType: '0', // 通用模板，绩效档案识别用
        newVersion: true, // 新模板标识
        curKeyList: this.curKeyList,
        curCommonList: this.curCommonList,
        extraArr: this.extraArr,
        reduceArr: this.reduceArr,
        // extraObj: this.extraObj,
        // reduceObj: this.reduceObj,
        summaryObj: this.summaryObj,
        planKeyList: this.planKeyList,
        planCommonList: this.planCommonList,
        examObj: this.examObj,
        leaderObj: this.leaderObj,
        flowList: this.flowObj.flowList
      }
      API.addOrUpdatePerfForm({
        form_id: this.form_id,
        unified_account_id: JSON.parse(localStorage.getItem('userData')).unified_account_id,
        dept_flow_id: JSON.parse(localStorage.getItem('userFlow')).dept_flow_id,
        belong_month: this.belong_month,
        self_evaluate_score: this.self_evaluate_score,
        re_evaluate_score: this.re_evaluate_score,
        wps_ids: this.wps_ids,
        form_json: JSON.stringify(_formJson)
      }).then(res => {
        if (res.data.Code === 10000) {
          this.form_id = res.data.Result
          this.$Message.success('表单保存成功！')
          // this.$Message.success(res.data.Message)
        } else {
          this.$Message.warning(res.data.Message)
        }
      })
    },
    handleSave () { // 保存
      if (this.belong_month === '') {
        this.$Message.warning('请选择绩效考核时间再提交！')
        return
      }
      let _formJson = {
        formType: '0', // 通用模板，绩效档案识别用
        newVersion: true, // 新模板标识
        curKeyList: this.curKeyList,
        curCommonList: this.curCommonList,
        extraArr: this.extraArr,
        reduceArr: this.reduceArr,
        // extraObj: this.extraObj,
        // reduceObj: this.reduceObj,
        summaryObj: this.summaryObj,
        planKeyList: this.planKeyList,
        planCommonList: this.planCommonList,
        examObj: this.examObj,
        leaderObj: this.leaderObj,
        flowList: this.flowObj.flowList
      }
      this.$Modal.confirm({
        title: '提示',
        content: '<p>是否保存并提交绩效审核？</p>',
        loading: true,
        okText: '提交',
        cancelText: '不提交',
        onOk: () => {
          API.submitPerfForm({
            form_id: this.form_id,
            draft_unified_account_id: this.flowHisList.length > 0 ? this.flowHisList[0].unified_account_id : JSON.parse(localStorage.getItem('userData')).unified_account_id,
            current_unified_account_id: JSON.parse(localStorage.getItem('userData')).unified_account_id,
            next_unified_account_id: this.next_unified_account_id,
            dept_flow_id: JSON.parse(localStorage.getItem('userFlow')).dept_flow_id,
            belong_month: this.belong_month,
            self_evaluate_score: this.self_evaluate_score,
            re_evaluate_score: this.re_evaluate_score,
            form_json: JSON.stringify(_formJson),
            execute_code: 1,
            wps_ids: this.wps_ids
          }).then(res => {
            this.loading = false
            this.$Modal.remove()
            if (res.data.Code === 10000) {
              this.$Message.success(res.data.Message)
              this.$router.push({
                name: 'performanceList'
              })
            } else {
              this.$Message.warning(res.data.Message)
            }
          })
        },
        onCancel: () => {
          API.addOrUpdatePerfForm({
            form_id: this.form_id,
            unified_account_id: JSON.parse(localStorage.getItem('userData')).unified_account_id,
            dept_flow_id: JSON.parse(localStorage.getItem('userFlow')).dept_flow_id,
            belong_month: this.belong_month,
            self_evaluate_score: this.self_evaluate_score,
            re_evaluate_score: this.re_evaluate_score,
            wps_ids: this.wps_ids,
            form_json: JSON.stringify(_formJson)
          }).then(res => {
            if (res.data.Code === 10000) {
              this.form_id = res.data.Result
              this.loading = false
              this.$Modal.remove()
              this.$Message.success(res.data.Message)
              this.$router.push({
                name: 'performanceList'
              })
            } else {
              this.$Message.warning(res.data.Message)
            }
          })
        }
      })
    },
    // 提交数据调整上级分数与自评分数一致
    resetJsonData () {
      this.curCommonList.map(item => {
        item.supRate = item.selfRate
        item.supQuality = item.selfQuality
      })
      this.curKeyList.map(item => {
        item.supRate = item.selfRate
        item.supQuality = item.selfQuality
      })
      this.examObj.planExamList.map(item => {
        item.supScore = item.selfScore
      })
      this.examObj.curExamList.map(item => {
        item.supScore = item.selfScore
      })
      this.extraArr.map(item => {
        item.supScore = item.selfScore
      })
      this.reduceArr.map(item => {
        item.supScore = item.selfScore
      })
    },
    getAddReduceRemark (id) {
      let _param = {
        unified_account_id: id
      }
      API.queryPerfAddReduceRemark(_param).then(res => {
        if (res.data.Code === 10000) {
          this.addRemoveTxt = res.data.dept_remark
        }
      })
    },
    // 绩效审批
    handleApprove (execute_code) {
      let backStr = execute_code === 1 ? '是否确认提交绩效审批？' : '是否确认退回修改'
      let _formJson = {
        formType: '0', // 采购模板，绩效档案识别用
        newVersion: true, // 新模板标识
        curKeyList: this.curKeyList,
        curCommonList: this.curCommonList,
        extraArr: this.extraArr,
        reduceArr: this.reduceArr,
        // extraObj: this.extraObj,
        // reduceObj: this.reduceObj,
        summaryObj: this.summaryObj,
        planKeyList: this.planKeyList,
        planCommonList: this.planCommonList,
        examObj: this.examObj,
        flowList: this.flowObj.flowList,
        leaderObj: this.leaderObj
      }
      let execute_bak = ''
      this.$Modal.confirm({
        title: '提示',
        content: '<p>' + backStr + '</p>',
        loading: true,
        okText: execute_code === 1 ? '提交' : '确定',
        cancelText: '取消',
        render: (h) => {
          if(execute_code !== 1) {
            return h('Input', {
              props: {
                value: execute_bak,
                autofocus: true,
                placeholder: '请输入退回备注'
              },
              on: {
                input: (val) => {
                  execute_bak = val
                }
              }
            })
          } else {
            return h('div', {},  backStr)
          }
        },
        onOk: () => {
          API.submitPerfForm({
            form_id: this.form_id,
            draft_unified_account_id: this.flowHisList[0].unified_account_id,
            current_unified_account_id: JSON.parse(localStorage.getItem('userData')).unified_account_id,
            next_unified_account_id: execute_code === -1 ? this.flowHisList[0].unified_account_id : this.next_unified_account_id,
            dept_flow_id: this.detailObj.dept_flow_id,
            belong_month: this.belong_month,
            self_evaluate_score: this.self_evaluate_score,
            re_evaluate_score: this.re_evaluate_score,
            final_score: this.final_score,
            leadership_comments: this.leaderObj.leaderComment,
            form_json: JSON.stringify(_formJson),
            execute_code: execute_code,
            execute_bak: execute_bak,
            wps_ids: this.wps_ids
          }).then(res => {
            this.loading = false
            this.$Modal.remove()
            if (res.data.Code === 10000) {
              this.$Message.success(res.data.Message)
              this.$router.push({
                name: 'performanceApprove'
              })
            } else {
              this.$Message.warning(res.data.Message)
            }
          })
        }
      })
    },
    handleCancel () { // 取消321
      this.$router.go(-1)
    },
    async handleEvent (event) {
      if (event.ctrlKey && event.code === 'KeyS') {
        event.preventDefault()
        event.returnValue = false
        if (this.type === 'detail') return
        this.handleJustSave()
      }
    }
  },
  created () {
    this.$store.commit('setCollapsed', true)
    this.type = this.$route.params.id
    this.sysUser = JSON.parse(localStorage.getItem('userData')).user_name
    this.flowObj = JSON.parse(localStorage.getItem('userFlow'))
    
    if (this.type !== 'add') { // 编辑或者详情
      let _backArr = this.type.split('&id=')
      this.type = _backArr[0]
      this.form_id = _backArr[1]
      this.getFormDetail()
    } else {
      if (this.flowObj.flowList && this.flowObj.flowList.length > 0) {
        this.getAddReduceRemark(this.flowObj.flowList[0].unified_account_id) // 获取加减分文案
        // this.flowObj.flowList.reverse() // 数组倒序
        this.sysIdx = this.flowObj.flowList.findIndex(item => item.user_name === this.sysUser)
        if (this.sysIdx < (this.flowObj.flowList.length - 1)) {
          this.next_unified_account_id = this.flowObj.flowList[this.sysIdx + 1].unified_account_id
        } else {
          this.next_unified_account_id = ''
        }
      }
    }
  },
  beforeDestroy () {
    this.$store.commit('setCollapsed', false)
    window.removeEventListener('keydown', this.handleEvent)
  },
  mounted () {
    window.addEventListener('keydown', this.handleEvent)
  }
})
</script>
<style lang="less">
  .table_scroll {
    overflow-x: scroll;
  }
  .temp1_add_btn {
    position: absolute;
    margin-left: 5px;
  }
  .remove_btn {
    position: absolute;
    background: #ccc;
    border: 0;
    margin-left: 35px;
  }
  .table-bordered {
    position: relative;
    border-left:2px solid #e8eaec;
    border-top:2px solid #e8eaec;
  }
  .table-bordered tr {
    position: relative;
  }
  .table-bordered th{
    border-right:2px solid #e8eaec;
    border-bottom:2px solid #e8eaec;
    min-width: 0;
    height: 24px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    text-align: left;
    text-overflow: ellipsis;
    text-align: center;
    vertical-align: middle;
    padding: 0 10px;
    input {
      color: #999;
    }
  }
  .table-bordered td{
    border-right:2px solid #e8eaec;
    border-bottom:2px solid #e8eaec;
    min-width: 0;
    height: 24px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    text-align: left;
    text-overflow: ellipsis;
    vertical-align: middle;
    padding: 0 10px;
  }
  .table-bordered .center {
    text-align: center;
  }
  .picker-area {
    position: relative;
    display: flex;
    margin-bottom: 20px;
    align-items: center;
  }
  .picker-list {
    margin-right: 30px;
  }
  .picker-list span {
    margin-right: 5px;
    font-weight: bold;
  }
  .approve-top {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 20px;
  }
  .approve-top-pre {
    color: #fff;
    width: 30px;
    height: 30px;
    padding: 5px;
    line-height: 30px;
    text-align: center;
    font-size: 14px;
    border-radius: 15px;
    border: 2px solid #fff;
    box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.4);
  }
  .approve-top-name {
    margin-left: 12px;
  }
  .approve-top-arrow {
    margin: 0 10px;
  }
  .cur-flow-color {
    background: #D9001B;
  }
  .back-flow-color {
    background: #aaa;
  }
  .pre-flow-color {
    background: #70B603;
  }
  .list-input .ivu-input {
    // height: 28px !important;
  }
  .btn-area {
    text-align: right;
    position: absolute;
    bottom: 0;
    right: 0;
    width: 100%;
    border-top: 1px solid #e8e8e8;
    padding: 10px 16px;
    background: #fff;
  }
  .btn-area button {
    margin-left: 15px;
  }
  .approve-steps {
    position: absolute;
    width: 30px;
    right: 30px;
    top: 160px;
  }
  .step-list {
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    font-size: 14px;
    background: #70B603;
    color: #fff;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.4);
    cursor: pointer;
    margin-top: 10px;
  }
  .step-cur {
    background: #D9001B;
  }
  .back_btn {
    position: absolute;
    right: 40px;
    top: 40px;
  }
</style>

<template>
  <div>
    <Button class="add_btn" type="primary" @click="handleAdd">新增</Button>
    <Table style="margin-top: 50px;" border :loading="loading"  :columns="columns" :data="list">
    </Table>
    <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="this.listQuery.pageIndex"
            :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
    <AddModal :modalData="modalData" @dataBack="getList"></AddModal>
  </div>
</template>
<script>
import API from '@/api/voyagePlanSet'
import AddModal from './addModal'

export default {
  components: {
    AddModal
  },
  data () {
    return {
      list: [
        {
          user_name: 'test',
          ship_name: '3232'
        }
      ],
      loading: false,
      total: 0,
      listQuery: {
        pageSize: 10,
        pageIndex: 1
      },
      modalData: {
        modalType: '',
        modal: false,
        title: '',
        data: {}
      },
      columns: [
        {
          title: '人员',
          key: 'user_names',
          align: 'center'
        },
        {
          title: '船舶',
          key: 'ship_names',
          align: 'center'
        },
        {
          title: '操作',
          key: '',
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                style: {
                  margin: '0 5px'
                },
                props: {
                  icon: 'md-brush',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleModify(params.row)
                  }
                }
              }, '修改'),
              h('Button', {
                style: {
                  margin: '0 5px'
                },
                props: {
                  icon: 'md-brush',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleDel(params.row)
                  }
                }
              }, '删除')
            ])
          }
        }
      ]
    }
  },
  created () {
    this.getList()
  },
  methods: {
    getList() { // 获取列表数据
      this.loading = true
      API.queryVmpSendConfigPage(this.listQuery).then(res => {
        this.loading = false
        if (res.data.Code === 10000) {
          this.list = res.data.Result
          this.total = res.data.Total
        }
      })
    },
    handleAdd () { // 新增
      this.modalData = {
        modalType: 'add',
        modal: true,
        title: '新增推送',
        data: {}
      }
    },
    handleModify (row) { // 修改
      this.modalData = {
        modalType: 'modify',
        modal: true,
        title: '修改推送',
        data: row
      }
    },
    handleDel (row) { // 删除
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确认删除该数据？</p>',
        loading: true,
        onOk: () => {
          API.delVmpSendConfig({
            vmp_send_config_id: row.vmp_send_config_id
          }).then(res => {
            this.$Modal.remove()
            if (res.data.Code === 10000) {
              this.$Message.success(res.data.Message)
              this.getList()
            } else {
              this.$Message.error(res.data.Message)
            }
          })
        }
      })
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    }
  }
}
</script>
<style scoped>
  .add_btn {
    position: absolute;
    right: 20px;
    top: 60px;
  }
</style>
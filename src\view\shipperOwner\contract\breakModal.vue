<template>
  <Modal v-model="modalData.modal" :title="modalData.title" width="340" @on-visible-change="modalShowHide" :mask-closable="false">
    <Form ref="formRef" :model="formData" :label-width="100" style="margin-bottom: 20px;">
      <FormItem label="解约日期" prop="termination_date" :rules="{required: true, message: '此处不能为空', trigger: 'change'}">
        <DatePicker type="date" format="yyyy-MM-dd" @on-change="data=>formData.termination_date=data" :value="formData.termination_date"></DatePicker>
      </FormItem>
      <FormItem label="解约原因" prop="termination_reason">
        <Input type="textarea" v-model="formData.termination_reason" />
      </FormItem>
      <FormItem label="是否离职" prop="resign_flag">
        <Select v-model="formData.resign_flag" filterable clearable>
          <Option v-for="item in resignList" :key="'resign_flag' + item.value" :value="item.value">{{ item.label }}</Option>
        </Select>
      </FormItem>
    </Form>
    <div slot="footer"  class="demo-drawer-footer">
      <Button style="margin-right: 8px" @click="handleCancel">取消</Button>
      <Button v-if="modalData.type !== 'detail'" type="primary"  @click="handleSave">保存</Button>
    </div>
  </Modal>
</template>
<script>
import API from '@/api/shipperOwner/contract'

export default ({
  props: {
    modalData: Object
  },
  data () {
    return {
      resignList: [
        { label: '否', value: 0 },
        { label: '是', value: 1 }
      ],
      formData: {
        termination_date: '',
        termination_reason: '',
        resign_flag: 0
      }
    }
  },
  methods: {
    handleSave () { // 保存
      this.$refs['formRef'].validate((valid) => {
        if (valid) {
          let _param = {
            seafarer_contract_id: this.modalData.data.seafarer_contract_id,
            audit_status: 4, // 1拟稿中；2已推送；3履行中；4到期已归档；5已作废；6待审批；7已撤回；8作废中；9履行中拒绝废弃；10未到期归档；
            termination_date: this.formData.termination_date,
            termination_reason: this.formData.termination_reason,
            resign_flag: this.formData.resign_flag
          }
          API.changeSeafarerContractAuditStatus(_param).then(res => {
            if (res.data.Code === 10000) {
              this.$Message.success(res.data.Message)
              this.handleCancel()
              this.$emit('breakBack')
            } else {
              this.$Message.error(res.data.Message)
            }
          })
        }
      })
      
    },
    handleCancel () { // 取消
      this.clearData()
      this.modalData.modal = false
      this.$refs['formRef'].resetFields()
    },
    clearData () {
      this.formData.termination_date = ''
      this.formData.termination_reason = ''
      this.formData.resign_flag = ''
    },
    modalShowHide (val) {
      if (val) {

      } else {
        this.clearData()
        this.$refs['formRef'].resetFields()
      }
    }
  }
})
</script>

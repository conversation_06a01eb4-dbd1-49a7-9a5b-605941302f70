<template>
  <div>
    <Spin size="large" fix v-if="spinShow">
      <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
      <div>{{ spinTxt }}</div>
    </Spin>
    <!-- 人脸验证失败展示 -->
    <div style="padding: 20px;" v-if="isFaceFailed">
      <Alert type="warning" show-icon>
        人脸识别异常
        <template slot="desc">
          {{ faceFailedTip }}
        </template>
      </Alert>
      <Button long type="primary" @click="backFaceVerify">返回重试</Button>
    </div>
    <div v-else>
      <div v-for="pageNumber in totalPages" :key="pageNumber" class="pdf-page">
        <pdf
          v-if="isPdfShow"
          :src="pdfUrl"
          :page="pageNumber"
        />
      </div>
      <!-- v-if="parseFloat(audit_status) === 1 && isPdfShow" -->
      <Button v-if="parseFloat(audit_status) === 1 && isPdfShow" class="sign_btn" type="primary" long size="large" @click="isPadShow = true">签名</Button>
      <Button v-if="(parseFloat(audit_status) === 3 || parseFloat(audit_status) === 4) && isPdfShow" v-down="pdfUrl" class="sign_btn" type="primary" long size="large">下载合同</Button>
      <div v-if="isPadShow" class="sig_pad_area">
        <SignaturePad class="sig_pad" @clsoePad="isPadShow = false" @saveBack="signatureSaveBack"></SignaturePad>
      </div>
    </div>
    <!-- 短信验证弹窗 -->
    <Modal v-model="isCodeVertify" title="身份验证" :mask-closable="false" :closable="false">
      <Input v-model="msgCode" placeholder="请输入短信验证码" :disabled="!isCodeSend">
        <div slot="append">
          <Button v-if="!isCodeCount" type="primary" style="color: #fff;" @click="msgCodeSend">{{ isCodeSend ? '再次发送' : '发送验证码' }}</Button>
          <Button v-else type="primary" style="color: #fff">
            倒计时{{ msgCodeTime }}秒
          </Button>
        </div>
      </Input>
      <div slot="footer">
        <Button :disabled="this.msgCode.length === 0" type="primary" @click="checkMsgCode">确定</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import Pdf from 'vue-pdf'
import SignaturePad from './signaturePad'
import API from '@/api/shipperOwner/faceVerify'
import bAPI from '@/api/shipperOwner/contract'
import axios from 'axios'
import { getToken, getAesSecret } from '@/libs/util'
import config from '@/config'
const baseUrl = process.env.NODE_ENV === 'development' ? config.baseUrl.dev : config.baseUrl.pro

export default ({
  components: {
    Pdf,
    SignaturePad
  },
  data () {
    return {
      isCodeVertify: false, // 是否开户短信验证弹窗
      isCodeSend: false, // 短信是否发送
      isPdfShow: false, // pdf是否展示
      isCodeCount: false, // 验证码发送是否在倒计时
      spinShow: false,
      msgCode: '', // 短信验证码
      msgCodeTime: 60, // 短信验证倒计时
      msgTimer: null, // 短信验证倒计时timer
      spinTxt: '',
      contractInfo: '', // 合同信息
      audit_status: '', // 合同状态  1、待签订  2、待审核  3、履行中  4、已完成  5、废弃
      loadingTask: null, // pdf任务
      pdfUrl: '',
      totalPages: 0,
      isPadShow: false, // 手写板展示
      isFaceFailed: false, // 人脸识别是否失败
      faceFailedTip: '' // 人脸识别失败返回方案
    }
  },
  methods: {
    checkContractInfo (query) { // 校验合同信息
      let _param = {
        seafarer_contract_id: query.id
      }
      bAPI.querySeafarerContractObject(_param).then(res => {
        if (res.data.Code === 10000 && res.data.Result) {
          this.contractInfo = res.data.Result
          this.audit_status = this.contractInfo.audit_status
          switch (this.contractInfo.audit_status) {
            case '1': // 待签订 走人脸识别通道
              this.faceVerifyInit()
              break
            case '2': // 待审核 走手机验证码通道（目前这个编码不用，此处备用）
              break
            case '3': case '4': // 履行中 查看需要手机验证码验证
              let current_time = Date.now()
              let last_msg_time = localStorage.getItem('msgCodeTime')
              if (last_msg_time && (current_time - last_msg_time < 86400000)) { // 如果小于24小时，可直接拉取pdf
                this.getPdfDetail(getAesSecret(this.contractInfo.url))
              } else {
                this.isCodeVertify = true
              }
              break
            case '5': // 废弃合同  加提示
              this.$Modal.warning({
                title: '作废通知',
                content: '该合同已作废,具体事宜可与船员部联系！'
              })
              break
            default:

          }
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    msgCodeSend () { // 发送验证码
      let _param = {
        seafarer_contract_id: this.contractInfo.seafarer_contract_id,
        mobile: this.contractInfo.other_tel
      }
      bAPI.getSeafarerContractMsg(_param).then(res => {
        this.isCodeSend = true
        this.isCodeCount = true
        this.msgTimer = setInterval(() => {
          if (this.msgCodeTime > 0) {
            this.msgCodeTime--
          } else {
            clearInterval(this.msgTimer)
            this.isCodeCount = false
            this.msgCodeTime = 60
          }
        }, 1000)
        if (res.data.Code === 10000) {
          this.$Message.success(res.data.Message)
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    checkMsgCode () { // 校验验证码
      clearInterval(this.msgTimer)
      this.isCodeCount = false
      this.msgCodeTime = 60
      let _param = {
        seafarer_contract_id: this.contractInfo.seafarer_contract_id,
        mobile: this.contractInfo.other_tel,
        auth_code: this.msgCode
      }
      bAPI.checkSeafarerContractMsg(_param).then(res => {
        if (res.data.Code === 10000) {
          this.isCodeSend = false
          this.isCodeVertify = false
          this.msgCode = ''
          localStorage.setItem('msgCodeTime', Date.now())
          this.getPdfDetail(getAesSecret(this.contractInfo.url))
        } else {
          this.$Message.warning(res.data.Message)
        }
      })
    },
    async getPdfDetail (pdfUrl) { // 拉取pdf数据
      if (this.loadingTask) {
        this.loadingTask.destroy()
      }
      this.isPdfShow = true
      this.spinShow = true
      this.spinTxt = '合同数据读取中……'
      this.pdfUrl = pdfUrl
      this.loadingTask = await Pdf.createLoadingTask(this.pdfUrl)
      try {
        const pdf = await this.loadingTask.promise // 使用 await 等待结果
        this.spinShow = false
        this.spinTxt = ''
        this.totalPages = pdf.numPages
      } catch (error) {
        console.error('Error loading PDF:', error)
        this.spinShow = false // 出错时隐藏加载状态
        this.spinTxt = '加载失败，请重试'
      }
      // this.loadingTask.promise.then(pdf => {
      //   this.spinShow = false
      //   this.spinTxt = ''
      //   this.totalPages = pdf.numPages
      // })
    },
    loadPdf () { // 下载pdf到本地

    },
    async signatureSaveBack (data) { // 签名保存回调
      let query = this.$route.query
      this.spinShow = true
      const response = await fetch(data)
      const blob = await response.blob()
      const formData = new FormData()
      const token = getToken()
      const postUrl = baseUrl + 'seafarer/contract/updateSeafarerContractSign'
      const config = {
        onUploadProgress: progressEvent => {
          this.isPadShow = false
          let progress = parseFloat(progressEvent.progress) * 100
          let percent = progress.toFixed(2)
          this.spinTxt = '已上传' + percent + '%'
          if (progress === 100) {
            this.spinTxt = '等待服务器解析中……'
          }
        }
      }
      formData.append('token', token)
      formData.append('seafarer_contract_id', query.id)
      formData.append('file', blob, 'signature.png')
      this.isPdfShow = false
      axios.post(postUrl, formData, config).then(res => {
        this.spinShow = false
        this.spinTxt = ''
        if (res.data.Code === 10000) {
          this.audit_status = 3 // 签订成功后把状态强制改为履行中
          this.getPdfDetail(getAesSecret(res.data.Result))
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    backFaceVerify () { // 失败后返回重试按钮触发
      let query = this.$route.query
      const backUrl = baseUrl + 'contractMobile?id=' + query.id
      window.location.href = backUrl
    },
    faceVerifyInit () { // 人脸验证初始化
      if (this.isPdfShow) return
      let query = this.$route.query
      let metaInfo = getMetaInfo()
      const { protocol, host } = window.location
      // const backUrl = 'http://localhost:8080/contractMobile?id=2DD31802F32540D582CA32B92F5AB53F'
      const backUrl = baseUrl + 'contractMobile?id=' + query.id
      let param = {
        seafarer_contract_id: query.id,
        meta_info: JSON.stringify(metaInfo),
        return_url: backUrl
      }
      API.initFaceVerify(param).then(res => {
        window.location.href = res.data.CertifyUrl
      })
    },
    checkFaceVerify (obj, id) { // 首次返回校验人脸识别结果
      let verifyStr = ''
      switch (obj.subCode) {
        case 'Z5050':
          verifyStr = '人脸验证成功'
          break
        case 'Z5051':
          verifyStr = '上传刷脸图片失败'
          break
        case 'Z5052':
          verifyStr = '数据错误或程序异常'
          break
        case 'Z5053':
          verifyStr = '网络错误'
          break
        case 'Z5054':
          verifyStr = '摄像头无权限或无法获取摄像头数据'
          break
        case 'Z5055':
          verifyStr = '用户退出'
          break
        case 'Z5056':
          verifyStr = '重试次数过多'
          break
        case 'Z5057':
          verifyStr = '视频上传超时'
          break
        case 'Z5058':
          verifyStr = '视频格式不满足要求'
          break
        case 'Z5059':
          verifyStr = '视频中无有效人脸'
          break
        case 'Z5128':
          verifyStr = '验证不是同一个人'
          break
        default:

      }
      let _param = {
        seafarer_contract_id: id,
        certify_id: obj.extInfo.certifyId
      }
      API.describeFaceVerify(_param).then(res => {
        if (res.data.Code === '200') { // 成功
          if (res.data.Passed === 'T') {
            this.isFaceFailed = false
            bAPI.querySeafarerContractObject(_param).then(res => {
              if (res.data.Code === 10000 && res.data.Result) {
                this.contractInfo = res.data.Result
                this.audit_status = this.contractInfo.audit_status
                this.getPdfDetail(getAesSecret(this.contractInfo.url))
              } else {
                this.$Modal.warning({
                  title: '异常通知',
                  content: '未获取到合同信息,请与船员部负责人联系查阅！'
                })
              }
            })
          } else {
            this.isFaceFailed = true
            this.faceFailedTip = verifyStr
          }
        } else {
          this.isFaceFailed = true
          this.faceFailedTip = verifyStr
        }
      })
    }
  },
  created () {
    let query = this.$route.query
    if (query.id) {
      if (query.response) { // 如果有返回response则表示人脸已经验证，未知过与不过
        let backObj = JSON.parse(decodeURIComponent(query.response)) // 将返回的文本解码再转为对象
        this.checkFaceVerify(backObj, query.id) // 校验人脸识别结果
      } else {
        this.checkContractInfo(query) // 校验合同信息
      }
    } else {
      this.$Modal.warning({
        title: '异常通知',
        content: '未找到有效的合同信息,请与船员部负责人联系查阅！'
      })
    }
  }
})
</script>
<style scoped>
  .sign_btn {
    position: fixed;
    left: 0;
    bottom: 0;
  }
  .sig_pad_area {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: rgba(0,0,0,0.7);
    z-index: 999;
  }
  .sig_pad {
    position: absolute;
    width: 96vw;
    left: 2vw;
    top: 50%;
    margin-top: -100px;
  }
  .demo-spin-icon-load{
    animation: ani-demo-spin 1s linear infinite;
  }
  @keyframes ani-demo-spin {
    from { transform: rotate(0deg);}
    50%  { transform: rotate(180deg);}
    to   { transform: rotate(360deg);}
  }
</style>
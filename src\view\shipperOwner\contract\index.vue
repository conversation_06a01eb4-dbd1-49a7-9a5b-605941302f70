<template>
  <div>
    <Card>
      <search @searchResults='searchResults' :setSearch='setSearchData' @resetResults='resetResults'></search>
      <div class="add_btn">
        <Spin v-if="isDataLoading" fix>
          <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
          <div>数据同步中…</div>
        </Spin>
        <!-- <Tooltip v-if="!isAuditUser" content="内贸新增"> -->
          <Button v-if="!isAuditUser" icon="md-add" type="primary" @click="addContract(0)">内贸新增</Button>
        <!-- </Tooltip> -->
        <!-- <Tooltip v-if="!isAuditUser" content="内外贸新增"> -->
          <Button v-if="!isAuditUser" style="margin-left: 10px;" icon="md-add-circle" type="primary" @click="addContract(1)">内外贸新增</Button>
        <!-- </Tooltip> -->
        <!-- <Tooltip v-if="!isAuditUser" content="批量发起"> -->
          <Button v-if="!isAuditUser" style="margin-left: 10px;" :disabled="selectList.length === 0" icon="md-send" type="primary" @click="batchPush">批量发起</Button>
        <!-- </Tooltip> -->
        <!-- <Tooltip v-if="!isAuditUser" content="批量催签"> -->
          <Button v-if="!isAuditUser" style="margin-left: 10px;" :disabled="selectList.length === 0" icon="ios-alarm" type="primary" @click="batchAlarm">批量催签</Button>
        <!-- </Tooltip> -->
        <!-- <Tooltip v-if="isAuditUser" content="批量审批"> -->
          <Button v-if="isAuditUser" style="margin-left: 10px;" :disabled="selectList.length === 0" icon="md-checkmark-circle" type="primary" @click="batchAudit">批量审批</Button>
        <!-- </Tooltip> -->
        <!-- <Tooltip content="同步数据"> -->
          <Button style="margin-left: 10px;" icon="md-sync" type="info" @click="resetContract">同步数据</Button>
        <!-- </Tooltip> -->
        <!-- <Button icon="md-add" type="primary" @click="addContract(0)"></Button>
        <Button style="margin-left: 10px;" icon="md-add-circle" type="primary" @click="addContract(1)">内外贸新增</Button>
        <Button style="margin-left: 10px;" :disabled="selectList.length === 0" icon="md-send" type="primary" @click="batchPush">批量推送</Button>
        <Button style="margin-left: 10px;" :disabled="selectList.length === 0" icon="md-checkmark-circle" type="primary" @click="batchAudit">批量审批</Button>
        <Button style="margin-left: 10px;" icon="md-sync" type="info" @click="resetContract">同步数据</Button> -->
      </div>
    </Card>
    <Card style="margin-top: 10px">
      <Spin size="large" fix v-if="spinShow">
        <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
        <div>{{ spinTxt }}</div>
      </Spin>
      <Table border :loading="isLoading" :columns="columns" @on-row-dblclick="showPdf" @on-selection-change="selectChange" :data="list"></Table>
      <Page show-sizer :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="listQuery.pageSize" :current.sync="listQuery.pageIndex"
          :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
    </Card>
    <input
      ref="fileInput"
      type="file"
      style="display: none;"
      @change="handleFileChange"
    />
    <AddModal :modalData="addModal" @addBack="addBack"></AddModal>
    <BreakModal :modalData="breakModal" @breakBack="breakBack"></BreakModal>
    <Modal :width="350" title="批量审批" v-model="isAuditModel" :mask-closable="false">
      <Form :label-width="70">
        <FormItem label="审批结果">
          <Select v-model="auditData.audit_result">
            <Option value="true">通过</Option>
            <Option value="false">不通过</Option>
          </Select>
        </FormItem>
        <FormItem label="审批意见">
          <Input v-model="auditData.comment"></Input>
        </FormItem>
      </Form>
      <div slot="footer"  class="demo-drawer-footer">
        <Button style="margin-right: 8px" @click="handleAuditCancel">取消</Button>
        <Button type="primary"  @click="handleAuditSave">保存</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import search from '_c/search' // 查询组件
import AddModal from './addModal'
import BreakModal from './breakModal'
import BasicAPI from '@/api/basicData'
import API from '@/api/shipperOwner/contract'
import axios from 'axios'
import { getToken } from '@/libs/util'
import CryptoJS from 'crypto-js'
import config from '@/config'
const baseUrl = process.env.NODE_ENV === 'development' ? config.baseUrl.dev : config.baseUrl.pro
// import { PDFDocument, rgb } from 'pdf-lib'
// import fontkit from '@pdf-lib/fontkit'
// import { exportWordDocx  } from './docTemp.js'
// import dataJson from './docData.json'

export default ({
  components: {
    search,
    AddModal,
    BreakModal
  },
  computed: {
    isAuditUser() {
      return localStorage.getItem('userDataId') === '13F2B8FD861446719303E92FDC559E48'
    }
  },
  data () {
    return {
      secretKey: 'UNIFIED_Xt603209',
      isAuditModel: false,
      auditData: {
        qiyuessuo_contract_ids: '',
        audit_result: 'true',
        comment: ''
      },
      copyUrl: '', // 复制的url
      spinShow: false,
      isSendShow: false,
      cur_seafarer_id: '',
      selectList: [],
      addModal: {
        type: '',
        modal: false,
        title: '',
        data: {},
        dutyList: []
      },
      breakModal: {
        modal: false,
        title: '合同解约',
        data: {}
      },
      listQuery: {
        pageSize: 10,
        pageIndex: 1
      },
      isDataLoading: false,
      isLoading: false,
      total: 0,
      setSearchData: {
        seafarer_name: {
          type: 'text',
          label: '姓名',
          value: '',
          width: 140,
          isdisable: false
        },
        crt_duty_id: {
          type: 'select',
          label: '职务',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 140,
          value: '',
          filterable: true
        },
        audit_status: {
          type: 'select',
          label: '状态',
          selectData: [
            {label: '拟稿中', value: 1},
            {label: '已推送', value: 2},
            {label: '履行中', value: 3},
            {label: '到期归档', value: 4},
            {label: '已作废', value: 5},
            {label: '待审批', value: 6},
            {label: '已撤回', value: 7},
            {label: '作废中', value: 8},
            {label: '履行中（拒绝作废）', value: 9},
            {label: '未到期归档', value: 10}
          ],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 120,
          value: '',
          filterable: true
        },
        international_flag: {
          type: 'select',
          label: '区域',
          selectData: [
            { label: '内贸', value: 0 },
            { label: '内外贸', value: 1 }
          ],
          selected: '',
          placeholder: '请选择',
          width: 120,
          value: ''
        }
      },
      columns: [
        {
          type: 'selection',
          width: 60,
          align: 'center'
        },
        {
          type: 'index',
          width: 60,
          align: 'center'
        },
        {
          title: '姓名',
          key: 'seafarer_name',
          align: 'center'
        },
        {
          title: '合同职务',
          key: 'crt_duty_name',
          align: 'center'
        },
        {
          title: '合同编号',
          key: 'contract_no',
          sortable: true,
          align: 'center',
          render: (h, params) => {
            if (parseFloat(params.row.audit_status) === 4 && params.row.termination_reason !== '') {
              return h('Tooltip', {
                props: {
                  maxWidth: 300,
                  transfer: true
                }
              }, [
                h('div', {
                  slot: 'content'
                }, params.row.termination_reason),
                h('div', {
                  style: {
                    display: 'inline-flex',
                    alignItems: 'center',
                    marginTop: '5px'
                  }
                }, params.row.contract_no)
              ])
            } else {
              return h('div', {
                style: {
                  display: 'inline-flex',
                  alignItems: 'center',
                  marginTop: '5px'
                }
              }, params.row.contract_no)
            }
          }
        },
        {
          title: '合同类型',
          key: 'contract_type',
          align: 'center',
          minWidth: 80,
          render: (h, params) => {
            // let typeStr = params.row.contract_type === '0' ? '首次合同' : '续签合同'
            let typeStr = params.row.offline_flag === '1' ? '线下合同' : '云签合同'
            let offLineStr = params.row.international_flag === '0' ? '(内贸)' : '(内外贸)'
            return h('div', {}, typeStr + offLineStr)
          }
        },
        {
          title: '工资总额',
          key: 'contract_basic_salary',
          align: 'center',
          ellipsis: true,
          tooltip: true,
          minWidth: 80,
          maxWidth: 150,
          render: (h, params) => {
            let totalSalary = parseFloat(params.row.contract_basic_salary) + parseFloat(params.row.contract_merit_pay)
            let salaryStr = params.row.international_flag === '0'? '元' : '美元'
            return h('div', {}, totalSalary.toFixed(0) + salaryStr)
          }
        },
        {
          title: '身份证',
          key: 'seafarer_id_no',
          align: 'center',
          ellipsis: true,
          tooltip: true,
          minWidth: 80
        },
        {
          title: '开始时间',
          key: 'contract_period_from',
          align: 'center'
        },
        {
          title: '结束时间',
          key: 'contract_period_to',
          align: 'center'
        },
        {
          title: '合同状态',
          key: 'audit_status',
          align: 'center',
          render: (h, params) => {
            let typeStr = ''
            /* {label: '拟稿中', value: 1},
            {label: '已推送', value: 2},
            {label: '履行中', value: 3},
            {label: '到期归档', value: 4},
            {label: '已作废', value: 5},
            {label: '待审批', value: 6},
            {label: '已撤回', value: 7},
            {label: '作废中', value: 8},
            {label: '履行中（拒绝作废）', value: 9},
            {label: '未到期归档', value: 10} */
            switch(params.row.audit_status) {
              case '1':
                typeStr = '拟稿中'
                break
              case '2':
                typeStr = '已推送'
                break
              case '3':
                typeStr = '履行中'
                break
              case '4':
                typeStr = '到期归档'
                break
              case '5':
                typeStr = '已作废'
                break
              case '6':
                typeStr = '待审批'
                break
              case '7':
                typeStr = '已撤回'
                break
              case '8':
                typeStr = '作废中'
                break
              case '9':
                typeStr = '履行中（拒绝作废）'
                break
              case '10':
                typeStr = '未到期归档'
                break
              default:
                typeStr = ''
            }
            return h('div', {}, typeStr)
          }
        },
        {
          title: '操作',
          key: 'seafarer_name',
          align: 'center',
          render: (h, params) => {
            return h('Tooltip', {
              props: {
                placement: 'bottom',
                theme: 'light'
              }
            }, [
              h('Button', '...'),
              h('Icon', {
                slot: 'content',
                style: {
                  display: 'block',
                  margin: '10px',
                  cursor: 'pointer'
                },
                class: 'list_btn',
                props: {
                  type: 'md-add-circle',
                  size: '14'
                },
                domProps: {
                  innerHTML: ' 内贸新增'
                },
                on: {
                  click: () => {
                    this.handleInAdd(params.row, 0)
                  }
                }
              }),
              h('Icon', {
                slot: 'content',
                style: {
                  display: 'block',
                  margin: '10px',
                  cursor: 'pointer'
                },
                class: 'list_btn',
                props: {
                  type: 'md-add-circle',
                  size: '14'
                },
                domProps: {
                  innerHTML: ' 内外贸新增'
                },
                on: {
                  click: () => {
                    this.handleInAdd(params.row, 1)
                  }
                }
              }),
              h('Icon', {
                slot: 'content',
                style: {
                  display: 'block',
                  margin: '10px',
                  cursor: 'pointer'
                },
                class: 'list_btn',
                props: {
                  type: 'ios-paper',
                  size: '14'
                },
                domProps: {
                  innerHTML: ' 详情'
                },
                on: {
                  click: () => {
                    this.handleDetail(params.row)
                  }
                }
              }),
              h('Icon', {
                slot: 'content',
                style: { // 待签订才允许导入 或者履行中且是线下合同
                  display: (parseFloat(params.row.audit_status) === 1 || (parseFloat(params.row.audit_status) === 3 && parseFloat(params.row.offline_flag) === 1)) ? 'block' : 'none',
                  margin: '10px',
                  cursor: 'pointer'
                },
                class: 'list_btn',
                props: {
                  type: 'md-cloud-upload',
                  size: '14'
                },
                domProps: {
                  innerHTML: ' 上传'
                },
                on: {
                  click: () => {
                    this.handleInport(params.row)
                  }
                }
              }),
              // h('Icon', {
              //   slot: 'content',
              //   style: {
              //     display: 'block',
              //     margin: '10px',
              //     cursor: 'pointer'
              //   },
              //   class: 'list_btn',
              //   props: {
              //     type: 'md-add-circle',
              //     size: '14'
              //   },
              //   domProps: {
              //     innerHTML: ' 删除'
              //   },
              //   on: {
              //     click: () => {
              //       this.handleDelContract(params.row)
              //     }
              //   }
              // }),
              h('Icon', {
                slot: 'content',
                style: {
                  display: parseFloat(params.row.audit_status) > 1 ? 'none' : 'block',
                  margin: '10px',
                  cursor: 'pointer'
                },
                class: 'list_btn',
                props: {
                  type: 'md-brush',
                  size: '14'
                },
                domProps: {
                  innerHTML: ' 修改'
                },
                on: {
                  click: () => {
                    this.handleModify(params.row)
                  }
                }
              }),
              h('Icon', {
                slot: 'content',
                style: {
                  // 1：拟稿中 2：已推送 3：履行中 8：作废中允许推送和再次推送
                  display: (params.row.qiyuessuo_contract_id !=='' && (parseFloat(params.row.audit_status) === 1 || parseFloat(params.row.audit_status) === 2 || parseFloat(params.row.audit_status) === 3 || parseFloat(params.row.audit_status) === 8)) ? 'block' : 'none',
                  margin: '10px',
                  cursor: 'pointer'
                },
                class: 'list_btn',
                props: {
                  type: 'ios-chatbubbles',
                  size: '14'
                },
                domProps: {
                  innerHTML: ' 推送'
                },
                on: {
                  click: () => {
                    this.handleSend(params.row, 'single')
                  }
                }
              }),
              h('Icon', {
                slot: 'content',
                style: {
                  display: (parseFloat(params.row.audit_status) === 5 || params.row.contract_status === 'DRAFT') ? 'none' : 'block',
                  margin: '10px',
                  cursor: 'pointer'
                },
                class: 'list_btn copy_btn',
                props: {
                  type: 'md-chatboxes',
                  size: '14'
                },
                domProps: {
                  innerHTML: ' 复制'
                },
                on: {
                  click: () => {
                    this.handleCopy(params.row)
                  }
                }
              }),
              h('Icon', {
                slot: 'content',
                style: {
                  // 3:履行中 9：履行中拒绝废弃 允许解约
                  display: (parseFloat(params.row.audit_status) === 3 || parseFloat(params.row.audit_status) === 9) ? 'block' : 'none',
                  margin: '10px',
                  cursor: 'pointer'
                },
                class: 'list_btn',
                props: {
                  type: 'md-bookmark',
                  size: '14'
                },
                domProps: {
                  innerHTML: ' 解约'
                },
                on: {
                  click: () => {
                    this.handleBreak(params.row)
                  }
                }
              }),
              h('Icon', {
                slot: 'content',
                style: {
                  // 1：拟稿中 2：已推送 3：履行中 6：待审批 9：履行中拒绝废弃 以上节点允许作废  offline_flag：0：云签   1：线下 线下可废弃
                  display: (params.row.qiyuessuo_contract_id !=='' || parseFloat(params.row.offline_flag) === 1) && (parseFloat(params.row.audit_status) === 1 || parseFloat(params.row.audit_status) === 2 || parseFloat(params.row.audit_status) === 3 || parseFloat(params.row.audit_status) === 6 || parseFloat(params.row.audit_status) === 9) ? 'block' : 'none',
                  margin: '10px',
                  cursor: 'pointer'
                },
                class: 'list_btn',
                props: {
                  type: 'md-close-circle',
                  size: '14'
                },
                domProps: {
                  innerHTML: ' 废弃'
                },
                on: {
                  click: () => {
                    this.handleDel(params.row)
                  }
                }
              })
            ])
          }
        }
      ],
      list: []
    }
  },
  methods: {
    getList () {
      this.isLoading = true
      API.querySeafarerContractPage(this.listQuery).then(res => {
        this.isLoading = false
        if (res.data.Code === 10000) {
          this.list = res.data.Result
          this.total = res.data.Total
        }
      })
    },
    async showPdf (row) { // 预览pdf
      if (row.qiyuessuo_contract_id) {
        let _url = '/v2/contract/viewurl?contractId=' + row.qiyuessuo_contract_id + '&documentId=' + row.qiyuessuo_document_id + '&downloadButton=true&printButton=true'
        let _param = {
          // paramMap: '',
          url: _url,
          method: 'GET'
        }
        API.requestTransferStation(_param).then(res => {
          if (res.data.Code === 10000) {
            if (res.data.Result.responseCode === '00000000') {
              let pageUrl = res.data.Result.result.pageUrl
              window.open(pageUrl, '_blank')
            } else {
              this.$Message.warning(res.data.Result.message)
            }
          } else {
            this.$Message.error(res.data.Message)
          }
        })
      } else {
        if (!row.wpsUrl) {
          this.$Message.warning('文件还未生成')
          return
        }
        try {
          const encryptedData = CryptoJS.enc.Base64.parse(row.wpsUrl)
          const key = CryptoJS.enc.Utf8.parse(this.secretKey)

          const decrypted = CryptoJS.AES.decrypt(
            { ciphertext: encryptedData },
            key,
            { mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7 }  // ECB 模式，不需要 IV
          )
          const decryptedText = decrypted.toString(CryptoJS.enc.Utf8)
          sessionStorage.setItem('wpsUrl', decryptedText)
          sessionStorage.setItem('token', getToken())
          const jump = this.$router.resolve({ name: 'viewFile' })
          window.open(jump.href, '_blank')
        } catch (err) {
          console.log('出错了： ' + err)
        }
      }
    },
    handleInvalid (row) { // 自己用 短信推送
      console.log(row)
      // 获取短链接
      let _url = '/v2/contract/shorturl?contractId=' + row.qiyuessuo_contract_id
      let urlObj = {
        url: _url,
        method: 'GET'
      }
      API.requestTransferStation(urlObj).then(resU => {
        let msgParam = {
          mobile: row.other_tel,
          sms_content: '兴通海运股份有限公司船员部申请《劳动合同》废弃给您，点击废弃 ' + resU.data.Result.result.shorturl
        }
        API.pushMsg(msgParam).then(resM => {
          if (resM.data.Code === 10000) {
            // 内部再走一次推送状态提交
            API.changeSeafarerContractAuditStatus({ seafarer_contract_id: row.seafarer_contract_id, audit_status: 2 }).then(resS => {
              if (resS.data.Code === 10000) {
                this.getList()
                // this.$Message.success('短信已经成功推送！')
              } else {
                this.$Message.error(resS.data.Message)
              }
            })
          } else {
            this.$Message.error(resM.data.Message)
          }
        })
      })
    },
    selectChange (arr) { // 选中
      this.selectList = arr
    },
    handleDelContract (row) {
      API.delSeafarerContract({ seafarer_contract_id: row.seafarer_contract_id }).then(res => {
        if (res.data.Code === 10000) {
          this.$Message.success(res.data.Message)
          this.getList()
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    getDutyList () { // 船员职务列表
      BasicAPI.queryDictCacheList({
        dic_code: 'unCrewDuty'
      }).then(res => {
        if (res.data.Code === 10000) {
          res.data.Result.map(item => {
            this.setSearchData.crt_duty_id.selectData.push({
              label: item.entryDesc || item.name,
              value: item.id
            })
          })
        }
      })
    },
    addContract (flag) { // 添加合同
      let titleSub = flag === 0 ? '(内贸)' : '(内外贸)'
      this.addModal = {
        type: 'add',
        flag: flag, // 是否国际电签（1是，0否）
        title: '新增船员合同' + titleSub,
        modal: true,
        data: {},
        dutyList: this.setSearchData.crt_duty_id.selectData
      }
    },
    // 审批保存
    handleAuditSave() {
      if (this.auditData.qiyuessuo_contract_ids === '') {
        this.$Message.warning('请选择有效的审批合同！')
        return
      }
       API.batchAuditSeafarerContracts(this.auditData).then(res => {
          this.isAuditModel = false
          if (res.data.Code === 10000) {
            this.$Message.success(res.data.Message)
            this.getList()
          } else {
            this.$Message.error(res.data.Message)
          }
        })
    },
    // 审批取消
    handleAuditCancel() {
      this.isAuditModel = false
      this.auditData = {
        qiyuessuo_contract_ids: '',
        audit_result: 'true',
        comment: ''
      }
    },
    batchAudit() { // 批量审批
      if (this.selectList.length > 0) {
        let canBatchAudit = this.selectList.every(item => item.audit_status === '6')
        if(!canBatchAudit) {
          this.$Message.warning('请过滤并选择待审批合同进行审批！')
          return
        }
        this.isAuditModel = true
        // 遍历this.selectList数组，筛选item.qiyuessuo_contract_id不为空的内容，6：待审批 形成数组
        let ids = this.selectList.filter(item => item.audit_status === '6').map(item => item.qiyuessuo_contract_id).join()
        this.auditData.qiyuessuo_contract_ids = ids
      }
    },
    async batchPush () { // 批量推送
      if (this.selectList.length > 0) {
        let canBatchPush = this.selectList.every(item => item.audit_status === '1')
        if(!canBatchPush) {
          this.$Message.warning('请只选拟稿合同进行批量发起！')
          return
        }
        // 遍历this.selectList数组，筛选item.qiyuessuo_contract_id不为空的内容，形成数组
        let ids = this.selectList.filter(item => item.qiyuessuo_contract_id !== '').map(item => item.qiyuessuo_contract_id).join()
        API.batchSendSeafarerContracts({qiyuessuo_contract_ids: ids}).then(res => {
          if (res.data.Code === 10000) {
            this.$Message.success(res.data.Message)
            this.getList() 
          } else {
            this.$Message.error(res.data.Message)
          }
        })
        // await this.selectList.forEach(async (item, idx) => {
        //   if (item.contract_status === '' || item.contract_status === 'DRAFT' || item.contract_status === 'SIGNING') {
        //     await this.handleSend(item, 'multile')
        //     if (idx === (this.selectList.length - 1)) { // 最后一个已经执行完成
        //       this.selectList = []
        //       this.getList()
        //       this.$Message.success('已批量推送完成！')
        //     }
        //   }
        // })
      }
    },
    // 批量催收
    batchAlarm() {
      if (this.selectList.length > 0) {
        // 遍历this.selectList数组，筛选2：已推送 8：作废中的合同进行催签
        let canBatchAlarm = this.selectList.every(item => item.audit_status === '2' || item.audit_status === '8')
        if(!canBatchAlarm) {
          this.$Message.warning('只能选择已推送和作废中合同进行催签！')
          return
        }
        let ids = this.selectList.filter(item => (item.audit_status === '2' || item.audit_status === '8')).map(item => item.qiyuessuo_contract_id).join()
        if(ids === '') {
          this.$Message.warning('请选择有效的催收合同！')
          return
        }
        API.batchNoticeSeafarerContracts({qiyuessuo_contract_ids: ids}).then(res => {
          if (res.data.Code === 10000) {
            this.$Message.success(res.data.Message)
            this.getList()
          } else {
            this.$Message.error(res.data.Message)
          }
        })
      }
    },
    resetContract () { // 同步数据
      this.isDataLoading = true
      API.SeafarerInfoSyncService().then(res => {
        this.isDataLoading = false
        if (res.data.Code === 10000) {
          this.$Message.success(res.data.Message)
          this.getDutyList()
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    handleInport (row) { // 上传合同
      this.cur_seafarer_id = row.seafarer_contract_id
      this.$refs.fileInput.value = ''
      this.$refs.fileInput.click()
    },
    async handleFileChange (event) {
      const file = event.target.files[0]
      const formData = new FormData()
      const token = getToken()
      if (file.type !== undefined) {
        formData.append('token', token)
        formData.append('seafarer_contract_id', this.cur_seafarer_id)
        formData.append('file', file)
      }
      this.spinShow = true
      this.spinTxt = '正在上传中…'
      const config = {
        onUploadProgress: progressEvent => {
          let progress = parseFloat(progressEvent.progress) * 100
          let percent = progress.toFixed(2)
          this.spinTxt = '已上传' + percent + '%'
          if (progress === 100) {
            this.spinTxt = '等待服务器解析中……'
          }
        }
      }
      const postUrl = baseUrl + 'seafarer/contract/updateSeafarerContractReplaceFile'
      await axios.post(postUrl, formData, config).then(res => {
        this.spinShow = false
        if (res.data.Code === 10000) {
          this.cur_seafarer_id = ''
          this.$Message.success(res.data.Message)
          this.getList()
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    handleInAdd (row, flag) { // 当前人员新增
      let addObj = {...row, ...{
        contract_no: '',
        contract_period: '',
        contract_period_from: '',
        contract_period_to: '',
        probation_period: '',
        probation_period_from: '',
        probation_period_to: '',
        probation_period_basic_salary: '',
        probation_period_merit_pay: '',
        contract_type: '',
        contract_basic_salary: '',
        contract_merit_pay: '',
        party_a_seal_image: '',
        party_a_sign_image: '',
        party_a_sign_date: '',
        party_b_sign_image: '',
        party_b_sign_date: '',
        contract_type: '',
        contract_url: '',
        international_flag: flag
      }}
      let titleSub = flag === 0 ? '(内贸)' : '(内外贸)'
      this.addModal = {
        type: 'add_in',
        title: '新增船员合同' + titleSub,
        modal: true,
        data: addObj,
        dutyList: this.setSearchData.crt_duty_id.selectData
      }
    },
    handleDetail (row) { // 详情
      this.addModal = {
        type: 'detail',
        title: '船员合同详情',
        modal: true,
        data: row,
        dutyList: this.setSearchData.crt_duty_id.selectData
      }
    },
    handleModify (row) { // 修改
      let titleSub = row.international_flag === '0' ? '(内贸)' : '(内外贸)'
      this.addModal = {
        type: 'modify',
        title: '船员合同编辑' + titleSub,
        modal: true,
        data: row,
        dutyList: this.setSearchData.crt_duty_id.selectData
      }
    },
    handleSend (row, pushStr) { // 推送  发起合同 -> 获取短链接 -> 内部短信渠道推送 -> 内部状态提交（已推送） 2024-12-30
      // 改为契约锁推送 2024/12/23
      let _obj = {
        contractId: row.qiyuessuo_contract_id
      }
      let _param = {
        paramMap: JSON.stringify(_obj),
        url: '/v2/contract/send'
      }
      if (row.audit_status === '1') { // 起草状态需先发起合同再获取短链接
        API.requestTransferStation(_param).then(res => { // 发起合同，直接走契约锁推送
          if (res.data.Code === 10000) {
            if (res.data.Result.responseCode === '00000000') {
              this.$Message.success('合同推送成功！')
              // this.handleSendAfter(row, pushStr) // 拟稿状态就不再走内部推送
            } else {
              this.$Message.warning(res.data.Result.message)
            }
          } else {
            this.$Message.error(res.data.Message)
          }
        })
      } else if (row.audit_status === '2' || row.audit_status === '3' || row.audit_status === '8' || row.audit_status === '9') { // 主要是推送查看及未收到短信再次推送使用
        // 2：已推送 3：履行中 8：作废中 9：履行中拒绝废弃
        this.handleSendAfter(row, pushStr)
      } else {
        if(row.offline_flag === '1') {
          this.$Message.warning('未保障合同安全，线下合同不支持推送！')
        } else {
          this.$Message.warning('该合同状态无法推送')
        }
        return
      }
      

      // let checkStr = parseFloat(row.audit_status) === 1 ? '点击签订合同：' : '点击查看合同：'
      // this.$Modal.confirm({
      //   title: '确定推送?',
      //   content: `<p>船  员： ${row.seafarer_name}</p><p>职  务： ${row.crt_duty_name}</p><p>手  机： ${row.other_tel}`,
      //   onOk: () => {
      //     let _param = {
      //       mobile: row.other_tel,
      //       sms_content: checkStr + baseUrl + 'contractMobile?id=' + row.seafarer_contract_id
      //     }
      //     API.pushMsg(_param).then(res => {
      //       if (res.data.Code === 10000) {
      //         this.$Message.success(res.data.Message)
      //       } else {
      //         this.$Message.error(res.data.Message)
      //       }
      //     })
      //   }
      // })
    },
    handleSendAfter (row, pushStr) { // 推送获取短链接后半段
      // 获取短链接
      let _url = '/v2/contract/shorturl?contractId=' + row.qiyuessuo_contract_id
      let urlObj = {
        url: _url,
        method: 'GET'
      }
      if (pushStr === 'multile') { // 批量推送直接处理 目前不需要，换成后台接口批量推送
        API.requestTransferStation(urlObj).then(resU => {
          let msgParam = {
            mobile: row.other_tel,
            sms_content: '兴通海运股份有限公司船员部发送《劳动合同》给您，点击签署查看 ' + resU.data.Result.result.shorturl
          }
          if (resU.data.Code === 10000 && resU.data.Result.responseCode === '00000000') {
            let msgParam = {
              mobile: row.other_tel,
              sms_content: '兴通海运股份有限公司船员部发送《劳动合同》给您，点击签署查看 ' + resU.data.Result.result.shorturl
            }
            API.pushMsg(msgParam).then(resM => {
              if (resM.data.Code === 10000) {
                // 内部再走一次推送状态提交
                API.changeSeafarerContractAuditStatus({ seafarer_contract_id: row.seafarer_contract_id, audit_status: 2 }).then(resS => {
                  if (resS.data.Code === 10000) {
                    this.getList()
                    // this.$Message.success('短信已经成功推送！')
                  } else {
                    this.$Message.error(resS.data.Message)
                  }
                })
              } else {
                this.$Message.error(resM.data.Message)
              }
            })
          } else {
            this.$Message.warning(resU.data.Result.message)
          }
        })
      } else {
        // 本推送只负责后续的短信再次提醒及推送合同文件使用
        API.requestTransferStation(urlObj).then(resU => {
          if (resU.data.Code === 10000 && resU.data.Result.responseCode === '00000000') {
            this.$Modal.confirm({
              title: '确定推送?',
              content: `<p>船  员： ${row.seafarer_name}</p><p>职  务： ${row.crt_duty_name}</p><p>手  机： ${row.other_tel}`,
              onOk: () => {
                let msgParam = {
                  mobile: row.other_tel,
                  sms_content: '兴通海运股份有限公司船员部发送《劳动合同》给您，点击签署查看 ' + resU.data.Result.result.shorturl
                }
                API.pushMsg(msgParam).then(resM => {
                  if (resM.data.Code === 10000) {
                    this.getList()
                    this.$Message.success('短信已经成功推送！')
                  } else {
                    this.$Message.error(resM.data.Message)
                  }
                })
              }
            })
          } else {
            this.$Message.warning(resU.data.Result.message)
          }
        })
      }
      // 内部再走一次推送状态提交
      // API.changeSeafarerContractAuditStatus({ seafarer_contract_id: row.seafarer_contract_id, audit_status: 2 }).then(res => {
      //   if (res.data.Code === 10000) {
      //     this.getList()
      //   } else {
      //     this.$Message.error(res.data.Message)
      //   }
      // })
    },
    async handleCopy (row) { // 复制链接地址
      if (row.wpsUrl) { // 阿里云存在的合同
        this.copyUrl = baseUrl + 'contractMobile?id=' + row.seafarer_contract_id
      } else {
        let _url = '/v2/contract/shorturl?contractId=' + row.qiyuessuo_contract_id
        let _param = {
          url: _url,
          method: 'GET'
        }
        await API.requestTransferStation(_param).then(res => {
          if (res.data.Code === 10000) {
            this.copyUrl = res.data.Result.result.shorturl
          } else {
            this.$Message.error(res.data.Message)
          }
        })
      }
      // await navigator.clipboard.writeText(this.copyUrl)
      // this.$Message.success('复制链接成功！')
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(this.copyUrl)
        this.$Message.success('复制链接成功！')
      } else {
        // fallback
        const textarea = document.createElement('textarea')
        textarea.value = this.copyUrl
        textarea.style.position = 'fixed'
        textarea.style.top = '-9999px'
        document.body.appendChild(textarea)
        textarea.focus()
        textarea.select()

        try {
          const successful = document.execCommand('copy')
          document.body.removeChild(textarea)
          this.$Message.success('复制链接成功！')
        } catch (err) {
          document.body.removeChild(textarea)
          this.$Message.success('复制链接失败！')
        }
      }
    },
    handleBreak (row) { // 解约
      this.breakModal.modal = true
      this.breakModal.data = row
    },
    handleDel (row) { // 废弃
      this.$Modal.confirm({
        title: row.seafarer_name + '合同',
        content: '<p>废弃后将无法恢复，确定要废弃？</p>',
        loading: true,
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          if (row.offline_flag === '1') { // 线下走改状态接口 5：已作废
            API.changeSeafarerContractAuditStatus({ seafarer_contract_id: row.seafarer_contract_id, audit_status: 5 }).then(res => {
              this.$Modal.remove()
              if (res.data.Code === 10000) {
                this.getList()
                this.$Message.success(res.data.Message)
              } else {
                this.$Message.error(res.data.Message)
              }
            })
          } else { // 线上走契约锁解约
            let _obj = {
              contractId: row.qiyuessuo_contract_id
            }
            let _param = {
              paramMap: JSON.stringify(_obj),
              url: '/v2/contract/invalid'
            }
            // 废弃
            API.requestTransferStation(_param).then(res => {
              this.$Modal.remove()
              if (res.data.Code === 10000) {
                if (res.data.Result.responseCode === '00000000') {
                  this.$Modal.remove()
                  this.getList()
                  this.$Message.success(res.data.Result.message)
                } else {
                  this.$Message.warning(res.data.Result.message)
                }
              } else {
                this.$Message.error(res.data.Message)
              }
            })
          }
        }
      })
    },
    addBack () { // 新增、修改回调
      this.getList()
    },
    breakBack () { // 解约回调
      this.getList()
    },
    // 查询
    searchResults (e) {
      this.selectList = []
      this.listQuery.seafarer_name = e.seafarer_name
      this.listQuery.crt_duty_id = e.crt_duty_id
      this.listQuery.audit_status = e.audit_status
      this.listQuery.international_flag = e.international_flag
      delete e.target
      this.listQuery.pageIndex = 1
      // localStorage.setItem('shipperQuery', JSON.stringify(this.listQuery))
      this.getList()
    },
    // 重置
    resetResults () {
      this.selectList = []
      this.listQuery = {
        seafarer_name: '',
        crt_duty_id: '',
        audit_status: '',
        international_flag: '',
        pageSize: 10,
        pageIndex: 1
      }
      this.setSearchData.seafarer_name.value = ''
      this.setSearchData.crt_duty_id.selected = ''
      this.setSearchData.audit_status.selected = ''
      this.setSearchData.international_flag.selected = ''
      // localStorage.removeItem('shipperQuery')
      this.getList()
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    }
  },
  created () {
    if (this.isAuditUser) { // 审批人移除最后一个操作项
      this.columns.splice(this.columns.length - 1, 1)
    }
    this.getDutyList()
    this.getList()
  }
})
</script>
<style>
  .add_btn {
    position: absolute;
    right: 16px;
    top: 22px;
  }
  .add_btn .ivu-btn-icon-only {
    width: 32px !important;
    height: 32px !important;
    padding: 0 !important;
    font-size: 16px !important;
    border-radius: 4px !important;
  }
  .list_btn:hover {
    color: #007DFF;
  }
  .demo-spin-icon-load{
    animation: ani-demo-spin 1s linear infinite;
  }
  @keyframes ani-demo-spin {
    from { transform: rotate(0deg);}
    50%  { transform: rotate(180deg);}
    to   { transform: rotate(360deg);}
  }
</style>

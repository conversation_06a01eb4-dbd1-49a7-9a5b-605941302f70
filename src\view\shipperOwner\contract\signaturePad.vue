<template>
  <div class="pad_box">
    <div class="pad_header">
      <h4>手写签名板</h4>
      <Icon style="position: absolute; right: 10px; cursor: pointer;" type="md-close" size="18" @click="closePad" />
    </div>
    <VueSignaturePad width="100vh" height="100vw" ref="signaturePad" class="sign_pad" :options="options"></VueSignaturePad>
    <div class="pad_footer">
      <Button type="primary" size="small" @click="doBack">撤销</Button>
      <Button style="margin-left: 10px;" type="primary" size="small" @click="clearAll">清屏</Button>
      <Button style="position: absolute; right: 10px;" type="primary" size="small" @click="saveUp">保存</Button>
    </div>
  </div>
</template>
<script>

export default ({
  data () {
    return {
      options: {
        minWidth: 5
      }
    }
  },
  methods: {
    closePad () { // 关闭面板
      this.$emit('clsoePad')
    },
    doBack () { // 撤销
      this.$refs.signaturePad.undoSignature()
    },
    clearAll () { // 清屏
      this.$refs.signaturePad.clearSignature()
    },
    saveUp () { // 保存提交
      const { isEmpty, data } =  this.$refs.signaturePad.saveSignature()
      if (isEmpty) {
        this.$Message.warning('请签名后再保存')
      } else {
        this.$emit('saveBack', data)
      }
    }
  }
})
</script>
<style scoped>
  .pad_box {
    background: #fff;
    border-radius: 8px;
  }
  .pad_header {
    position: relative;
    display: flex;
    align-items: center;
    height: 35px;
    border-bottom: 1px solid #ccc;
    padding-left: 10px;
  }
  .sign_pad {
    width: 91vw !important;
    height: 150px !important;
    bottom: 0;
  }
  .pad_footer {
    position: relative;
    display: flex;
    align-items: center;
    height: 45px;
    border-top: 1px solid #ccc;
    padding-left: 10px;
  }
</style>

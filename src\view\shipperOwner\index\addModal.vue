<template>
  <Modal v-model="modalData.modal" :title="modalData.title" width="625" @on-visible-change="modalShowHide" :mask-closable="false">
    <!-- 其他公司服务年限 -->
    <div v-if="modalData.title === '其他公司服务年限'">
      <div class="detail-con">
        <div class="detail-block" v-for="(item, idx) in formData" :key="idx">
          <Form :model="modalData" :label-width="65" style="margin-bottom: 20px;">
            <Row>
              <Col span="12">
                <FormItem label="职务">
                  <Select v-model="item.crt_duty_id" filterable label-in-value @on-change="obj => obj ? item.crt_duty_name = obj.label : ''">
                    <Option v-for="item in crewDutyList" :key="item.id" :value="item.id">{{ item.name }}</Option>
                  </Select>
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="公司">
                  <span><Input type='text' v-model='item.company_name'></Input></span>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="12">
                <FormItem label="船舶">
                  <span><Input type='text' v-model='item.ship_name'></Input></span>
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="时长(月)">
                  <span><Input type='text' v-model='item.months'></Input></span>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <FormItem label="备注">
                <span><Input type='text' v-model='item.bak'></Input></span>
              </FormItem>
            </Row>
          </Form>
        </div>
      </div>
    </div>
    <!-- 证书资质 -->
    <div v-if="modalData.title === '证书资质'">
      <div class="detail-con">
        <div class="detail-block" v-for="(item, idx) in formData" :key="idx">
          <Form :label-width="85" style="margin-bottom: 20px;">
            <Row>
              <Col span="12">
                <FormItem label="证书名称">
                  <Select v-model="item.certificate_key" filterable label-in-value @on-change="obj => obj ? item.certificate_name = obj.label : ''">
                    <Option v-for="item in certificateList" :key="item.id" :value="item.id">{{ item.name }}</Option>
                  </Select>
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="获取时间">
                  <DatePicker type="date" v-model="item.acquisition_time" format="yyyy-MM-dd" @on-change="data=>item.acquisition_time=data" style="width: 100%"></DatePicker>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="12">
                <FormItem label="有效期开始">
                  <DatePicker type="date" v-model="item.validity_start" format="yyyy-MM-dd" @on-change="data=>item.validity_start=data" style="width: 100%"></DatePicker>
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="有效期结束">
                  <DatePicker type="date" v-model="item.validity_end" format="yyyy-MM-dd" @on-change="data=>item.validity_end=data" style="width: 100%"></DatePicker>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <FormItem label="备注">
                <span><Input type='text' v-model='item.bak'></Input></span>
              </FormItem>
            </Row>
          </Form>
        </div>
      </div>
    </div>
    <!-- 外部检查详情 -->
    <div v-if="modalData.title === '外部检查详情'">
      <div class="detail-con">
        <div class="detail-block" v-for="(item, idx) in formData" :key="idx">
          <Form :label-width="65" style="margin-bottom: 20px;">
            <Row>
              <Col span="12">
                <FormItem label="船舶">
                  <Select v-model="item.ship_id" filterable label-in-value @on-change="obj => obj ? item.ship_name = obj.label : ''">
                    <Option v-for="item in shipList" :key="item.ship_id" :value="item.ship_id">{{ item.ship_name }}</Option>
                  </Select>
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="名称">
                  <Select v-model="item.name_key" filterable label-in-value @on-change="obj => obj ? item.inspection_name = obj.label : ''">
                    <Option v-for="item in inspectList" :key="item.id" :value="item.id">{{ item.name }}</Option>
                  </Select>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="12">
                <FormItem label="时间">
                  <DatePicker type="date" v-model="item.inspection_time" format="yyyy-MM-dd" @on-change="data=>item.inspection_time=data" style="width: 100%"></DatePicker>
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="评价">
                  <Select v-model="item.evaluate_key" filterable label-in-value @on-change="obj => obj ? item.evaluate_name = obj.label : ''">
                    <Option v-for="item in evaluateList" :key="item.id" :value="item.id">{{ item.name }}</Option>
                  </Select>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <FormItem label="备注">
                <span><Input type='text' v-model='item.bak'></Input></span>
              </FormItem>
            </Row>
          </Form>
        </div>
      </div>
    </div>
    <!-- 荣誉详情 -->
    <div v-if="modalData.title === '荣誉详情'">
      <div class="detail-con">
        <Form :label-width="55" ref="formValide" :model="formValide" style="margin-bottom: 20px;">
          <div class="detail-block" v-for="(item, idx) in formValide.formData" :key="idx">
            <Row>
              <!-- <Col span="8">
                <FormItem label="船舶">
                  <Select v-model="item.ship_id" filterable label-in-value @on-change="obj => obj ? item.ship_name = obj.label : ''">
                    <Option v-for="item in shipList" :key="item.ship_id" :value="item.ship_id">{{ item.ship_name }}</Option>
                  </Select>
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="航次">
                  <span><Input type='text' v-model='item.voyage_no'></Input></span>
                </FormItem>
              </Col> -->
              <FormItem label="时间" :prop="'formData.' + idx + '.get_time'" :rules="{required: true, message: '时间不能为空', trigger: 'change'}">
                <DatePicker type="year" :clearable="false" format="yyyy" v-model="item.get_time" @on-change="data=>item.get_time=data" style="width: 100%"></DatePicker>
              </FormItem>
            </Row>
            <Row>
              <FormItem label="标题">
                <Select v-model="item.title" filterable label-in-value @on-change="obj => obj ? item.title_name = obj.label : ''">
                  <Option v-for="item in hornorList" :key="item.id" :value="item.id">{{ item.name }}</Option>
                </Select>
              </FormItem>
            </Row>
            <Row>
              <FormItem label="备注">
                <span><Input type='text' v-model='item.bak'></Input></span>
              </FormItem>
            </Row>
          </div>
        </Form>
      </div>
    </div>
    <div slot="footer">
      <Button style="margin-right: 8px" @click="closeModal">取消</Button>
      <Button type="primary" @click="addHandle">保存</Button>
    </div>
  </Modal>
</template>
<script>
import BasicAPI from '@/api/basicData'
import { addSeafarerService, addSeafarerCertificate, addSeafarerInspection, addSeafarerHonor } from '@/api/shipperOwner/index/index'

export default ({
  props: {
    modalData: Object
  },
  data () {
    return {
      formValide: {
        formData: []
      },
      detailId: '', // 船员Id
      formData: [],
      shipList: [], // 船舶列表
      crewDutyList: [], // 职务列表 字典项
      certificateList: [], // 证书列表 字典项
      inspectList: [], // 外部检查列表 字典项
      evaluateList: [], // 外部检查评价列表 字典项
      hornorList: [] // 荣誉列表 字典项
    }
  },
  methods: {
    // 新增条目
    addHandle () {
      let param = this.formData[0]
      if (this.modalData.title === '其他公司服务年限') { // 批量修改保存其他公司服务年限
        addSeafarerService({
          seafarer_id: this.detailId,
          crt_duty_id: param.crt_duty_id,
          company_name: param.company_name,
          ship_name: param.ship_name,
          months: param.months,
          bak: param.bak
        }).then(res => {
          this.$emit('modalDataBack')
          if (res.data.Code === 10000) {
            this.closeModal()
            this.$Message.success(res.data.Message)
          } else {
            this.$Message.error(res.data.Message)
          }
        })
      }
      if (this.modalData.title === '证书资质') { // 批量修改保存船员持有证书
        addSeafarerCertificate({
          seafarer_id: this.detailId,
          certificate_key: param.certificate_key,
          acquisition_time: param.acquisition_time,
          validity_start: param.validity_start,
          validity_end: param.validity_end,
          bak: param.bak
        }).then(res => {
          this.$emit('modalDataBack')
          if (res.data.Code === 10000) {
            this.closeModal()
            this.$Message.success(res.data.Message)
          } else {
            this.$Message.error(res.data.Message)
          }
        })
      }
      if (this.modalData.title === '外部检查详情') { // 批量修改保存外部检查详情
        addSeafarerInspection({
          seafarer_id: this.detailId,
          ship_id: param.ship_id,
          name_key: param.name_key,
          inspection_time: param.inspection_time,
          evaluate_key: param.evaluate_key,
          bak: param.bak
        }).then(res => {
          this.$emit('modalDataBack')
          if (res.data.Code === 10000) {
            this.closeModal()
            this.$Message.success(res.data.Message)
          } else {
            this.$Message.error(res.data.Message)
          }
        })
      }
      if (this.modalData.title === '荣誉详情') { // 批量修改保存荣誉详情
        param = this.formValide.formData[0]
        this.$refs['formValide'].validate((valid) => {
          if (valid) {
            addSeafarerHonor({
              seafarer_id: this.detailId,
              // ship_id: param.ship_id,
              // voyage_no: param.voyage_no,
              get_time: param.get_time,
              title: param.title,
              bak: param.bak
            }).then(res => {
              this.$emit('modalDataBack')
              if (res.data.Code === 10000) {
                this.closeModal()
                this.$Message.success(res.data.Message)
              } else {
                this.$Message.error(res.data.Message)
              }
            })
          }
        })
      }
    },
    // 获取基础数据
    getBasicData () {
      if (this.modalData.title === '荣誉详情' || this.modalData.title === '外部检查详情') {
        BasicAPI.queryCompanyShipList({}).then(res => {
          if (res.data.Code === 10000) {
            this.shipList = res.data.Result
          }
        })
        if (this.modalData.title === '外部检查详情') {
          BasicAPI.queryDictCacheList({ // 外部检查 字典项
            dic_code: 'unCrewInspectionName'
          }).then(res => {
            if (res.data.Code === 10000) {
              this.inspectList = res.data.Result
            }
          })
          BasicAPI.queryDictCacheList({ // 外部检查评价 字典项
            dic_code: 'unCrewEvaluate'
          }).then(res => {
            if (res.data.Code === 10000) {
              this.evaluateList = res.data.Result
            }
          })
        }
        if (this.modalData.title === '荣誉详情') {
          BasicAPI.queryDictCacheList({ // 荣誉列表 字典项
            dic_code: 'unCrewHonorTitle'
          }).then(res => {
            if (res.data.Code === 10000) {
              this.hornorList = res.data.Result
            }
          })
        }
      }
      if (this.modalData.title === '其他公司服务年限') { // 其他公司服务年限
        BasicAPI.queryDictCacheList({
          dic_code: 'unCrewDuty'
        }).then(res => {
          if (res.data.Code === 10000) {
            this.crewDutyList = res.data.Result
          }
        })
      }
      if (this.modalData.title === '证书资质') { // 证书列表 字典项
        BasicAPI.queryDictCacheList({
          dic_code: 'unCrewCertificate'
        }).then(res => {
          if (res.data.Code === 10000) {
            this.certificateList = res.data.Result
          }
        })
      }
    },
    // 关闭模态框
    closeModal () {
      this.modalData.modal = false
      this.formValide.formData = []
      if (this.modalData.title === '荣誉详情') {
        this.$nextTick(() => {
          this.$refs['formValide'].resetFields()
        })
      }
    },
    // 弹窗显隐
    modalShowHide (val) {
      if (val) {
        this.detailId = this.$route.params.id
        this.getBasicData()
      }
      if (!val) {
        this.closeModal()
      }
      this.$nextTick(() => { // 重新赋值 防止接口异步导致数据匹配异常
        if (this.modalData.title === '荣誉详情') {
          this.formValide.formData = this.modalData.data
        } else {
          this.formData = this.modalData.data
        }
      })
    }
  }
})
</script>
<style lang="less">
  .detail-con {
    margin-top: 20px;
    .detail-block {
      margin-top: 10px;
      .block-head {
        display: flex;
        // height: 160px;
        background-color: #EFF8FF;
        border-top: 1px solid #DAE1ED;
        border-right: 1px solid #DAE1ED;
        align-items: center;
        justify-content: center;
        font-weight: bold;
      }
      .detail-other-block {
        font-size: 12px;
        font-weight: 500;
        color: #4A4A4A;
        margin-bottom: 10px;
      }
    }
    .ivu-form {
      border-left: 1px solid #DAE1ED;
      border-bottom: 1px solid #DAE1ED;
    }
    .detail-port-type {
      font-size: 16px;
      color: #333;
      font-weight: 600;
    }
    .detail-name {
      font-size: 16px;
      color: #333;
      font-weight: 600;
      margin-left: 20px;
    }
    .detail-time {
      font-size: 16px;
      color: #333;
      font-weight: 400;
      margin-left: 20px;
    }
    .ivu-form-item {
      margin-bottom: 0;
      // height: 40px;
      // line-height: 40px;
    }
    .ivu-form-item-label {
      padding: 0 10px;
      text-align: left;
      font-weight: 600;
      border: 1px solid #DAE1ED;
      border-bottom: none;
      border-left: none;
      background-color: #EFF8FF;
      // height: 40px;
      // line-height: 40px;
    }
    .ivu-form-item-content {
      border: 1px solid #DAE1ED;
      border-left: none;
      border-bottom: none;
      padding: 0 10px;
      // height: 40px;
      // line-height: 40px;
      // overflow: hidden;
      // text-overflow: ellipsis;
      // white-space: nowrap;
    }
  }
</style>

<template>
  <div>
    <Card>
      <search @searchResults='searchResults' :setSearch='setSearchData' @resetResults='resetResults'></search>
    </Card>
    <Card style="margin-top: 10px">
      <Table border :loading="loading" ref="selection" :columns="columns" :data="list" @on-row-dblclick="handleDetailModal"></Table>
      <Page :styles="{marginTop:'16px',textAlign: 'center'}" :page-size="this.listQuery.pageSize" :current.sync="listCurrent"
        :total="total" prev-text="< 上一页" next-text="下一页 >" @on-change='handleCurrentChange' @on-page-size-change='handleSizeChange'/>
    </Card>
  </div>
</template>
<script>
import search from '_c/search' // 查询组件
import API from '@/api/basicData'
import { queryShipperList } from '@/api/shipperOwner/index/index'
export default {
  components: {
    search
  },
  data () {
    return {
      loading: false,
      total: 0,
      list: [],
      listCurrent: 1,
      listQuery: {
        seafarer_name: '',
        crt_duty_id: '',
        status_key: '',
        pageSize: 10,
        pageIndex: 1
      },
      setSearchData: {
        seafarer_name: {
          type: 'text',
          label: '姓名',
          value: '',
          isdisable: false
        },
        crt_duty_id: {
          type: 'select',
          label: '职务',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 140,
          value: '',
          filterable: true
        },
        status_key: {
          type: 'select',
          label: '状态',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 120,
          value: '',
          filterable: true
        }
      },
      columns: [
        {
          type: 'index',
          width: 60,
          align: 'center'
        },
        {
          title: '船员姓名',
          key: 'seafarer_name',
          align: 'center'
        },
        {
          title: '证书职务',
          key: 'crt_duty_name',
          align: 'center'
        },
        {
          title: '出生日期',
          key: 'birthday',
          align: 'center',
          render: (h, params) => {
            return h('div', params.row.birthday.substring(0, 10))
          }
        },
        {
          title: '入司时间',
          key: 'enter_company_date',
          align: 'center',
          render: (h, params) => {
            return h('div', params.row.enter_company_date.substring(0, 10))
          }
        },
        {
          title: '联系电话',
          key: 'mobile_phone',
          align: 'center'
        },
        {
          title: '状态',
          key: 'status_name',
          align: 'center'
        },
        {
          title: '操作',
          align: 'center',
          render: (h, params) => {
            return h('Tooltip', {
              props: {
                placement: 'bottom',
                theme: 'light'
              }
            }, [
              h('Button', '...'),
              h('div', {
                slot: 'content',
                style: {
                  margin: '0 5px',
                  cursor: 'pointer'
                },
                on: {
                  click: () => {
                    this.handleDetailModal(params.row)
                  }
                }
              }, '详情')
            ])
            // return h('Button', {
            //   props: {
            //     icon: 'md-brush',
            //     size: 'small'
            //   },
            //   on: {
            //     click: () => {
            //       this.handleDetailModal(params.row)
            //     }
            //   }
            // }, '详情')
          }
        }
      ]
    }
  },
  created () {
    if (localStorage.getItem('shipperQuery')) {
      this.listQuery = JSON.parse(localStorage.getItem('shipperQuery'))
      this.setSearchData.seafarer_name.value = this.listQuery.seafarer_name
      this.setSearchData.crt_duty_id.selected = this.listQuery.crt_duty_id
      this.setSearchData.status_key.selected = this.listQuery.status_key
    }
    API.queryDictCacheList({ dic_code: 'unCrewDuty' }).then(res => { // 获取职务
      if (res.data.Code === 10000) {
        res.data.Result.map(item => {
          this.setSearchData.crt_duty_id.selectData.push({
            label: item.entryDesc || item.name,
            value: item.id
          })
        })
      }
    })
    API.queryDictCacheList({ dic_code: 'unCrewStatus' }).then(res => { // 获取状态
      if (res.data.Code === 10000) {
        res.data.Result.map(item => {
          this.setSearchData.status_key.selectData.push({
            label: item.entryDesc || item.name,
            value: item.id
          })
        })
      }
    })
    this.getList()
  },
  methods: {
    // 获取列表
    getList () {
      this.loading = true
      queryShipperList(this.listQuery).then(res => {
        this.loading = false
        if (res.data.Code === 10000) {
          this.list = res.data.Result
          this.total = res.data.Total
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    // 详情
    handleDetailModal (row) {
      sessionStorage.setItem('shipperObj', JSON.stringify(row))
      this.$router.push({
        name: 'shipperOwnerDetail',
        params: {
          id: row.seafarer_id
        }
      })
    },
    // 查询
    searchResults (e) {
      this.listQuery.seafarer_name = e.seafarer_name
      this.listQuery.crt_duty_id = e.crt_duty_id
      this.listQuery.status_key = e.status_key
      delete e.target
      this.listCurrent = 1
      this.listQuery.pageIndex = 1
      localStorage.setItem('shipperQuery', JSON.stringify(this.listQuery))
      this.getList()
    },
    // 重置
    resetResults () {
      this.listCurrent = 1
      this.listQuery = {
        seafarer_name: '',
        crt_duty_id: '',
        status_key: '',
        pageSize: 10,
        pageIndex: 1
      }
      this.setSearchData.seafarer_name.value = ''
      this.setSearchData.crt_duty_id.selected = ''
      this.setSearchData.status_key.selected = ''
      localStorage.removeItem('shipperQuery')
      this.getList()
    },
    // 页面跳转
    handleSizeChange (val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    // 分页跳转
    handleCurrentChange (val) {
      this.listQuery.pageIndex = val
      this.getList()
    }
  }
}
</script>

<template>
  <div>
    <Tabs v-model="curTab">
        <TabPane style="height: calc(100vh - 146px);" v-for="(item, idx) in tabList" :key="'icon' + idx" :label="item.name" :name="item.name">
          <iframe
            :src="item.url"
            width="100%"
            height="100%"
            frameborder="0"
          ></iframe>
        </TabPane>
    </Tabs>
  </div>
</template>
<script>

export default ({
  data () {
    return {
      curTab: '',
      tabList: [
        { name: '港币', url: 'http://bi.xtshipping.com:8061/link/xpikYbwf' },
        { name: '美元', url: 'http://bi.xtshipping.com:8061/link/1nAJWQ8C' },
        { name: '新币', url: 'http://bi.xtshipping.com:8061/link/Ltu0gH95' },
        { name: '欧元', url: 'http://bi.xtshipping.com:8061/link/9uSGTTHu' }
      ]
    }
  },
  methods: {

  },
  created () {
    this.curTab = this.tabList[0].name
  }
})
</script>

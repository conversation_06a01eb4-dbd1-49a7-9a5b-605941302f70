<template>
  <div class="iframe_area">
    <Tabs value="name1" style="height: 100%;">
      <TabPane label="船型" name="name1" icon="md-boat">
        <iframe
          src="http://bi.xtshipping.com:8061/link/F62mws8Z"
          width="100%"
          height="100%"
          frameborder="0"
        ></iframe>
      </TabPane>
      <TabPane label="航线" name="name2" icon="md-planet">
        <iframe
          src="http://bi.xtshipping.com:8061/link/Hva58ZyI"
          width="100%"
          height="100%"
          frameborder="0"
        ></iframe>
      </TabPane>
    </Tabs>
  </div>
</template>

<script>
export default {
  data () {
    return {
      src: ''
    }
  },
  computed: {
    collapsed () {
      return this.$store.state.app.collapsed
    }
  },
  // 组件的其他选项和逻辑
  created () {
  }
}
</script>
<style>
  .iframe_area {
    position: absolute;
    width: 100%;
    height: calc(100% - 20px);
  }
  .iframe_area .ivu-tabs-content {
    height: calc(100% - 30px);
  }
</style>

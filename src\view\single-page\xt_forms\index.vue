<template>
  <div v-if="!isModalShow" class="tab_box">
    <Button class="top_save_btn" type="primary" @click="submitForm">Submit</Button>
    <Tabs type="card" :animated="false" v-model="curShipName" @on-click="tabClick">
      <TabPane v-for="(item, idx) in tableList" :key="item.id" :label="item.xtgf_vesselid_xtgf_shipname" :name="item.xtgf_vesselid_xtgf_shipname">
        <FormList ref="formList" :item="item" :list="item.billentry" @updateBack="formBack"></FormList>
      </TabPane>
    </Tabs>
  </div>
</template>
<script>
import { decryptData, getParams } from '@/libs/util.js'
import FormList from './FormList'
import API from '@/api/erpSys/common'
import { loginGetToken } from '@/api/erpSys/login'

export default ({
  components: {
    FormList
  },
  data () {
    return {
      curShipName: '',
      isFirstLoad: true,
      isPass: false,
      isModalShow: false,
      token: '',
      access_token: '',
      param: {},
      tableList: []
    }
  },
  methods: {
    getList () {
      this.tableList = []
      let _url = '/ierp/kapi/v2/xtgf/pm/pm_purapplybill/purchasemonthsearch?pageSize=10&pageNo=1&biztime=' + this.param.t + '&xtgf_supplier_number=' + this.param.n + '&access_token=' + this.access_token
      let dataParam = {
        url: _url,
        user: 'cengyabin'
      }
      API.transferStationGet(dataParam).then(res => {
        if (!res) {
          this.$Message.warning('Please re-get the order address!')
        } else if (res.data.errorCode === '0') {
          if (res.data.data.totalCount && res.data.data.totalCount > 0) {
            this.tableList = res.data.data.rows
            if (this.tableList.length > 0 && this.isFirstLoad) this.curShipName = this.tableList[0].xtgf_vesselid_xtgf_shipname
            this.isFirstLoad = false
          } else {
            this.$Message.warning('No data!')
          }
        } else {
          if (res.data.errorCode === '401') {
            this.$Message.warning('Please re-get the order address!')
          } else {
            this.$Message.error('The data gets an error, please contact the administrator!')
          }
        }
      })
    },
    formBack () {
      this.getList()
    },
    analysisParam () { // 地址参数解析
      const query = this.$route.fullPath.split('?')[1]
      const _param = decryptData(decodeURIComponent(query))
      this.param = getParams('xt_forms?' + _param)
      this.getList()
    },
    checkToken () { // 验证token的有效性
      let dataParam = {
        data: {},
        url: '/ierp/kapi/v2/xtgf/pm/pm_purapplybill/purchaseedit',
        user: 'cengyabin'
      }
      API.transferStationOut(dataParam).then(res => {
        if (res.data.Code === -202) {
          this.$Message.error('error key!')
          localStorage.removeItem('token')
          setTimeout(() => {
            this.showTipModal()
          }, 500)
        } else {
          loginGetToken({ user:'cengyabin' }).then(res => {
            if (res.data.Code === 10000) {
              this.isModalShow = false
              this.access_token = res.data.access_token
              this.analysisParam()
            } else {
              this.$Message.error('login error!')
            }
          })
        }
      })
    },
    submitForm () {
      let tabIndex = this.tableList.findIndex(item => item.xtgf_vesselid_xtgf_shipname === this.curShipName)
      this.$refs['formList'][tabIndex].handleSubmit()
    },
    tabClick (val) {
      this.curShipName = val
      let tabIndex = this.tableList.findIndex(item => item.xtgf_vesselid_xtgf_shipname === val)
      this.$refs['formList'][tabIndex].adjustAllHeights()
    },
    showTipModal () {
      this.isModalShow = true
      this.$Modal.confirm({
        okText: 'OK',
        cancelText: 'Cancel',
        onOk: () => {
          this.checkToken()
        },
        render: (h) => {
          return h('Input', {
            props: {
              value: this.value,
              autofocus: true,
              placeholder: 'Please enter your key...'
            },
            on: {
              input: (val) => {
                if (val) {
                  localStorage.setItem('token', val)
                }
              }
            }
          })
        }
      })
    }
  },
  created () {
    const token = localStorage.getItem('token')
    // analysisParam() // test
    if (!token) { // 未找到token，弹窗提示输入 2025-04-08 关闭验证
      this.showTipModal()
    } else {
      // 验证token是否有效
      this.checkToken()
    }
    window.document.title = 'xingtong-forms'
    // console.log(this.param)
  }
})
</script>
<style>
  .tab_box {
    padding: 10px;
    font-size: 10px !important;
    font-family: Verdana, Arial, Helvetica, sans-serif;
  }
  .tab_box .ivu-tabs-nav-container {
    font-size: 10px !important;
  }
  .top_save_btn {
    position: fixed;
    background: #204681 !important;
    top: 5px;
    left: 1140px;
    z-index: 99999;
  }
  /* .tab_box > .ivu-tabs-card > .ivu-tabs-content {
    height: 120px;
    margin-top: -16px;
  }

  .tab_box > .ivu-tabs-card > .ivu-tabs-content > .ivu-tabs-tabpane {
    background: #204681;
    color: #fff;
  } */

  .tab_box > .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab {
    background: #204681;
    color: #fff;
    border-color: transparent;
  }

  .tab_box > .ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab-active {
    background: #204681;
    color: #F1673A !important;
  }
</style>

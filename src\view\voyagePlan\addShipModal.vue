<template>
  <Modal v-model="modalData.modal" width="200" title="添加船舶" @on-visible-change="modalShowHide" :mask-closable="false">
    <Select v-model="ship_name" label-in-value filterable @on-change="selectChange">
      <Option v-for="item in modalData.data" :value="item.ship_id" :key="item.ship_id">{{ item.ship_name }}</Option>
    </Select>
    <div slot="footer">
      <Button @click="handleCancel">取消</Button>
      <Button @click="handleSave" type="primary">保存</Button>
    </div>
  </Modal>
</template>
<script>
export default {
  props: {
    modalData: Object
  },
  data () {
    return {
      ship_name: '',
      shipObj: {}
    }
  },
  methods: {
    handleSave () {
      this.clearData()
      this.$emit('shipBack', this.shipObj)
    },
    handleCancel () {
      this.clearData()
    },
    clearData () {
      this.modalData.modal = false
      this.ship_name = ''
    },
    modalShowHide (val) {
      
    },
    selectChange (obj) {
      this.shipObj = obj
    }
  }
}
</script>
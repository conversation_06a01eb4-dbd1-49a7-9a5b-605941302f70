<template>
  <div class="area_con">
    <Spin v-if="isLoading" fix style="background: rgba(255,255,255,1); z-index: 99999;">
      <img style="margin-top: -100px;" :src="LoadingImg" />
      <div>加载中...</div>
    </Spin>
    <DetailModal :modalData="detailModal"></DetailModal>
    <Modal title="查询" class="drawer_con" v-model="isSearch" width="100" placement="bottom">
      <Form label-position="left">
        <Form-item label="日期：">
          <DatePicker v-model="plan_month" type="month" placeholder="请选择月份" style="width: 250px;" @on-change="dateChange"></DatePicker>
        </Form-item>
        <Form-item label="船舶：">
          <Select v-model="ship_id_arr" multiple filterable style="width:250px;">
            <Option v-for="item in tabList" :value="item.ship_id" :key="item.ship_id">{{ item.ship_name }}</Option>
          </Select>
        </Form-item>
      </Form>
      <div slot="footer">
        <Button type="primary" @click="handleSave">查询</Button>
        <Button type="info" @click="handleReset">重置</Button>
        <Button @click="handleCancel">取消</Button>
      </div>
    </Modal>
    <div class="search_area" @click="searchShow">
      <Icon type="ios-search" size="20" color="#f1fafb"/>
    </div>
    <div style="margin: 10px;text-align: center;" v-if="tabList.length === 0">
      暂无数据……
    </div>
    <Badge v-else class="list_badge" v-for="(item, idx) in tabList" :key="'badge' + idx" :count="parseInt(item.not_read_num)" style="width: 100%;">
      <Collapse>
        <Panel name="'panel' + idx">
          <span style="font-size: 1rem; font-weight: bold;">{{ item.ship_name }}</span>
          <div slot="content" v-if="item.planList && item.planList.length > 0">
            <div class="row_body" v-for="(list, index) in item.planList" :key="item.ship_id + index" @click="detailClick(list)">
              <Badge v-if="list.is_modify === '1' && list.is_read === '0'" class="plan_list_badge" text="new"></Badge>
              <Row>
                <Col span="8">
                  <span class="detail_title">航次：</span>
                  <span class="detail_con">V.{{ list.voyage_no }}</span>
                </Col>
                <Col span="16">
                  <span class="detail_title">航线：</span>
                  <span class="detail_con">{{ list.load_port_names }} ~ {{ list.unload_port_names }}</span>
                </Col>
              </Row>
              <Row class="detail_row">
                <Col span="8">
                  <div class="detail_title">受载日期：</div>
                  <div class="detail_con">{{ list.loading_date[0].substring(5,10) }} - {{ list.loading_date[1].substring(5,10) }}</div>
                </Col>
                <Col span="11">
                  <div class="detail_title">货品：</div>
                  <div class="detail_con">{{ list.goods_names }}</div>
                </Col>
                <Col span="5">
                  <div class="detail_title">货量：</div>
                  <div class="detail_con">{{ list.goods_amount }}吨</div>
                </Col>
              </Row>
            </div>
          </div>
        </Panel>
      </Collapse>
    </Badge>
  </div>
</template>

<script>
import API from '@/api/voyagePlan'
import basicAPI from '@/api/basicData'
import search from '_c/search' // 查询组件
import DetailModal from './detail_modal'
import LoadingImg from '@/assets/images/loading.gif'

export default {
  components: {
    search,
    DetailModal
  },
  data () {
    return {
      LoadingImg,
      isLoading: true,
      defaultMonth: null,
      isSearch: false,
      plan_month: '',
      ship_id_arr: [],
      tabList: [],
      shipList: [],
      openId: 'test',
      detailModal: {
        modal: false,
        title: '',
        data: {}
      },
      listQuery: {
        plan_month: '', // 月份
        ship_id_in: '' // 船舶id
      },
      setSearchData: {
        date: {
          type: 'month',
          label: '日期',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: '100vw',
          value: '',
          filterable: true
        },
        ship_id: {
          type: 'selectMultiple',
          label: '船舶',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: '100vw',
          value: '',
          filterable: true
        }
      }
    }
  },
  created () {
    let _that = this
    let _param = this.$route.query.plan_month
    this.defaultMonth = this.getCurrentYearMonth()
    if (_param) {
      this.plan_month = _param
      this.listQuery.plan_month = _param
      this.setSearchData.date.selected = _param
    } else {
      this.plan_month = this.defaultMonth
      this.listQuery.plan_month = this.defaultMonth
      this.setSearchData.date.selected = this.defaultMonth
    }
    qing.call('getPersonInfo', { // 针对云之家小程序限定
      success: function (res) {
        if (res.data.openId && res.data.openId !== '') {
          _that.openId = res.data.openId
          _that.getList()
        } else {
          _that.getList()
        }
      },
      error: function (err) {
        _that.getList()
      }
    })
  },
  methods: {
    getList () {
      Object.assign(this.listQuery, {
        open_id: this.openId
      })
      API.queryGroupVmpPlanList(this.listQuery).then(res => {
        if (res.data.Code === 10000) {
          this.tabList = res.data.Result
          setTimeout(() => {
            this.isLoading = false
          }, 1500)
        }
      })
    },
    getBasicList () { // 获取基础数据
      basicAPI.queryBasicShipList({ business_model_in: '1,2,3' }).then(res => {
        if (res.data.Code === 10000) {
          this.shipList = res.data.Result
          this.setSearchData.ship_id.selectData = this.shipList.map(item => {
            return {
              value: item.ship_id,
              label: item.ship_name
            }
          })
        }
      })
    },
    detailClick (list) {
      if (list.is_modify === '1' && list.is_read === '0') { // 有修改过的才走已读接口
        let _readParam = {
          open_id: this.openId,
          vmp_plan_id: list.vmp_plan_id
        }
        API.addVmpUserRead(_readParam).then(res => {
          if (res.data.Code === 10000) {
            this.getList()
          }
        })
      }
      this.detailModal = {
        modal: true,
        title: list.ship_name + '- V.' + list.voyage_no + '月度计划详情',
        data: list
      }
    },
    searchShow () {
      this.isSearch = true
    },
    selectOnChanged (e) {
      if (e.flag === 'month_start') {
        this.listQuery.plan_month = e.key
      }
    },
    // 获取当前年月
    getCurrentYearMonth() {
      const today = new Date()
      const currentDay = today.getDate()
      const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0).getDate() // 本月最后一天
      const daysLeftInMonth = lastDayOfMonth - currentDay  // 本月最后一天 - 当前日期（天）
      let currentYear, currentMonth

      if (daysLeftInMonth <= 5) { // 如果是月底 则默认是下个月 与月底差5天情况下则视为月底
        const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1)
        // const nextMonth = new Date(2024, 12, 1)
        currentYear = nextMonth.getFullYear()
        currentMonth = nextMonth.getMonth() + 1
      } else { // 如果非月底，则默认本月
        currentYear = today.getFullYear()
        currentMonth = today.getMonth() + 1
      }
      let month = currentMonth < 10 ? '0' + currentMonth : currentMonth
      return currentYear + '-' + month
    },
    dateChange (e) {
      this.listQuery.plan_month = e
    },
    handleSave () { // 弹窗保存
      if (this.ship_id_arr.length > 0) {
        this.listQuery.ship_id_in = this.ship_id_arr.join()
      }
      this.isSearch = false
      this.getList()
    },
    handleReset () { // 弹窗重置
      this.plan_month = this.defaultMonth
      this.listQuery.plan_month = this.defaultMonth
      this.listQuery.ship_id_in = ''
      this.ship_id_arr = []
    },
    handleCancel () { // 弹窗取消
      this.isSearch = false
    },
    // 查询
    searchResults (e) {
      this.listQuery.ship_id_in = e.ship_id ? e.ship_id.join() : e.ship_id
      this.getList()
    },
    // 重置
    resetResults () {
      this.plan_month = this.defaultMonth
      this.listQuery = {
        plan_month: this.defaultMonth,
        ship_id_in: ''
      }
      this.setSearchData.date.selected = this.defaultMonth
      this.setSearchData.ship_id.selected = ''
      this.getList()
    }
  }
}
</script>
<style>
  .area_con {
    /* background: linear-gradient(to bottom, #145ae8, #b4d2ff); */
    margin: 0 -5px;
  }
  .search_area {
    position: absolute;
    width: 2rem;
    height: 2rem;
    line-height: 2rem;
    border-radius: 1rem;
    bottom: 1rem;
    right: 0.5rem;
    border: 1px solid #f7f7f7;
    background: #7ea6f4;
    text-align: center;
    z-index: 9999;
  }
  .row_title, .row_body {
    text-align: left;
    padding-left: 0.5rem;
    border-bottom: 1px solid #e8e8e8;
  }
  .row_body {
    min-height: 1.6rem;
    line-height: 1.6rem;
    margin: 0.1rem 0;
    padding: 0.5rem;
  }
  .row_body:last-child {
    border-bottom: none;
  }
  .row_body:nth-child(odd) {
    background: #ddf6fa;
  }
  .row_body .ivu-col {
    font-size: 0.6rem;
  }
  .row_body .ivu-collapse-content {
    padding: 0 !important;
  }
  .ivu-collapse-content {
    padding: 0 !important;
  }
  .list_badge {
    margin-bottom: 10px;
  }
  .list_badge .ivu-badge-count {
    right: 1rem !important;
    top: 0.5rem !important;
  }
  .drawer_con .ivu-modal-header {
    background: #4993fa;
  }
  .drawer_con .ivu-modal-header p, .drawer_con .ivu-modal-header-inner {
    color: #fff;
  }
  .drawer_con .ivu-modal-close .ivu-icon-ios-close {
    color: #fff;
  }
  .detail_row {
    margin-bottom: 0.1rem;
  }
  .detail_title {
    font-size: 0.7rem;
    color: #B0B1B3;
  }
  .detail_con {
    font-size: 0.9rem;
    color: #333;
  }
  .ivu-collapse {
    background-color: #75c2f6;
    border-radius: 3px;
    color: #fff;
    border: 1px solid #6fa7f3;
  }
  .ivu-collapse-header {
    background: linear-gradient(to right, #4993fa, #9dc6ff);
    border: none;
    color: #fff !important;
  }
  .ivu-collapse-content {
    background: #f1fafb;
  }
  .plan_list_badge {
    position: absolute;
    right: 5px;
    margin-top: -10px;
  }
</style>
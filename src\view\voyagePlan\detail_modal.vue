<template>
  <Modal class="mobile_modal" v-model="modalData.modal" :title="modalData.title" @on-visible-change="modalShowHide">
    <Row class="detail_row">
      <Col span="8">
        <div class="detail_title">
          船舶：
          <Poptip v-if="isShipChanged" class="pop_tip" placement="top-start">
            <Icon color="red" type="ios-help-circle" size="12" />
            <span slot="content">{{ prevData.ship_name }}</span>
          </Poptip>
        </div>
        <div class="detail_con">{{ modalData.data.ship_name }}</div>
      </Col>
      <Col span="8">
        <div class="detail_title">
          航次：
          <Poptip v-if="isVoyageChanged" class="pop_tip" placement="top-start">
            <Icon color="red" type="ios-help-circle" size="12" />
            <span slot="content">{{ prevData.voyage_no }}</span>
          </Poptip>
        </div>
        <div class="detail_con">V.{{ modalData.data.voyage_no }}</div>
      </Col>
      <Col span="8">
        <div class="detail_title">
          航线：
          <Poptip v-if="isLineChanged" class="pop_tip" placement="top-start">
            <Icon color="red" type="ios-help-circle" size="12" />
            <span slot="content">{{ prevData.load_port_names }} - {{ prevData.unload_port_names }}</span>
          </Poptip>
        </div>
        <div class="detail_con">{{ modalData.data.load_port_names }} - {{ modalData.data.unload_port_names }}</div>
      </Col>
    </Row>
    <Row class="detail_row" v-if="modalData.modal">
      <Col span="8">
        <div class="detail_title">
          货品：
          <Poptip v-if="isGoodsChanged" class="pop_tip" placement="top-start">
            <Icon color="red" type="ios-help-circle" size="12" />
            <span slot="content">{{ prevData.goods_names }}</span>
          </Poptip>
        </div>
        <div class="detail_con">{{ modalData.data.goods_names }}</div>
      </Col>
      <Col span="8">
        <div class="detail_title">
          货量：
          <Poptip v-if="isAmountChanged" class="pop_tip" placement="top-start">
            <Icon color="red" type="ios-help-circle" size="12" />
            <span slot="content">{{ prevData.goods_amount }}吨</span>
          </Poptip>
        </div>
        <div class="detail_con">{{ modalData.data.goods_amount }}吨</div>
      </Col>
      <Col span="8">
        <div class="detail_title">
          受载日期：
          <Poptip v-if="isDateChanged" class="pop_tip" placement="top-start">
            <Icon color="red" type="ios-help-circle" size="12" />
            <span slot="content">{{ prevData.loading_date[0].substring(5,10) }} - {{ prevData.loading_date[1].substring(5,10) }}</span>
          </Poptip>
        </div>
        <div class="detail_con">{{ modalData.data.loading_date[0].substring(5,10) }} - {{ modalData.data.loading_date[1].substring(5,10) }}</div>
      </Col>
    </Row>
    <Row class="detail_row" v-if="modalData.modal">
      <Col span="12">
        <div class="detail_title">
          装港码头：
          <Poptip v-if="isLoadWharfChanged" class="pop_tip" placement="top-start">
            <Icon color="red" type="ios-help-circle" size="12" />
            <span slot="content">{{ prevData.load_wharf_names || '-' }}</span>
          </Poptip>
        </div>
        <div class="detail_con">{{ modalData.data.load_wharf_names || '-' }}</div>
      </Col>
      <Col span="12">
        <div class="detail_title">
          卸港码头：
          <Poptip v-if="isunLoadWharfChanged" class="pop_tip" placement="top-start">
            <Icon color="red" type="ios-help-circle" size="12" />
            <span slot="content">{{ prevData.unload_wharf_names || '-' }}</span>
          </Poptip>
        </div>
        <div class="detail_con">{{ modalData.data.unload_wharf_names || '-' }}</div>
      </Col>
    </Row>
    <Row class="detail_row">
      <Col span="12">
        <div class="detail_title">
          托运方：
          <Poptip v-if="isRecipChanged" class="pop_tip" placement="top-start">
            <Icon color="red" type="ios-help-circle" size="12" />
            <span slot="content">{{ prevData.recipient_company_name || '-' }}</span>
          </Poptip>
        </div>
        <div class="detail_con">{{ modalData.data.recipient_company_name || '-' }}</div>
      </Col>
      <Col span="12">
        <div class="detail_title">
          装/卸港特殊要求：
          <Poptip v-if="isPortChanged" class="pop_tip" placement="top-start">
            <Icon color="red" type="ios-help-circle" size="12" />
            <span slot="content">
              <div>装港：{{ modalData.data.load_require || '-' }}</div>
              <div>卸港：{{ modalData.data.unload_require || '-' }}</div>
            </span>
          </Poptip>
        </div>
        <div class="detail_con">
          <div>装港：{{ modalData.data.load_require || '-' }}</div>
          <div>卸港：{{ modalData.data.unload_require || '-' }}</div>
        </div>
      </Col>
    </Row>
    <div>
      <div class="detail_title">
        备注：
        <Poptip v-if="isRemarksChanged" class="pop_tip" placement="top-start">
          <Icon color="red" type="ios-help-circle" size="12" />
          <span slot="content">{{ prevData.remarks || '-' }}</span>
        </Poptip>
      </div>
      <div class="detail_con">{{ modalData.data.remarks || '-' }}</div>
    </div>
    <div slot="footer">
      <Button @click="closeModal">关闭</Button>
    </div>
  </Modal>
</template>
<script>
import API from '@/api/voyagePlan'

export default {
  props: {
    modalData: Object
  },
  data () {
    return {
      prevData: {},
      isShipChanged: false,
      isVoyageChanged: false,
      isLineChanged: false,
      isGoodsChanged: false,
      isAmountChanged: false,
      isDateChanged: false,
      isLoadWharfChanged: false,
      isunLoadWharfChanged: false,
      isRecipChanged: false,
      isPortChanged: false,
      isRemarksChanged: false
    }
  },
  methods: {
    getPlanDetail () { // 获取详情数据
      API.queryVmpPlanDetail({ vmp_plan_id: this.modalData.data.vmp_plan_id }).then(res => {
        if (res.data.Code === 10000) {
          if (res.data.PreviousPlan) {
            this.prevData = res.data.PreviousPlan
            if (res.data.CurrentPlan.is_read === '0') {
              this.isShipChanged = this.modalData.data.ship_name !== this.prevData.ship_name
              this.isVoyageChanged = this.modalData.data.voyage_no !== this.prevData.voyage_no
              this.isLineChanged = (this.modalData.data.load_port_names !== this.prevData.load_port_names || this.modalData.data.unload_port_names !== this.prevData.unload_port_names)
              this.isGoodsChanged = this.modalData.data.goods_names !== this.prevData.goods_names
              this.isAmountChanged = this.modalData.data.goods_amount !== this.prevData.goods_amount
              this.isDateChanged = (this.modalData.data.loading_date[0] !== this.prevData.loading_date[0] || this.modalData.data.loading_date[1] !== this.prevData.loading_date[1])
              this.isLoadWharfChanged = this.modalData.data.load_wharf_names !== this.prevData.load_wharf_names
              this.isunLoadWharfChanged = this.modalData.data.unload_wharf_names !== this.prevData.unload_wharf_names
              this.isRecipChanged = this.modalData.data.recipient_company_name !== this.prevData.recipient_company_name
              this.isPortChanged = (this.modalData.data.load_require !== this.prevData.load_require || this.modalData.data.unload_require !== this.prevData.unload_require)
              this.isRemarksChanged = this.modalData.data.remarks !== this.prevData.remarks
            } else {
              this.isShipChanged = false
              this.isVoyageChanged = false
              this.isLineChanged = false
              this.isGoodsChanged = false
              this.isAmountChanged = false
              this.isDateChanged = false
              this.isLoadWharfChanged = false
              this.isunLoadWharfChanged = false
              this.isRecipChanged = false
              this.isPortChanged = false
              this.isRemarksChanged = false
            }
          }
        } else {
          this.$Message.error(res.data.Message)
        }
      })
    },
    closeModal () {
      this.modalData.modal = false
    },
    modalShowHide (val) {
      if (val) {
        if (this.modalData.data.is_modify === '1') { // 判断是否有修改过，有修改要判断哪些修改过
          this.getPlanDetail()
        }
      }
    }
  }
}
</script>
<style>
  .mobile_modal .detail_row {
    margin-bottom: 0.5rem;
  }
  .mobile_modal .detail_title {
    font-size: 0.6rem;
    color: #B0B1B3;
  }
  .mobile_modal .detail_con {
    font-size: 0.8rem;
    color: #333;
    margin-top: 0.1rem;
  }
  .mobile_modal .ivu-modal-header {
    background: #4993fa !important;
  }
  .mobile_modal .ivu-modal-header p, .mobile_modal .ivu-modal-header-inner {
    color: #fff;
  }
  .mobile_modal .ivu-modal-close .ivu-icon-ios-close {
    color: #fff;
  }
</style>
<template>
  <div>
    <!-- <input v-model="searchQuery" @input="searchPdf" placeholder="搜索..."> -->
    <pdf
        v-for="i in numPages"
        :key="i"
        :src="src"
        :page="i"
        @onPassword="getPassWord"></pdf>
  </div>
</template>

<script>
import pdf from 'vue-pdf'
var loadingTask = pdf.createLoadingTask('https://file.shipformula.com/2024/08/06/db0e552053cf11efa7e705773359f602.pdf')

export default ({
  components: {
    pdf
  },
  data () {
    return {
      src: loadingTask,
      numPages: 0
    }
  },
  methods: {
    getPassWord (updatePassword, reason) {
      updatePassword(prompt('密码是test'))
    },
    async loadPdf (pdfUrl) { // 初始化加载pdf
      this.pdfDoc = await getDocumtent(pdfUrl).promise
      this.renderPage(1)
    },
    async renderPage (pageNum) {
      const page = await this.pdfDoc.getPage(pageNum)
      const viewport = page.getViewPort({scale: 1.5})
      const canvas = this.$refs.pdfCanvas
      const context = canvas.getContext('2d')
      canvas.width = viewport.width
      canvas.height = viewport.height

      const renderContext = {
        canvasContext: context,
        viewport: viewport
      }
      await page.render(renderContext).promise
    },
    async searchPdf () {
      // if (!this.searchQuery) return
      // const pages = this.pdfDoc.numPages
      // for (let pageNum = 1; pageNum < pages; pageNum++) {
      //   const page = await this.pdfDoc.getPage(pageNum)
      //   const textContent = await page.getTextContent()
      //   const items = textContent.items.map(item => item.str).join(' ')
      //   if (items.includes(this.searchQuery)) {
      //     this.renderPage(pageNum)
      //     console.log('找到关键字在第' + pageNum + '页')
      //     break
      //   }
      // }
    }
  },
  mounted () {
    // this.loadPdf('https://file.shipformula.com/2024/08/06/db0e552053cf11efa7e705773359f602.pdf')
    this.src.promise.then(pdf => {
      this.numPages = pdf.numPages
    })
  }
})
</script>

<template>
  <div>
    <Card>
      <search @searchResults='searchResults' :setSearch='setSearchData' @selectOnChanged='selectOnChanged' @resetResults='resetResults'></search>
    </Card>
    <Card>
      <div v-if="tabList.length === 0">
        暂无数据……
        <Button style="margin-left: 10px;" type="primary" @click="handleTabsAdd" size="small">添加船舶</Button>
      </div>
      <Tabs v-else type="card" closable :before-remove="onBeforeTabRemove" v-model="curTab">
        <TabPane v-for="(item,idx) in tabList" :key="'tab' + idx" :label="item.ship_name">
          <Table border :loading="loading" ref="selection" :columns="columns" :data="item.planList"></Table> <!-- show-summary-->
          <Button class="plan_add_btn" type="primary" @click="addPlan(item)">添加计划</Button>
        </TabPane>
        <div slot="extra">
          <Button type="primary" @click="handleSendMess" size="small">推送消息</Button>
          <Button style="margin-left: 10px;" type="primary" @click="handleTabsAdd" size="small">添加船舶</Button>
        </div>
      </Tabs>
    </Card>
    <AddShipModal :modalData="addShipModal" @shipBack="addShipBack"></AddShipModal>
    <AddPlanList :modalData="addPlanModal" @planBack="addPlanBack"></AddPlanList>
    <PushSetModal :modalData="pushSetModal" ></PushSetModal>
  </div>
</template>
<script>
import search from '_c/search' // 查询组件
import AddShipModal from './addShipModal'
import AddPlanList from './addPlanModal'
import PushSetModal from './pushSetModal'
import basicAPI from '@/api/basicData'
import API from '@/api/voyagePlan'

export default ({
  components: {
    search,
    AddShipModal,
    AddPlanList,
    PushSetModal
  },
  data () {
    return {
      defaultMonth: null,
      loading: false,
      curTab: 0,
      list: [],
      listQuery: {
        plan_month: '', // 月份
        ship_id: '', // 船舶id
        business_model: '' // 船舶类型
      },
      shipList: [],
      tabList: [],
      addShipModal: {
        modal: false,
        data: []
      },
      addPlanModal: {
        modal: false,
        type: '',
        title: '',
        shipList: [],
        data: {}
      },
      pushSetModal: {
        modal: false,
        title: ''
      },
      setSearchData: {
        date: {
          type: 'month',
          label: '日期',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 140,
          value: '',
          filterable: true
        },
        ship_id: {
          type: 'select',
          label: '船舶',
          selectData: [],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 120,
          value: '',
          filterable: true
        },
        ship_type: {
          type: 'select',
          label: '船舶类型',
          selectData: [{value: 1, label: '自运'}, {value: 2, label: '期租'}, {value: 3, label: '国际'}],
          selected: '',
          placeholder: '请选择',
          selectName: '',
          width: 120,
          value: '',
          filterable: true
        }
      },
      columns: [
        {
          title: '受载期',
          key: 'loading_date',
          align: 'center',
          render: (h, params) => {
            let dateStr = params.row.loading_date
            if (params.row.loading_date.length === 2) {
              let dateArr = params.row.loading_date
              dateStr = dateArr[0].substring(5, 10) + ' - ' + dateArr[1].substring(5, 10)
            }
            return h('div', {}, dateStr)
          }
        },
        {
          title: '航次',
          key: 'voyage_no',
          align: 'center',
          render: (h, params) => {
            return h('div', {}, 'V.' + params.row.voyage_no)
          }
        },
        {
          title: '航线',
          key: '',
          align: 'center',
          render: (h, params) => {
            let voyageLine = params.row.load_port_names + ' - ' + params.row.unload_port_names
            return h('div', {}, voyageLine)
          }
        },
        {
          title: '货品',
          key: 'goods_names',
          align: 'center'
        },
        {
          title: '货量(吨)',
          key: 'goods_amount',
          align: 'center'
        },
        {
          title: '客户',
          key: 'recipient_company_name',
          align: 'center'
        },
        {
          title: '装港要求',
          key: 'load_require',
          align: 'center'
        },
        {
          title: '卸港要求',
          key: 'unload_require',
          align: 'center'
        },
        {
          title: '备注',
          key: 'remarks',
          align: 'center'
        },
        {
          title: '操作',
          key: '',
          width: 200,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                style: {
                  margin: '0 5px'
                },
                props: {
                  icon: 'md-brush',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleModify(params.row)
                  }
                }
              }, '修改'),
              h('Button', {
                style: {
                  margin: '0 5px'
                },
                props: {
                  icon: 'md-brush',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.handleDel(params.row)
                  }
                }
              }, '删除')
            ])
          }
        }
      ]
    }
  },
  async created () {
    this.defaultMonth = this.getCurrentYearMonth()
    this.setSearchData.date.selected = this.defaultMonth
    this.listQuery.plan_month = this.defaultMonth
    await this.getBasicList()
    this.getList()
  },
  methods: {
    getList () { // 获取列表
      this.loading = true
      API.queryGroupVmpPlanList(this.listQuery).then(res => {
        this.loading = false
        if (res.data.Code === 10000) {
          this.tabList = res.data.Result
          if (this.listQuery.ship_id !== '' || this.listQuery.business_model !== '') return
          let selfShipList = this.shipList.filter(item => item.business_model === '1' && !item.ship_name.includes('善')) // 拉取自营的船舶数据 剔除万邦船舶
          if (this.tabList.length > 0) { // 如果船期表里有船舶，需要剔除掉再自动引入其他船舶
            let resetList = selfShipList.filter(item1 => {
              return !this.tabList.some(item2 => item2.ship_id === item1.ship_id)
            })
            resetList.map(item => {
              this.tabList.push({
                ship_id: item.ship_id,
                ship_name: item.ship_name,
                planList: []
              })
            })
          } else { // 如果船期表里没数据，则全部帮忙添加上去
            selfShipList.map(item => {
              this.tabList.push({
                ship_id: item.ship_id,
                ship_name: item.ship_name,
                planList: []
              })
            })
          }
        }
      })
    },
    async getBasicList () { // 获取基础数据
      await basicAPI.queryBasicShipList({ business_model_in: '1,2,3' }).then(res => {
        if (res.data.Code === 10000) {
          this.shipList = res.data.Result
          this.addShipModal.data = res.data.Result
          this.setSearchData.ship_id.selectData = this.shipList.map(item => {
            return {
              value: item.ship_id,
              label: item.ship_name
            }
          })
        }
      })
    },
    addShipBack (obj) { // 添加船舶回调
      let isInTab = this.tabList.some(item => item.ship_id === obj.value) // 判断该船是否存在
      if (isInTab) {
        this.$Message.warning('该船已存在列表中！')
        return
      }
      this.tabList.push({
        ship_id: obj.value,
        ship_name: obj.label,
        planList: []
      })
    },
    addPlan (item) { // 添加当前船舶计划
      this.addPlanModal = {
        modal: true,
        title: item.ship_name + '-月度计划新增',
        type: 'add',
        shipList: this.shipList,
        ship_id: item.ship_id,
        data: {}
      }
    },
    addPlanBack () { // 添加计划回调
      this.getList()
    },
    handleModify (row) {
      this.addPlanModal = {
        modal: true,
        title: row.ship_name + '-月度计划修改',
        type: 'modify',
        shipList: this.shipList,
        ship_id: row.ship_id,
        data: row
      }
    },
    handleDel (row) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确定要删除本条数据？</p>',
        loading: true,
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          API.delVmpPlan({ vmp_plan_id: row.vmp_plan_id }).then(res => {
            if (res.data.Code === 10000) {
              this.$Message.success(res.data.Message)
              this.$Modal.remove()
              this.getList()
            } else {
              this.$Message.error(res.data.Message)
            }
          })
        }
      })
    },
    handleSendMess () { // 推送消息
      if (this.listQuery.plan_month === '') {
        this.$Message.warning('请先选择推送日期！')
        return
      }
      this.pushSetModal = {
        modal: true,
        plan_month: this.listQuery.plan_month,
        title: '推送消息'
      }
      // API.sendTipsToYunZhiJia({ plan_month: this.listQuery.plan_month }).then(res => {
      //   if (res.data.Code === 10000) {
      //     this.$Message.success(res.data.Message)
      //   } else {
      //     this.$Message.error(res.data.Message)
      //   }
      // })
    },
    handleTabsAdd () { // 添加新船
      this.addShipModal.modal = true
    },
    onBeforeTabRemove (idx) {
      return new Promise(() => {
        this.$Modal.confirm({
          title: '提示',
          content: '<p>确定要删除该船所有数据？</p>',
          loading: true,
          okText: '确定',
          cancelText: '取消',
          onOk: () => {
            if (this.tabList[idx].planList.length > 0) {
              let plan_ids = this.tabList[idx].planList.map(item => item.vmp_plan_id)
              API.delBatchVmpPlan({ vmp_plan_ids: plan_ids.join() }).then(res => {
                if (res.data.Code === 10000) {
                  this.$Message.success(res.data.Message)
                  this.$Modal.remove()
                  this.getList()
                  return true
                } else {
                  return false
                }
              })
            } else {
              this.tabList.splice(idx, 1)
              this.$Modal.remove()
            }
          }
        })
      })
    },
    selectOnChanged (e) {
      if (e.flag === 'month_start') {
        this.listQuery.plan_month = e.key
      }
    },
    // 获取当前年月
    getCurrentYearMonth() {
      const today = new Date()
      const currentDay = today.getDate()
      const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0).getDate() // 本月最后一天
      const daysLeftInMonth = lastDayOfMonth - currentDay  // 本月最后一天 - 当前日期（天）
      let currentYear, currentMonth

      if (daysLeftInMonth <= 5) { // 如果是月底 则默认是下个月 与月底差5天情况下则视为月底
        const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1)
        // const nextMonth = new Date(2024, 12, 1)
        currentYear = nextMonth.getFullYear()
        currentMonth = nextMonth.getMonth() + 1
      } else { // 如果非月底，则默认本月
        currentYear = today.getFullYear()
        currentMonth = today.getMonth() + 1
      }
      let month = currentMonth < 10 ? '0' + currentMonth : currentMonth
      return currentYear + '-' + month
    },
    // 查询
    searchResults (e) {
      this.listQuery.ship_id = e.ship_id
      this.listQuery.business_model = e.ship_type
      this.curTab = 0
      this.getList()
    },
    // 重置
    resetResults () {
      this.listQuery = {
        plan_month: this.defaultMonth,
        ship_id: '',
        business_model: ''
      }
      this.setSearchData.date.selected = this.defaultMonth
      this.setSearchData.ship_id.selected = ''
      this.setSearchData.ship_type.selected = ''
      this.curTab = 0
      this.getList()
    }
  }
})
</script>
<style scoped>
  .plan_add_btn {
    float: right;
    margin-top: 20px;
  }
</style>
